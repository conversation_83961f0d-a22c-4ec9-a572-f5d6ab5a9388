/// <reference types="vite/client" />
/// <reference types="@remix-run/node" />

interface ImportMetaEnv {
  readonly VITE_HOST_URL: string
  readonly VITE_SESSION_SECRET: string
  readonly VITE_IMAGE_EXTENSION: string
  readonly VITE_FEATURABLE_ID: string
  readonly VITE_G_PLACE_ID: string
  readonly VITE_G_API: string
  readonly VITE_STRAPI_URL: string
  readonly VITE_STRAPI_API_TOKEN: string
  readonly VITE_LONG_DO_MAP_API_TOKEN: string
  readonly VITE_SENTRY_AUTH_TOKEN: string
  readonly VITE_SENTRY_TELEMETRY: boolean
  readonly VITE_SENTRY_DNS: string
  readonly MODE: 'development' | 'production' | 'test'
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
