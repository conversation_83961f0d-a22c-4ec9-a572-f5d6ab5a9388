sonar.projectKey=prolgue-aims-clinic
sonar.organization=prolgue
sonar.scm.provider=git
sonar.javascript.lcov.reportPaths=./coverage/lcov.info

# Exclude all files in the 'tests' or 'test' directories
sonar.exclusions=**/__tests__/**, **/__mock__/**, **/*Test.java, **/*.spec.js, **/*.test.js, vitest.setup.ts


# This is the name and version displayed in the SonarCloud UI.
#sonar.projectName=AIMS CLINIC
#sonar.projectVersion=1.0


# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
#sonar.sources=.

# Encoding of the source code. Default is default system encoding
#sonar.sourceEncoding=UTF-8
