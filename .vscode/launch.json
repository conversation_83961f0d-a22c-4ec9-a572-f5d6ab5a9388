{"version": "0.2.0", "configurations": [{"type": "bun", "internalConsoleOptions": "neverOpen", "request": "launch", "name": "Debug File", "program": "${file}", "cwd": "${workspaceFolder}", "stopOnEntry": false, "watchMode": false}, {"type": "bun", "internalConsoleOptions": "neverOpen", "request": "launch", "name": "Run File", "program": "${file}", "cwd": "${workspaceFolder}", "noDebug": true, "watchMode": false}, {"type": "bun", "internalConsoleOptions": "neverOpen", "request": "attach", "name": "<PERSON><PERSON><PERSON>", "url": "ws://localhost:6499/", "stopOnEntry": false}]}