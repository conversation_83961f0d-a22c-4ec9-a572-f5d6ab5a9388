/** @type {import('@remix-run/dev').AppConfig} */
import { flatRoutes } from "remix-flat-routes"

export default {
  tailwind: true,
  postcss: true,
  ignoredRouteFiles: ["**/*"],
  routes: async (defineRoutes) => {
    return flatRoutes("routes", defineRoutes, {
      ignoredRouteFiles: [
        ".*",
        "**/*.css",
        "**/*.test.{js,jsx,ts,tsx}",
        "**/__*.*",
      ],
    })
  },
  future: {
    unstable_dev: true,
  },
  sourcemap: process.env.NODE_ENV !== ENV.PRODUCTION,
  // serverBuildTarget: "cloudflare-pages",
  // assetsBuildDirectory: "build/client",
  // publicPath: "/build/",
  // assetsBuildDirectory: "public/build",
  // serverBuildPath: "build/index.js",
}