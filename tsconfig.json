{
  "compilerOptions": {
    "lib": ["DOM", "DOM.Iterable", "ES2022", "WebWorker"],
    "types": ["@remix-run/node", "vite/client", "node", "vitest"],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "target": "ES2022",
    "strict": true,
    "allowJs": true,
    "skipLibCheck": true,
    "incremental": true,
    "composite": false,
    "declaration": false,
    "declarationMap": false,
    "forceConsistentCasingInFileNames": true,
    "inlineSources": false,
    "noImplicitAny": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "preserveWatchOutput": true,
    "strictNullChecks": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./app/*"]
    },

    // Vite takes care of building everything, not tsc.
    "noEmit": true
  },
  "include": [
    "env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    "**/.server/**/*.ts",
    "**/.server/**/*.tsx",
    "**/.client/**/*.ts",
    "**/.client/**/*.tsx",
    "app/routes/index.test..spec"
  ],
  "exclude": ["node_modules"]
}
