# Use a compatible Node.js version
FROM node:18-slim AS builder

# Set the working directory in the container
WORKDIR /app

# Install necessary build tools
RUN apt-get update && apt-get install -y build-essential python3 curl unzip

# Install Bun (lock version for stability)
RUN curl -fsSL https://bun.sh/install | bash -s
ENV PATH="/root/.bun/bin:$PATH"

# Copy the package.json for dependencies
COPY package.json ./


# Install dependencies using Bun
RUN bun install

# Copy the rest of the application code
COPY . .

# Build the application
RUN bun run build


# Use a compatible Node.js version
FROM node:18-slim

# Set the working directory in the container
WORKDIR /app

# Install necessary build tools
RUN apt-get update && apt-get install -y build-essential python3 curl unzip

COPY --from=builder /app/build ./build
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY ./instrumentation.server.mjs ./instrumentation.server.mjs
COPY ./public ./public
COPY ./.env ./.env

# Create a non-root user
RUN groupadd --system appgroup && useradd --system --group appgroup --create-home appuser
RUN chown -R appuser:appgroup /app

# Install Bun (lock version for stability)
USER appuser
RUN curl -fsSL https://bun.sh/install | bash -s
ENV PATH="/home/<USER>/.bun/bin:$PATH"

# Set environment to production
ENV NODE_ENV=production

# Expose the port the app runs on
EXPOSE 3000

# Start the application
CMD ["bun", "run", "start:prod"]
