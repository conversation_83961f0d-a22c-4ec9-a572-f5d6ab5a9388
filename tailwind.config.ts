/* eslint-disable import/no-default-export */
import type { Config } from 'tailwindcss'
import tailwindcssAnimate from 'tailwindcss-animate'
import defaultTheme from 'tailwindcss/defaultTheme'

const config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: '1.5rem',
        lg: '2.5rem',
        xl: '2.5rem',
        '2xl': '2.5rem',
      },
      screens: {
        ...defaultTheme.screens,
        '2xl': '1400px',
        '3xl': '1800px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: {
          primary: 'hsl(var(--background-primary))',
          secondary: 'hsl(var(--background-secondary))',
          'service-special': 'hsl(var(--background-service-special))',
          DEFAULT: 'hsl(var(--background))',
        },
        foreground: 'hsl(var(--foreground))',
        lemon: {
          '200': 'hsl(var(--lemon-200))',
        },
        brown: {
          '100': 'hsl(var(--brown-100))',
          '200': 'hsl(var(--brown-200))',
        },
        'blue-mist': {
          DEFAULT: 'hsl(var(--blue-mist))',
        },
        primary: {
          '100': 'hsl(var(--primary-100))',
          '200': 'hsl(var(--primary-200))',
          '300': 'hsl(var(--primary-300))',
          '400': 'hsl(var(--primary-400))',
          '500': 'hsl(var(--primary-500))',
          '600': 'hsl(var(--primary-600))',
          '700': 'hsl(var(--primary-700))',
          '800': 'hsl(var(--primary-800))',
          '900': 'hsl(var(--primary-900))',
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        pink: {
          100: '#ecd5e9',
          200: '#d8abd2',
          300: '#c582bc',
          400: '#b158a5',
          500: '#9e2e8f',
          600: '#7e2572',
          700: '#5f1c56',
          800: '#3f1239',
          900: '#20091d',
        },
        secondary: {
          '100': 'hsl(var(--secondary-100))',
          '200': 'hsl(var(--secondary-200))',
          '300': 'hsl(var(--secondary-300))',
          '400': 'hsl(var(--secondary-400))',
          '500': 'hsl(var(--secondary-500))',
          '600': 'hsl(var(--secondary-600))',
          '700': 'hsl(var(--secondary-700))',
          '800': 'hsl(var(--secondary-800))',
          '900': 'hsl(var(--secondary-900))',
          dark: 'hsl(var(--secondary-dark))',
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.3s ease-out',
        'accordion-up': 'accordion-up 0.3s ease-out',
      },
      fontFamily: {
        english: ['Century Gothic', ...defaultTheme.fontFamily.sans],
        thai: ['Sukhumvit Set', ...defaultTheme.fontFamily.sans],
        italic: ['Kanit', ...defaultTheme.fontFamily.sans],
      },
      fontSize: {
        '7xl': '4.375rem',
        '5xl': '3rem',
        lg: '1.5rem',
        md: '1.25rem',
        base: '1rem',
      },
      letterSpacing: {
        '5pt': '0.3125rem',
        '0pt': '0',
        '100pt': '6.25rem',
      },
      lineHeight: {
        '12': '3rem',
        '18': '4.5rem',
        auto: 'auto',
      },
      spacing: {
        '50': '12.5rem',
        '100': '25rem',
        '110': '27.5rem',
        '150': '37.5rem',
      },
    },
  },
  plugins: [tailwindcssAnimate],
} satisfies Config

export default config
