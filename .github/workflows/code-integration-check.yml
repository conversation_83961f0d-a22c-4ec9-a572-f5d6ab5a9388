name: Code Integration Check
on:
  push:
    branches:
      - 'main'
    paths:
      - '**'
      - '!readme'
  pull_request:
    types: [opened, synchronize, reopened]

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  install:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.2.2
        with:
          fetch-depth: 0
      - name: Set up Node.js 18
        uses: actions/setup-node@v4.1.0
        with:
          node-version: '18'
      - name: Use Bun
        uses: oven-sh/setup-bun@v2.0.1
        with:
          bun-version: 'latest'
      - name: Cache Bun Dependencies
        uses: actions/cache@v4.2.0
        with:
          path: |
            ~/.bun/install/cache/v1
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}
      - name: Install Dependencies
        run: bun install

  build-check-type-linters-formatters:
    needs: install
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.2.2
        with:
          fetch-depth: 0
      - name: Set up Node.js 18
        uses: actions/setup-node@v4.1.0
        with:
          node-version: '18'
      - name: Use Bun
        uses: oven-sh/setup-bun@v2.0.1
        with:
          bun-version: 'latest'
      - name: Cache Bun Dependencies
        uses: actions/cache@v4.2.0
        with:
          path: |
            ~/.bun/install/cache/v1
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}
      - name: Install Dependencies
        run: bun install
      - name: Install Node.js types
        run: bun add -d @types/node
      - name: Build
        run: bun run build
      - name: Run Check Type
        run: bun run tsc
      - name: Run Lint
        run: bun run lint
      - name: Run Format
        run: bun run format
