name: Deploy to DigitalOcean
on:
  workflow_dispatch:

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  PROJECT_ID: aims-clinic
  REGION: asia-southeast1
  IMAGE: asia-southeast1-docker.pkg.dev/aims-clinic/aims-clinic/aims-clinic:prod

jobs:
  build-push:
    runs-on: ubuntu-latest
    steps:
      - name: 'Checkout'
        uses: actions/checkout@v4.2.2

      - name: 'set env due to build time env.'
        run: |
          echo "VITE_RESEND_API_KEY=${{ secrets.VITE_RESEND_API_KEY }}" >> .env
          echo "RESEND_API_KEY=${{ secrets.RESEND_API_KEY }}" >> .env
          echo "VITE_FROM_EMAIL=${{ secrets.VITE_FROM_EMAIL }}" >> .env
          echo "VITE_SESSION_SECRET=${{ secrets.VITE_SESSION_SECRET }}" >> .env
          echo "VITE_LONG_DO_MAP_API_TOKEN=${{ secrets.VITE_LONG_DO_MAP_API_TOKEN }}" >> .env
          echo "VITE_IMAGE_EXTENSION=${{ secrets.VITE_IMAGE_EXTENSION }}" >> .env
          echo "VITE_HOST_URL=${{ secrets.VITE_HOST_URL }}" >> .env
          echo "VITE_STRAPI_URL=${{ secrets.VITE_STRAPI_URL }}" >> .env
          echo "VITE_STRAPI_API_TOKEN=${{ secrets.VITE_STRAPI_API_TOKEN }}" >> .env
          echo "VITE_SENTRY_AUTH_TOKEN=${{ secrets.VITE_SENTRY_AUTH_TOKEN }}" >> .env
          echo "VITE_SENTRY_TELEMETRY=${{ secrets.VITE_SENTRY_TELEMETRY }}" >> .env
          echo "VITE_SENTRY_DNS=${{ secrets.VITE_SENTRY_DNS }}" >> .env
          echo "VITE_GTM=${{ secrets.VITE_GTM }}" >> .env
          echo "VITE_GSV=${{ secrets.VITE_GSV }}" >> .env
          echo "VITE_FEATURABLE_ID=${{ secrets.VITE_FEATURABLE_ID }}" >> .env

      - id: 'auth'
        uses: 'google-github-actions/auth@v1'
        with:
          credentials_json: '${{ secrets.SERVICE_ACCOUNT_KEY }}'

      - name: 'Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v1'

      - name: 'Docker auth'
        run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      - name: Build image
        run: docker build --tag ${{ env.IMAGE }} .

      - name: Push image
        run: docker push ${{ env.IMAGE}}
