name: Sonar Cloud Code Check

on:
  workflow_run:
    workflows: ['Code Integration Check']
    types:
      - completed

jobs:
  sonarcloud:
    name: Sonar Cloud
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    permissions:
      contents: read
    env:
      VITE_SESSION_SECRET: ${{ secrets.SESSION_SECRET }}
    steps:
      - uses: actions/checkout@v4.2.2
        with:
          fetch-depth: 0

      - name: Set up Node.js 18
        uses: actions/setup-node@v4.1.0
        with:
          node-version: '18'

      - name: Use Bun
        uses: oven-sh/setup-bun@v2.0.1
        with:
          bun-version: 'latest'

      - name: <PERSON><PERSON> B<PERSON> Dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.bun/install/cache/v1
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}

      - name: Install Dependencies
        run: bun install

      - name: Run Tests with Coverage
        run: bun run test:coverage

      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@v5.0.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

      - name: 'Upload Coverage'
        uses: actions/upload-artifact@v4.4.3
        with:
          name: coverage
          path: coverage
