name: Report Coverage
on:
  workflow_run:
    workflows: ['Sonar Cloud Code Check']
    types:
      - completed

permissions:
  actions: read
  contents: read
  pull-requests: write

jobs:
  report:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    steps:
      - name: Checkout Target Repository
        uses: actions/checkout@v4
        with:
          repository: ${{ github.repository }}
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Download Coverage Artifacts
        uses: actions/download-artifact@v4.1.8
        with:
          name: coverage
          github-token: ${{ secrets.GITHUB_TOKEN }}
          run-id: ${{ github.event.workflow_run.id }}
          path: coverage

      - name: Report Coverage
        uses: davelosert/vitest-coverage-report-action@v2.8.0
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          file-coverage-mode: all
          name: Report Coverage

      - name: Delete Coverage Artifacts
        uses: geekyeggo/delete-artifact@v5
        with:
          name: coverage
