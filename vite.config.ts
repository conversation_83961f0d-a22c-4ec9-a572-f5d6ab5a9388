import { sentryVitePlugin } from '@sentry/vite-plugin'
import { vitePlugin as remix } from '@remix-run/dev'
import { installGlobals } from '@remix-run/node'
import { flatRoutes } from 'remix-flat-routes'
import { defineConfig, loadEnv } from 'vite'
import tsconfigPaths from 'vite-tsconfig-paths'
import { ENV } from './app/constants/environment'
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer-remix'

installGlobals()

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  return {
    mode: env.NODE_ENV,
    server: {
      port: 3000,
    },
    ssr: {
      noExternal: [
        'node:crypto',
        'node:fs',
        'fs',
        'path',
        'crypto',
        'react-helmet-async',
      ],
    },
    plugins: [
      remix({
        future: {
          v3_fetcherPersist: true,
          v3_relativeSplatPath: true,
          v3_throwAbortReason: true,
          v3_lazyRouteDiscovery: true,
          v3_singleFetch: true,
        },
        ignoredRouteFiles: ['**/*'],
        routes: async (defineRoutes) => {
          return flatRoutes('routes', defineRoutes, {
            ignoredRouteFiles: [
              '.*',
              '**/*.css',
              '**/*.test.{js,jsx,ts,tsx}',
              '**/__*.*',
            ],
          })
        },
      }),
      tsconfigPaths(),
      ...(env.NODE_ENV === ENV.PRODUCTION
        ? [
            sentryVitePlugin({
              org: 'prolgue',
              project: 'aims-clinic',
              authToken: env.VITE_SENTRY_AUTH_TOKEN,
              telemetry: env.VITE_SENTRY_TELEMETRY === 'true',
            }),
          ]
        : []),
      ViteImageOptimizer({
        includePublic: true,
        include: /\.(jpe?g|png|gif|svg|webp|avif)$/i,
        exclude: ['node_modules/**/*'],
        logStats: true,
        ansiColors: true,
        svg: {
          multipass: true,
          plugins: [
            {
              name: 'preset-default',
              params: {
                overrides: {
                  removeViewBox: false,
                },
              },
            },
            'sortAttrs',
            {
              name: 'addAttributesToSVGElement',
              params: {
                attributes: [{ xmlns: 'http://www.w3.org/2000/svg' }],
              },
            },
          ],
        },
        png: { quality: 65 },
        jpeg: { quality: 60, progressive: true },
        jpg: { quality: 60, progressive: true },
        webp: { quality: 60 },
        avif: { quality: 40 },
      }),
    ],
    esbuild: {
      target: 'es2022',
    },
    optimizeDeps: {
      include: ['react', 'react-dom', '@remix-run/react'],
      esbuildOptions: {
        target: 'es2022',
      },
    },
    build: {
      target: 'es2022',
      assetsInclude: ['**/*.{woff,woff2,json,png,jpg,jpeg}'],
      rollupOptions: {
        external: [
          '@next-boost/hybrid-disk-cache',
          'node:crypto',
          'node:fs',
          'fs',
          'path',
          'crypto',
        ],
      },
      minify: 'terser',
      sourcemap: env.NODE_ENV !== ENV.PRODUCTION,
    },
  }
})
