{"name": "aims-clinic", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "build:start": "rm -rf build .cache && bun run build && bun run start:prod", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "lint:fix": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint . --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "start": "NODE_ENV=development NODE_OPTIONS='--import ./instrumentation.server.mjs' remix-serve ./build/server/index.js", "start:prod": "NODE_ENV=production NODE_OPTIONS='--import ./instrumentation.server.mjs' remix-serve ./build/server/index.js", "prepare": "bunx husky install", "test": "vitest ", "test:coverage": "vitest --coverage"}, "dependencies": {"@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.1", "@next-boost/hybrid-disk-cache": "^0.3.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@remix-run/node": "^2.11.2", "@remix-run/react": "^2.11.2", "@remix-run/serve": "^2.11.2", "@remix-run/server-runtime": "^2.11.2", "@remixicon/react": "^4.3.0", "@sentry/remix": "^8.32.0", "@sentry/vite-plugin": "^2.22.6", "@sooro-io/react-gtm-module": "^3.0.1", "@types/qs": "^6.9.16", "better-sqlite3": "^11.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.3.0", "i18next": "^23.15.2", "i18next-browser-languagedetector": "^8.0.0", "i18next-fs-backend": "^2.3.2", "i18next-http-backend": "^2.6.2", "invariant": "^2.2.4", "isbot": "^4.4.0", "lodash-es": "^4.17.21", "lucide-react": "^0.293.0", "qs": "^6.13.0", "react": "18.3.1", "react-day-picker": "8.10.1", "react-dom": "18.3.1", "react-email": "^4.0.7", "react-google-reviews": "^1.7.4", "react-helmet-async": "2.0.5", "react-hook-form": "^7.53.2", "react-i18next": "^15.0.2", "react-intersection-observer": "^9.16.0", "react-social-media-embed": "^2.5.16", "remix-i18next": "^6.4.0", "remix-image": "^1.4.0", "remix-image-sharp": "^0.1.4", "remix-themes": "^1.5.1", "remix-utils": "^7.6.0", "remixicon": "^4.3.0", "sharp": "0.31.0", "svgo": "^3.3.2", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "valibot": "^1.0.0-beta.7", "vite-plugin-image-optimizer-remix": "^1.0.1", "web-vitals": "^4.2.3", "xior": "^0.6.1", "zod": "^3.23.8"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.3.1", "@remix-run/dev": "^2.14.0", "@remix-run/eslint-config": "^2.11.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.7", "@types/lodash-es": "^4.17.12", "@types/node": "^22.10.2", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@types/react-gtm-module": "^2.0.4", "@typescript-eslint/eslint-plugin": "8.4.0", "@typescript-eslint/parser": "8.4.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.1.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.30.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.2", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^8.0.0", "husky-init": "^8.0.0", "jsdom": "^25.0.1", "lint-staged": "^15.2.10", "node-gyp": "^10.2.0", "postcss": "^8.4.49", "prettier": "3.1.1", "prettier-plugin-tailwindcss": "^0.5.14", "remix-flat-routes": "^0.8.5", "tailwindcss": "^3.4.14", "terser": "^5.36.0", "tsx": "^4.19.0", "typescript": "^5.5.4", "vite": "^6.1.6", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.1"}, "resolutions": {"sharp": "^0.34.3", "@next-boost/hybrid-disk-cache": "^0.3.0"}, "engines": {"node": "18", "npm": ">=6.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}