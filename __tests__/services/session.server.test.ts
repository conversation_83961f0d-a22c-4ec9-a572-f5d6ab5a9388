import { describe, it, expect } from 'vitest'
import {
  getSession,
  commitSession,
  destroySession,
} from '@/services/session.server'
import { createCookieSessionStorage } from '@remix-run/node'
import { createThemeSessionResolver } from 'remix-themes'

describe('Session Storage Tests', () => {
  it('should create and retrieve a new session', async () => {
    const session = await getSession()
    session.set('user', 'test_user')

    expect(session.get('user')).toBe('test_user')
  })

  it('should commit a session and verify its contents', async () => {
    const session = await getSession()
    session.set('user', 'test_user')

    const cookie = await commitSession(session)

    expect(cookie).toContain('_session')
    expect(typeof cookie).toBe('string')
  })

  it('should destroy a session', async () => {
    const session = await getSession()
    session.set('user', 'test_user')

    const cookie = await destroySession(session)

    expect(cookie).toContain('_session=')
    expect(cookie).toContain('Expires=Thu, 01 Jan 1970 00:00:00 GMT')
  })
})

describe('Theme Session Resolver Tests', () => {
  const sessionStorage = createCookieSessionStorage({
    cookie: {
      name: '_theme',
      sameSite: 'lax',
      path: '/',
      httpOnly: true,
      secrets: ['test-secret'],
      secure: false,
    },
  })

  const themeSessionResolver = createThemeSessionResolver(sessionStorage)

  it('should resolve a theme from session', async () => {
    const session = await sessionStorage.getSession()
    session.set('theme', 'dark')

    const cookieHeader = await sessionStorage.commitSession(session)
    const request = new Request('http://localhost', {
      headers: { cookie: cookieHeader },
    })

    const themeSession = await themeSessionResolver(request)
    const theme = themeSession.getTheme()

    expect(theme).toBe('dark')
  })

  it('should return null if no theme is set in the session', async () => {
    const session = await sessionStorage.getSession()

    const cookieHeader = await sessionStorage.commitSession(session)
    const request = new Request('http://localhost', {
      headers: { cookie: cookieHeader },
    })

    const themeSession = await themeSessionResolver(request)
    const theme = themeSession.getTheme()

    expect(theme).toBeNull()
  })
})
