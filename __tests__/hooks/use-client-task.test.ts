import { describe, it, expect, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import useClientTasks from '@/hooks/use-client-task'

describe('useClientTasks Hook', () => {
  it('should set isClientRendered to true after rendering', async () => {
    const { result } = renderHook(() => useClientTasks(undefined))

    await act(async () => {})

    expect(result.current.isClientRendered).toBe(true)
    expect(result.current.isTaskPending).toBe(false)
  })

  it('should execute async task and update isTaskPending to false', async () => {
    const asyncTask = vi.fn(() => Promise.resolve())

    const { result } = renderHook(() => useClientTasks(asyncTask))

    expect(result.current.isTaskPending).toBe(true)

    await act(async () => {})

    expect(result.current.isTaskPending).toBe(false)
    expect(asyncTask).toHaveBeenCalledTimes(1)
  })

  it('should handle undefined asyncTask without errors', async () => {
    const { result } = renderHook(() => useClientTasks(undefined))

    await act(async () => {})

    expect(result.current.isTaskPending).toBe(false)
    expect(result.current.isClientRendered).toBe(true)
  })
})
