import { renderHook } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import usePrevious from '@/hooks/use-previous'

describe('usePrevious Hook', () => {
  it('should return undefined initially', () => {
    const { result } = renderHook(() => usePrevious(0))
    expect(result.current).toBeNull()
  })

  it('should return the previous value after an update', () => {
    const { result, rerender } = renderHook(({ value }) => usePrevious(value), {
      initialProps: { value: 0 },
    })

    expect(result.current).toBeNull()

    rerender({ value: 1 })
    expect(result.current).toBe(0)

    rerender({ value: 2 })
    expect(result.current).toBe(1)
  })

  it('should handle multiple updates correctly', () => {
    const { result, rerender } = renderHook(({ value }) => usePrevious(value), {
      initialProps: { value: 'a' },
    })

    expect(result.current).toBeNull()

    rerender({ value: 'b' })
    expect(result.current).toBe('a')

    rerender({ value: 'c' })
    expect(result.current).toBe('b')
  })
  it('should handle null and undefined values', () => {
    type TestProps = { value: number | null | undefined }

    const { result, rerender } = renderHook(
      ({ value }: TestProps) => usePrevious(value),
      {
        initialProps: { value: null } as TestProps,
      }
    )

    expect(result.current).toBeNull()

    rerender({ value: null })
    expect(result.current).toBe(null)

    rerender({ value: 42 })
    expect(result.current).toBeNull()
  })

  it('should work with complex data types like objects and arrays', () => {
    type TestProps = { value: number | null | undefined | object }

    const initialObject = { name: 'Alice' }
    const updatedObject = { name: 'Bob' }

    const { result, rerender } = renderHook(
      ({ value }: TestProps) => usePrevious(value),
      {
        initialProps: { value: initialObject } as TestProps,
      }
    )

    expect(result.current).toBeNull()

    rerender({ value: updatedObject })
    expect(result.current).toEqual(initialObject)

    const newArray = [1, 2, 3]
    rerender({ value: newArray })
    expect(result.current).toEqual(updatedObject)
  })
})
