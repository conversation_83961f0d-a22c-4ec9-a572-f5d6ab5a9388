import { describe, it, expect } from 'vitest'
import { renderHook } from '@testing-library/react'
import useClient from '@/hooks/use-client'

describe('useClient Hook', () => {
  it('should set isClient to true after mounting', () => {
    const { result } = renderHook(() => useClient())

    expect(result.current.isClient).toBe(true)
  })

  it('should return an object containing the key "isClient"', () => {
    const { result } = renderHook(() => useClient())

    expect(result.current).toHaveProperty('isClient')
  })

  it('should behave consistently on re-renders', () => {
    const { result, rerender } = renderHook(() => useClient())

    expect(result.current.isClient).toBe(true)

    rerender()

    expect(result.current.isClient).toBe(true)
  })

  it('should not update "isClient" after the first render', () => {
    const { result, rerender } = renderHook(() => useClient())

    expect(result.current.isClient).toBe(true)

    const previousValue = result.current.isClient

    rerender()
    expect(result.current.isClient).toBe(previousValue)
  })

  it('should not throw errors if unmounted immediately', () => {
    const { unmount } = renderHook(() => useClient())

    expect(() => unmount()).not.toThrow()
  })
})
