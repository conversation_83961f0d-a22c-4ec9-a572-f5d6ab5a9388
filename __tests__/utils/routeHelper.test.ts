import { describe, it, expect, vi } from 'vitest'
import { defineRoute } from '@/utils/routeHelper'
import { i18nConfig } from '@/lib/server/translations/i18n.config'

vi.mock('@/lib/server/translations/i18n.config', () => ({
  i18nConfig: {
    fallbackLng: 'eng',
  },
}))

describe('defineRoute', () => {
  it('should return correct RouteData for a simple URL', () => {
    const request = new Request('https://example.com/page?lng=fr')
    const result = defineRoute(request)

    expect(result).toEqual({
      url: new URL('https://example.com/page?lng=fr'),
      path: '/page',
      slug: 'page',
      lng: 'fr',
    })
  })

  it('should use fallbackLng if lng is not present', () => {
    const request = new Request('https://example.com/page')
    const result = defineRoute(request)

    expect(result).toEqual({
      url: new URL('https://example.com/page'),
      path: '/page',
      slug: 'page',
      lng: i18nConfig.fallbackLng,
    })
  })

  it('should handle root path correctly', () => {
    const request = new Request('https://example.com/')
    const result = defineRoute(request)

    expect(result).toEqual({
      url: new URL('https://example.com/'),
      path: '/',
      slug: undefined,
      lng: i18nConfig.fallbackLng,
    })
  })

  it('should handle paths with multiple segments', () => {
    const request = new Request('https://example.com/segment1/segment2?lng=de')
    const result = defineRoute(request)

    expect(result).toEqual({
      url: new URL('https://example.com/segment1/segment2?lng=de'),
      path: '/segment1/segment2',
      slug: 'segment2',
      lng: 'de',
    })
  })

  it('should handle URLs without query parameters', () => {
    const request = new Request('https://example.com/segment1')
    const result = defineRoute(request)

    expect(result).toEqual({
      url: new URL('https://example.com/segment1'),
      path: '/segment1',
      slug: 'segment1',
      lng: i18nConfig.fallbackLng,
    })
  })
})
