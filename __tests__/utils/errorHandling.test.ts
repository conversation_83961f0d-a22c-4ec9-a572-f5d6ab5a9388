import { describe, it, expect, vi } from 'vitest'
import { checkStatus, checkEnvVars } from '@/utils/errorHandling'

class APIResponseError extends Error {
  constructor(response) {
    super(`API Error Response: ${response.status} ${response.statusText}`)
  }
}

class MissingEnvironmentVariable extends Error {
  constructor(name: string) {
    super(
      `Missing Environment Variable: The ${name} environment variable must be defined`
    )
  }
}
describe('Utility Functions and Errors', () => {
  describe('APIResponseError', () => {
    it('should create an error with the correct message', () => {
      const response = { status: 404, statusText: 'Not Found' }
      const error = new APIResponseError(response)
      expect(error.message).toBe('API Error Response: 404 Not Found')
    })
  })

  describe('checkStatus', () => {
    it('should return the response if status is ok', () => {
      const response = { ok: true }
      expect(checkStatus(response)).toBe(response)
    })

    it('should throw APIResponseError if status is not ok', () => {
      const response = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      }
      expect(() => checkStatus(response)).toThrowError(
        'API Error Response: 500 Internal Server Error'
      )
    })
  })

  describe('MissingEnvironmentVariable', () => {
    it('should create an error with the correct message', () => {
      const name = 'VITE_STRAPI_URL'
      const error = new MissingEnvironmentVariable(name)
      expect(error.message).toBe(
        `Missing Environment Variable: The ${name} environment variable must be defined`
      )
    })
  })

  describe('checkEnvVars', () => {
    vi.stubEnv('VITE_STRAPI_URL', 'http://example.com')
    vi.stubEnv('VITE_STRAPI_API_TOKEN', 'token123')

    it('should not throw if all environment variables are defined', () => {
      expect(() => checkEnvVars()).not.toThrow()
    })

    it('should throw MissingEnvironmentVariable if an environment variable is missing (VITE_STRAPI_URL)', () => {
      vi.stubEnv('VITE_STRAPI_URL', undefined)
      vi.stubEnv('VITE_STRAPI_API_TOKEN', 'token123')

      expect(() => checkEnvVars()).toThrowError(
        'Missing Environment Variable: The VITE_STRAPI_URL environment variable must be defined'
      )
    })

    it('should throw MissingEnvironmentVariable if an environment variable is missing (VITE_STRAPI_API_TOKEN)', () => {
      vi.stubEnv('VITE_STRAPI_URL', 'http://example.com')
      vi.stubEnv('VITE_STRAPI_API_TOKEN', undefined)

      expect(() => checkEnvVars()).toThrowError(
        'Missing Environment Variable: The VITE_STRAPI_API_TOKEN environment variable must be defined'
      )
    })
  })
})
