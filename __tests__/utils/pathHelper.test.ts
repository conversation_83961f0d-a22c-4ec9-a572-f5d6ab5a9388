import { describe, it, expect } from 'vitest'
import { removeTrailingSlash } from '@/utils/pathHelper'

describe('removeTrailingSlash', () => {
  it('should remove trailing slash from a path', () => {
    expect(removeTrailingSlash('/example/')).toBe('/example')
  })

  it('should not modify a path without a trailing slash', () => {
    expect(removeTrailingSlash('/example')).toBe('/example')
  })

  it('should not remove the single root slash', () => {
    expect(removeTrailingSlash('/')).toBe('/')
  })

  it('should handle empty strings gracefully', () => {
    expect(removeTrailingSlash('')).toBe('')
  })

  it('should handle paths with multiple trailing slashes', () => {
    expect(removeTrailingSlash('/example///')).toBe('/example//')
  })
})
