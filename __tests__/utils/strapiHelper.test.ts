import { describe, it, expect } from 'vitest'
import { warperSingleType, warperCollectionType } from '@/utils/strapiHelper'
import { StrapiCollectionType, StrapiSingleType } from '@/lib/api/types'

describe('warperSingleType', () => {
  it('should return attributes if data exists', () => {
    const response = {
      data: {
        attributes: { key: 'value' },
      },
    } as StrapiSingleType<object>
    const result = warperSingleType(response)
    expect(result).toEqual({ key: 'value' })
  })

  it('should return null if data is missing', () => {
    const response = {} as StrapiSingleType<object>
    const result = warperSingleType(response)
    expect(result).toBeNull()
  })

  it('should return null if attributes are missing', () => {
    const response = { data: {} } as StrapiSingleType<object>
    const result = warperSingleType(response)
    expect(result).toBeNull()
  })
})

describe('warperCollectionType', () => {
  it('should return an array of items with id and attributes if data exists', () => {
    const response = {
      data: [
        { id: 1, attributes: { key: 'value1' } },
        { id: 2, attributes: { key: 'value2' } },
      ],
    }
    const result = warperCollectionType(response)
    expect(result).toEqual([
      { id: '1', key: 'value1' },
      { id: '2', key: 'value2' },
    ])
  })

  it('should return null if data is missing', () => {
    const response = {} as StrapiCollectionType<object>
    const result = warperCollectionType(response)
    expect(result).toBeNull()
  })

  it('should handle empty data arrays gracefully', () => {
    const response = { data: [] } as StrapiCollectionType<object>
    const result = warperCollectionType(response)
    expect(result).toEqual([])
  })

  it('should handle items without attributes', () => {
    const response = {
      data: [
        { id: 1, attributes: null },
        { id: 2, attributes: { key: 'value2' } },
      ],
    } as StrapiCollectionType<object>
    const result = warperCollectionType(response)
    expect(result).toEqual([{}, { id: '2', key: 'value2' }])
  })
})
