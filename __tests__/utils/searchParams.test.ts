import { describe, it, expect } from 'vitest'
import { setSearchParams } from '@/utils/searchParams'

describe('setSearchParams', () => {
  it('should add a new query parameter', () => {
    const result = setSearchParams('', 'key', 'value')
    expect(result).toBe('key=value')
  })

  it('should update an existing query parameter', () => {
    const result = setSearchParams('key=oldValue', 'key', 'newValue')
    expect(result).toBe('key=newValue')
  })

  it('should remove a query parameter if the value is empty', () => {
    const result = setSearchParams('key=value', 'key', '')
    expect(result).toBe('')
  })

  it('should retain other query parameters when adding a new one', () => {
    const result = setSearchParams(
      'existingKey=existingValue',
      'newKey',
      'newValue'
    )
    expect(result).toBe('existingKey=existingValue&newKey=newValue')
  })

  it('should retain other query parameters when deleting a specific one', () => {
    const result = setSearchParams(
      'key=value&anotherKey=anotherValue',
      'key',
      ''
    )
    expect(result).toBe('anotherKey=anotherValue')
  })

  it('should handle an empty current string when removing a parameter', () => {
    const result = setSearchParams('', 'key', '')
    expect(result).toBe('')
  })

  it('should overwrite one of many query parameters', () => {
    const result = setSearchParams('foo=1&bar=2&baz=3', 'bar', 'updated')
    expect(result).toBe('foo=1&bar=updated&baz=3')
  })

  it('should do nothing when trying to delete a non-existent key', () => {
    const result = setSearchParams('foo=1&bar=2', 'nonExistent', '')
    expect(result).toBe('foo=1&bar=2')
  })
})
