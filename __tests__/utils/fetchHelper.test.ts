import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  queryStringBuilder,
  fetchPageBySlug,
  fetchServiceBySlug,
  fetchArticleBySlug,
  fetchDynamicSlugs,
} from '@/utils/fetchHelper'
import { STRAPI_ARTICLE, STRAPI_SERVICES } from '@/constants/http'
import { strapiGetPage } from '@/lib/api/strapi/page'
import { strapiGetServices } from '@/lib/api/strapi/service'
import { strapiGetArticles } from '@/lib/api/strapi/article'
import { ERROR_CODE_404 } from '@/constants/error'

vi.mock('@/utils/errorHandling', () => ({
  checkEnvVars: vi.fn(),
}))

vi.mock('@/lib/api/strapi/page', () => ({
  strapiGetPage: vi.fn(),
}))

vi.mock('@/lib/api/strapi/service', () => ({
  strapiGetServices: vi.fn(),
}))

vi.mock('@/lib/api/strapi/article', () => ({
  strapiGetArticles: vi.fn(),
}))

describe('queryStringBuilder', () => {
  it('should build a query string with all parameters', () => {
    const result = queryStringBuilder(
      ['field1', 'field2'],
      { key: 'value' },
      ['sortField'],
      {
        page: 1,
        pageSize: 10,
        withCount: false,
        start: 0,
        limit: 0,
      }
    )
    expect(result).toBe(
      'pagination[page]=1&pagination[pageSize]=10&pagination[withCount]=false&pagination[start]=0&pagination[limit]=0&populate[0]=field1&populate[1]=field2&filters[key]=value&sort[0]=sortField'
    )
  })

  it('should handle missing parameters gracefully', () => {
    const result = queryStringBuilder(['field1'])
    expect(result).toBe('populate[0]=field1')
  })

  it('should return an empty string if no parameters are provided', () => {
    const result = queryStringBuilder([])
    expect(result).toBe('')
  })
})

describe('fetchPageBySlug', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return attributes on a successful fetch', async () => {
    vi.mocked(strapiGetPage).mockResolvedValue({
      data: [
        {
          attributes: {
            id: 0,
            backgroundImage: null,
            images: null,
          },
          id: 0,
        },
      ],
    })

    const result = await fetchPageBySlug('test-slug', 'eng')
    expect(result).toEqual({ id: 0, backgroundImage: null, images: null })
  })

  it('should throw a 404 error if data is missing', async () => {
    vi.mocked(strapiGetPage).mockResolvedValue({ data: undefined })

    await expect(fetchPageBySlug('test-slug', 'eng')).rejects.toMatchObject({
      status: ERROR_CODE_404,
    })
  })
})

describe('fetchServiceBySlug', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return attributes on a successful fetch', async () => {
    vi.mocked(strapiGetServices).mockResolvedValue({
      data: [
        {
          attributes: {
            tag: null,
            questionAnswer: null,
          },
          id: 0,
        },
      ],
    })

    const result = await fetchServiceBySlug('test-slug', 'eng')
    expect(result).toEqual({ tag: null, questionAnswer: null })
  })

  it('should throw a 404 error if data is missing', async () => {
    vi.mocked(strapiGetServices).mockResolvedValue({ data: undefined })

    await expect(fetchServiceBySlug('test-slug', 'eng')).rejects.toMatchObject({
      status: ERROR_CODE_404,
    })
  })
})

describe('fetchArticleBySlug', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return attributes on a successful fetch', async () => {
    vi.mocked(strapiGetArticles).mockResolvedValue({
      data: [
        {
          attributes: {
            id: 0,
            title: '',
            content: null,
            slug: '',
          },
          id: 0,
        },
      ],
    })

    const result = await fetchArticleBySlug('test-slug', 'eng')
    expect(result).toEqual({ id: 0, title: '', content: null, slug: '' })
  })

  it('should throw a 404 error if data is missing', async () => {
    vi.mocked(strapiGetArticles).mockResolvedValue({ data: undefined })

    await expect(fetchArticleBySlug('test-slug', 'eng')).rejects.toMatchObject({
      status: ERROR_CODE_404,
    })
  })
})

describe('fetchDynamicSlugs', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return an array of slugs on a successful fetch (STRAPI_ARTICLE)', async () => {
    vi.mocked(strapiGetArticles).mockResolvedValue({
      data: [
        {
          attributes: {
            slug: 'slug1',
            id: 0,
            title: '',
            content: null,
          },
          id: 0,
        },
        {
          attributes: {
            slug: 'slug2',
            id: 0,
            title: '',
            content: null,
          },
          id: 0,
        },
      ],
    })

    const result = await fetchDynamicSlugs(STRAPI_ARTICLE, 'eng')
    expect(result).toEqual(['slug1', 'slug2'])
  })

  it('should return an array of slugs on a successful fetch (STRAPI_SERVICES)', async () => {
    vi.mocked(strapiGetServices).mockResolvedValue({
      data: [
        {
          attributes: {
            slug: 'slug1',
            tag: null,
            questionAnswer: null,
          },
          id: 0,
        },
        {
          attributes: {
            slug: 'slug2',
            tag: null,
            questionAnswer: null,
          },
          id: 0,
        },
      ],
    })

    const result = await fetchDynamicSlugs(STRAPI_SERVICES, 'eng')
    expect(result).toEqual(['slug1', 'slug2'])
  })

  it('should throw a 404 error if no data is returned', async () => {
    vi.mocked(strapiGetArticles).mockResolvedValue({ data: undefined })

    await expect(
      fetchDynamicSlugs(STRAPI_ARTICLE, 'eng')
    ).rejects.toMatchObject({
      status: ERROR_CODE_404,
    })
  })
})
