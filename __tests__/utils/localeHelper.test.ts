import { describe, it, expect } from 'vitest'
import { switchLanguage, getLocaleParam } from '@/utils/localeHelper'
import { LANGUAGE } from '@/constants/language'

describe('Language Utility Functions', () => {
  describe('switchLanguage', () => {
    it('should return "?lng=tha" when locale is LANGUAGE.THA', () => {
      const result = switchLanguage(LANGUAGE.THA)
      expect(result).toBe('?lng=eng')
    })
  })

  describe('getLocaleParam', () => {
    it('should return "?lng=tha" when locale is LANGUAGE.ENG', () => {
      const result = getLocaleParam(LANGUAGE.ENG)
      expect(result).toBe('?lng=eng')
    })

    it('should return an empty string when locale is not LANGUAGE.THA', () => {
      const result = getLocaleParam(LANGUAGE.THA)
      expect(result).toBe('')
    })
  })
})
