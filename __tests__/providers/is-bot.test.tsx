import { describe, it, expect, afterEach } from 'vitest'
import { render, screen, cleanup } from '@testing-library/react'
import { useIsBot, IsBotProvider } from '@/providers/is-bot'

const TestComponent = () => {
  const isBot = useIsBot()
  return <div data-testid="is-bot">{isBot ? 'Bot' : 'Not a Bot'}</div>
}

describe('IsBotProvider and useIsBot Hook', () => {
  afterEach(() => cleanup())

  it('should return false when no provider is used', () => {
    render(<TestComponent />)

    expect(screen.getByTestId('is-bot').textContent).toBe('Not a Bot')
  })

  it('should return true when isBot is set to true via the provider', () => {
    render(
      <IsBotProvider isBot={true}>
        <TestComponent />
      </IsBotProvider>
    )

    expect(screen.getByTestId('is-bot').textContent).toBe('Bot')
  })

  it('should return false when isBot is set to false via the provider', () => {
    render(
      <IsBotProvider isBot={false}>
        <TestComponent />
      </IsBotProvider>
    )

    expect(screen.getByTestId('is-bot').textContent).toBe('Not a Bot')
  })

  it('should handle edge cases like null or undefined values gracefully', () => {
    render(
      <IsBotProvider isBot={null as unknown as boolean}>
        <TestComponent />
      </IsBotProvider>
    )

    expect(screen.getByTestId('is-bot').textContent).toBe('Not a Bot')

    cleanup()

    render(
      <IsBotProvider isBot={undefined as unknown as boolean}>
        <TestComponent />
      </IsBotProvider>
    )

    expect(screen.getByTestId('is-bot').textContent).toBe('Not a Bot')
  })
})
