import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { useContext } from 'react'
import { AppContext } from '@/providers/appContext'
import { MainMenuEntity } from '@/lib/api/entity'

const TestComponent = () => {
  const { mainMenu } = useContext(AppContext)

  const menuItems = mainMenu?.mainMenuItems || []

  if (menuItems.length === 0) {
    return <div data-testid="main-menu">No menu available</div>
  }

  return (
    <div data-testid="main-menu">
      {menuItems.map((item) => (
        <div key={item.id} data-testid={`menu-item-${item.id}`}>
          {item.name ? `Menu: ${item.name}` : 'Unnamed Menu'}
        </div>
      ))}
    </div>
  )
}

describe('AppContext with MainMenuEntity', () => {
  it('should display "No menu available" when mainMenu is null', () => {
    render(<TestComponent />)

    expect(screen.getByTestId('main-menu').textContent).toBe(
      'No menu available'
    )
  })

  it('should display menu items when mainMenuItems are provided', () => {
    const customMainMenu = {
      id: 1,
      mainMenuItems: [
        { id: 101, name: 'Home', url: '/home', __component: null },
        { id: 102, name: 'About', url: '/about', __component: null },
      ],
    }

    render(
      <AppContext.Provider value={{ mainMenu: customMainMenu, home: null }}>
        <TestComponent />
      </AppContext.Provider>
    )

    expect(screen.getByTestId('menu-item-101').textContent).toBe('Menu: Home')
    expect(screen.getByTestId('menu-item-102').textContent).toBe('Menu: About')
  })

  it('should handle undefined mainMenuItems gracefully', () => {
    const mockMainMenu: MainMenuEntity = {
      mainMenuItems: undefined,
    }

    render(
      <AppContext.Provider value={{ mainMenu: mockMainMenu, home: null }}>
        <TestComponent />
      </AppContext.Provider>
    )

    expect(screen.getByTestId('main-menu').textContent).toBe(
      'No menu available'
    )
  })

  it('should handle empty mainMenuItems array', () => {
    const mockMainMenu: MainMenuEntity = {
      mainMenuItems: [],
    }

    render(
      <AppContext.Provider value={{ mainMenu: mockMainMenu, home: null }}>
        <TestComponent />
      </AppContext.Provider>
    )

    expect(screen.getByTestId('main-menu').textContent).toBe(
      'No menu available'
    )
  })

  it('should display "Unnamed Menu" when a menu item has no name', () => {
    const customMainMenu = {
      id: 3,
      mainMenuItems: [
        { id: 201, name: null, url: '/unknown', __component: null },
      ],
    }

    render(
      <AppContext.Provider value={{ mainMenu: customMainMenu, home: null }}>
        <TestComponent />
      </AppContext.Provider>
    )

    expect(screen.getByTestId('menu-item-201').textContent).toBe('Unnamed Menu')
  })
})
