import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { AimsLogoText } from '@/lib/assets/aims-logo-text'

describe('AimsLogoText Component', () => {
  it('renders the SVG element with default props', () => {
    render(<AimsLogoText />)
    const svgElement = screen.getByRole('img', { hidden: true })
    expect(svgElement).toBeInTheDocument()
    expect(svgElement).toHaveAttribute('viewBox', '0 0 728.31932 522.62282')
    expect(svgElement).toHaveClass('aims-logo-text')
  })

  it('applies additional className when passed as a prop', () => {
    render(<AimsLogoText className="custom-class" />)
    const svgElement = screen.getByRole('img', { hidden: true })
    expect(svgElement).toHaveClass('aims-logo-text custom-class')
  })

  it('renders the text content correctly', () => {
    render(<AimsLogoText />)
    const textElement = screen.getByText('Aims for your perfection')
    expect(textElement).toBeInTheDocument()
  })
})
