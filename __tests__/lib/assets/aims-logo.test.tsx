import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { AimsLogo } from '@/lib/assets/aims-logo'

describe('AimsLogo Component', () => {
  it('renders the SVG element with default props', () => {
    render(<AimsLogo />)
    const svgElement = screen.getByRole('img', { hidden: true })
    expect(svgElement).toBeInTheDocument()
    expect(svgElement).toHaveAttribute('viewBox', '0 0 1024 450')
    expect(svgElement).toHaveClass('aims-logo')
  })

  it('applies additional className when passed as a prop', () => {
    render(<AimsLogo className="custom-class" />)
    const svgElement = screen.getByRole('img', { hidden: true })
    expect(svgElement).toHaveClass('aims-logo custom-class')
  })
})
