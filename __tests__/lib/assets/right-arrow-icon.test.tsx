import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { RightArrowIcon } from '@/lib/assets/right-arrow-icon'

describe('RightArrowIcon Component', () => {
  it('renders the SVG element with default attributes', () => {
    render(<RightArrowIcon />)
    const svgElement = screen.getByRole('img', { hidden: true })
    expect(svgElement).toBeInTheDocument()
    expect(svgElement).toHaveAttribute('viewBox', '0 0 330 330')
    expect(svgElement).toHaveAttribute('width', '800px')
    expect(svgElement).toHaveAttribute('height', '800px')
  })

  it('applies additional className when passed as a prop', () => {
    render(<RightArrowIcon className="custom-class" />)
    const svgElement = screen.getByRole('img', { hidden: true })
    expect(svgElement).toHaveClass('right-arrow-icon custom-class')
  })

  it('spreads additional props to the SVG element', () => {
    render(
      <RightArrowIcon data-testid="right-arrow" aria-label="Right Arrow Icon" />
    )
    const svgElement = screen.getByTestId('right-arrow')
    expect(svgElement).toHaveAttribute('aria-label', 'Right Arrow Icon')
  })

  it('applies custom width and height', () => {
    render(<RightArrowIcon width="100px" height="100px" />)
    const svgElement = screen.getByRole('img', { hidden: true })
    expect(svgElement).toHaveAttribute('width', '100px')
    expect(svgElement).toHaveAttribute('height', '100px')
  })

  it('renders the correct path inside the SVG', () => {
    render(<RightArrowIcon />)
    const pathElement = screen
      .getByRole('img', { hidden: true })
      .querySelector('path')
    expect(pathElement).toBeInTheDocument()
    expect(pathElement).toHaveAttribute(
      'd',
      'M165,0C74.019,0,0,74.019,0,165s74.019,165,165,165s165-74.019,165-165S255.981,0,165,0z M225.606,175.605 l-80,80.002C142.678,258.535,138.839,260,135,260s-7.678-1.464-10.606-4.394c-5.858-5.857-5.858-15.355,0-21.213l69.393-69.396 l-69.393-69.392c-5.858-5.857-5.858-15.355,0-21.213c5.857-5.858,15.355-5.858,21.213,0l80,79.998 c2.814,2.813,4.394,6.628,4.394,10.606C230,168.976,228.42,172.792,225.606,175.605z'
    )
  })
})
