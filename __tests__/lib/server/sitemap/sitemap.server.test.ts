import { generateSitemap } from '@/lib/server/sitemap/sitemap.server'
import { mockOptions, mockRequest, mockRoutes } from '__mocks__/sitemap'
import { vi, it, expect, describe } from 'vitest'

describe('generateSitemap', () => {
  it('should generate a sitemap with correct XML response', async () => {
    vi.mock('@/lib/server/sitemap/sitemap-utils.server', () => ({
      SitemapRoute: vi.fn().mockResolvedValue(`
        <?xml version="1.0" encoding="UTF-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
          <url>
            <loc>https://example.com/</loc>
            <priority>0.7</priority>
          </url>
        </urlset>
      `),
    }))

    const response = await generateSitemap(mockRequest, mockRoutes, mockOptions)

    expect(response).toBeInstanceOf(Response)

    expect(response.headers.get('Content-Type')).toBe('application/xml')

    const body = await response.text()

    const bytes = new TextEncoder().encode(body).byteLength
    expect(response.headers.get('Content-Length')).toBe(String(bytes))

    expect(body).toContain('<loc>https://example.com/</loc>')
    expect(body).toContain('</urlset>')
  })
})
