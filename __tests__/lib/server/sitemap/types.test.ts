import { SEOHandle, SEOOptions, SitemapEntry } from '@/lib/server/sitemap/types'
import { describe, it, expect } from 'vitest'

describe('SitemapEntry', () => {
  it('should correctly define a valid SitemapEntry object', () => {
    const entry: SitemapEntry = {
      route: '/home',
      lastmod: '2023-12-19',
      changefreq: 'daily',
      priority: 0.8,
    }

    expect(entry.route).toBe('/home')
    expect(entry.lastmod).toBe('2023-12-19')
    expect(entry.changefreq).toBe('daily')
    expect(entry.priority).toBe(0.8)
  })

  it('should allow partial SitemapEntry objects', () => {
    const entry: SitemapEntry = {
      route: '/about',
    }

    expect(entry.route).toBe('/about')
    expect(entry.lastmod).toBeUndefined()
    expect(entry.changefreq).toBeUndefined()
    expect(entry.priority).toBeUndefined()
  })

  it('should enforce valid changefreq values', () => {
    const validChangefreq: SitemapEntry['changefreq'] = 'monthly'
    expect(validChangefreq).toBe('monthly')
  })

  it('should enforce valid priority values', () => {
    const validPriority: SitemapEntry['priority'] = 0.5
    expect(validPriority).toBe(0.5)
  })
})

describe('SEOHandle', () => {
  it('should allow defining a getSitemapEntries function', async () => {
    const handle: SEOHandle = {
      getSitemapEntries: async () => [
        { route: '/contact', lastmod: '2023-12-19', changefreq: 'weekly' },
        { route: '/services', priority: 0.7 },
      ],
    }

    const mockRequest = new Request('https://example.com')
    const entries = await handle.getSitemapEntries?.(mockRequest)

    expect(entries).toBeDefined()
    expect(entries?.length).toBe(2)
    expect(entries).toContainEqual({
      route: '/contact',
      lastmod: '2023-12-19',
      changefreq: 'weekly',
    })
    expect(entries).toContainEqual({
      route: '/services',
      priority: 0.7,
    })
  })

  it('should return null if no getSitemapEntries is defined', async () => {
    const handle: SEOHandle = {}
    const mockRequest = new Request('https://example.com')
    const entries = await handle.getSitemapEntries?.(mockRequest)

    expect(entries).toBeUndefined()
  })
})

describe('SEOOptions', () => {
  it('should define valid SEO options', () => {
    const options: SEOOptions = {
      siteUrl: 'https://example.com',
      articlesSlugs: ['/blog/post-1', '/blog/post-2'],
      servicesSlugs: ['/service-1', '/service-2'],
      headers: {
        'Content-Type': 'application/json',
      },
    }

    expect(options.siteUrl).toBe('https://example.com')
    expect(options.articlesSlugs).toEqual(['/blog/post-1', '/blog/post-2'])
    expect(options.servicesSlugs).toEqual(['/service-1', '/service-2'])
    expect(options.headers).toBeDefined()
    expect(options.headers?.['Content-Type']).toBe('application/json')
  })

  it('should allow optional headers', () => {
    const options: SEOOptions = {
      siteUrl: 'https://example.com',
      articlesSlugs: ['/blog/post-1'],
      servicesSlugs: ['/service-1'],
    }

    expect(options.headers).toBeUndefined()
  })
})
