import { describe, it, expect, vi } from 'vitest'
import { SitemapRoute } from '@/lib/server/sitemap/sitemap-utils.server'
import { mockRequest, mockRoutes, mockOptions } from '__mocks__/sitemap'
import { ServerBuild } from '@remix-run/node'

describe('SitemapRoute', () => {
  it('should generate sitemap XML for static routes', async () => {
    const result = await SitemapRoute(mockRequest, mockRoutes, mockOptions)

    expect(result).toContain('<loc>https://example.com/</loc>')
    expect(result).toContain('</urlset>')
  })

  it('should generate sitemap XML for dynamic routes', async () => {
    const result = await SitemapRoute(mockRequest, mockRoutes, mockOptions)

    expect(result).toContain(
      '<loc>https://example.com//article/article-1</loc>'
    )
    expect(result).toContain(
      '<loc>https://example.com//article/article-2</loc>'
    )
    expect(result).toContain(
      '<loc>https://example.com//our-service/service-1</loc>'
    )
    expect(result).toContain(
      '<loc>https://example.com//our-service/service-2</loc>'
    )
    expect(result).toContain('</urlset>')
    expect(result).toContain('<priority>0.7</priority>')
  })

  it('should handle duplicate routes gracefully', async () => {
    const consoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {})

    await SitemapRoute(mockRequest, mockRoutes, mockOptions)

    expect(consoleWarn).not.toHaveBeenCalled()
    consoleWarn.mockRestore()
  })

  it('should correctly format the XML output', async () => {
    const result = await SitemapRoute(mockRequest, mockRoutes, mockOptions)

    expect(result).toMatch(/^<\?xml version="1\.0" encoding="UTF-8"\?>/)
    expect(result).toContain(
      `<urlset
      xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"`
    )
  })

  it('should handle routes with missing slugs', async () => {
    const mockRoutesIn: ServerBuild['routes'] = {
      root: {
        path: '/',
        module: {
          default: undefined,
        },
        parentId: undefined,
        id: '',
      },
      test: {
        index: true,
        path: 'test/',
        module: {
          default: undefined,
        },
        parentId: undefined,
        id: '',
      },
      services: {
        path: 'services/:slug',
        module: {
          default: undefined,
        },
        parentId: 'root',
        id: '',
      },
    }

    const mockOptionsIn = {
      siteUrl: 'https://example.com',
      articlesSlugs: [],
      servicesSlugs: [],
    }

    const result = await SitemapRoute(mockRequest, mockRoutesIn, mockOptionsIn)

    expect(result).not.toContain('<loc>https://example.com/services/')
    expect(result).toContain('</urlset>')
  })
})
