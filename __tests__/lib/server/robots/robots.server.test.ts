import { describe, it, expect } from 'vitest'
import { generateRobotsTxt } from '@/lib/server/robots/robots.server'
import { RobotsPolicy } from '@/lib/server/robots/types'

describe('generateRobotsTxt', () => {
  it('should generate a valid robots.txt with default policies', async () => {
    const response = await generateRobotsTxt()
    const text = await response.text()

    expect(text).toBe(`User-agent: *\nAllow: /\n`)
    expect(response.headers.get('Content-Type')).toBe('text/plain')
    expect(response.headers.get('Content-Length')).toBe(String(text.length))
  })

  it('should append custom policies to the default policies', async () => {
    const customPolicies: RobotsPolicy[] = [
      { type: 'disallow', value: '/admin' },
      { type: 'sitemap', value: '/sitemap.xml' },
    ]

    const response = await generateRobotsTxt(customPolicies)
    const text = await response.text()

    expect(text).toBe(
      `User-agent: *\nAllow: /\nDisallow: /admin\nSitemap: /sitemap.xml\n`
    )
  })

  it('should replace default policies with custom policies when appendOnDefaultPolicies is false', async () => {
    const customPolicies: RobotsPolicy[] = [
      { type: 'userAgent', value: 'Googlebot' },
      { type: 'disallow', value: '/private' },
    ]

    const response = await generateRobotsTxt(customPolicies, {
      appendOnDefaultPolicies: false,
    })
    const text = await response.text()

    expect(text).toBe(`User-agent: Googlebot\nDisallow: /private\n`)
  })

  it('should include custom headers in the response', async () => {
    const customHeaders = {
      'X-Custom-Header': 'CustomValue',
    }

    const response = await generateRobotsTxt([], { headers: customHeaders })

    expect(response.headers.get('X-Custom-Header')).toBe('CustomValue')
  })

  it('should handle an empty policies array', async () => {
    const response = await generateRobotsTxt([], {
      appendOnDefaultPolicies: false,
    })
    const text = await response.text()

    expect(text).toBe('')
  })

  it('should correctly encode the response content length', async () => {
    const response = await generateRobotsTxt()
    const robotText = await response.text()
    const bytes = new TextEncoder().encode(robotText).byteLength

    expect(response.headers.get('Content-Length')).toBe(String(bytes))
  })
})
