import { describe, it, expect } from 'vitest'
import type { RobotsPolicy, RobotsConfig } from '@/lib/server/robots/types'
import { createRobotsConfig, validateRobotsPolicy } from '__mocks__/robots'

describe('RobotsPolicy and RobotsConfig', () => {
  it('should validate a correct RobotsPolicy', () => {
    const policy: RobotsPolicy = { type: 'allow', value: '/home' }
    expect(validateRobotsPolicy(policy)).toBe(true)
  })

  it('should invalidate an incorrect RobotsPolicy', () => {
    const invalidPolicy: RobotsPolicy = {
      type: 'invalidType' as never,
      value: '/home',
    }
    expect(validateRobotsPolicy(invalidPolicy)).toBe(false)
  })

  it('should invalidate a RobotsPolicy with missing value', () => {
    const invalidPolicy = { type: 'allow' } as unknown as RobotsPolicy
    expect(validateRobotsPolicy(invalidPolicy)).toBe(false)
  })

  it('should create RobotsConfig with default values', () => {
    const config: RobotsConfig = createRobotsConfig({})
    expect(config.appendOnDefaultPolicies).toBe(true)
    expect(config.headers).toEqual({})
  })

  it('should create RobotsConfig with provided values', () => {
    const config: RobotsConfig = createRobotsConfig({
      appendOnDefaultPolicies: false,
      headers: { 'Content-Type': 'text/plain' },
    })
    expect(config.appendOnDefaultPolicies).toBe(false)
    expect(config.headers).toEqual({ 'Content-Type': 'text/plain' })
  })

  it('should handle missing optional fields in RobotsConfig', () => {
    const config: RobotsConfig = createRobotsConfig({
      headers: { 'Content-Type': 'application/json' },
    })
    expect(config.appendOnDefaultPolicies).toBe(true)
    expect(config.headers).toEqual({ 'Content-Type': 'application/json' })
  })
})
