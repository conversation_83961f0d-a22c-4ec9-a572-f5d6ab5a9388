import { describe, it, expect } from 'vitest'
import { getRobotsText } from '@/lib/server/robots/robots-utils'
import { RobotsPolicy } from '@/lib/server/robots/types'

describe('getRobotsText', () => {
  it('should generate robots.txt with multiple policies', () => {
    const policies: RobotsPolicy[] = [
      { type: 'userAgent', value: '*' },
      { type: 'allow', value: '/' },
      { type: 'disallow', value: '/admin' },
      { type: 'sitemap', value: '/sitemap.xml' },
      { type: 'crawlDelay', value: '10' },
    ]

    const result = getRobotsText(policies)

    expect(result).toBe(
      `User-agent: *\nAllow: /\nDisallow: /admin\nSitemap: /sitemap.xml\nCrawl-delay: 10\n`
    )
  })

  it('should handle an empty policies array', () => {
    const policies: RobotsPolicy[] = []

    const result = getRobotsText(policies)

    expect(result).toBe('')
  })

  it('should generate robots.txt for a single policy', () => {
    const policies: RobotsPolicy[] = [{ type: 'userAgent', value: '*' }]

    const result = getRobotsText(policies)

    expect(result).toBe(`User-agent: *\n`)
  })

  it('should ignore unknown policy types', () => {
    const policies: RobotsPolicy[] = [
      { type: 'userAgent', value: '*' },
      // @ts-expect-error: Intentionally testing unknown type
      { type: 'unknownType', value: '/unknown' },
    ]

    const result = getRobotsText(policies)

    expect(result).toBe(`User-agent: *\n`)
  })

  it('should handle policies with empty values', () => {
    const policies: RobotsPolicy[] = [
      { type: 'userAgent', value: '*' },
      { type: 'allow', value: '' },
    ]

    const result = getRobotsText(policies)

    expect(result).toBe(`User-agent: *\nAllow: \n`)
  })
})
