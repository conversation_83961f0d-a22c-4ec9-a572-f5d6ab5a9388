/* eslint-disable @typescript-eslint/no-explicit-any */
import { vi, describe, it, expect } from 'vitest'
import { CSRFError } from 'remix-utils/csrf/server'
import { validateCsrfToken, csrf } from '@/lib/server/csrf.server'

vi.mock('@remix-run/node', () => ({
  createCookie: vi.fn().mockReturnValue({
    name: 'csrf',
    path: '/',
    httpOnly: true,
    secure: true,
    sameSite: 'lax',
    secrets: ['mock-secret'],
  }),
}))

vi.mock('remix-utils/csrf/server', async () => {
  const actual = await import('remix-utils/csrf/server') // Import the original module

  return {
    ...actual,
    CSRF: vi.fn().mockImplementation(() => ({
      cookie: {},
      formDataKey: 'csrf',
      generate: vi.fn().mockReturnValue('generated-token'),
      getToken: vi.fn().mockReturnValue('mock-token'),
      validate: vi.fn(),
    })),
    CSRFError: actual.CSRFError,
  }
})

describe('CSRF token validation', () => {
  it('should pass if the CSRF token is valid', async () => {
    const mockRequest = new Request('https://example.com', {
      method: 'POST',
      headers: { csrf: 'mock-csrf-token' },
    })

    await expect(validateCsrfToken(mockRequest)).resolves.not.toThrow()

    expect(csrf.validate).toHaveBeenCalled()
  })

  it('should throw a 403 error if the CSRF token is invalid', async () => {
    const mockRequest = new Request('https://example.com', {
      method: 'POST',
      headers: { csrf: 'invalid-csrf-token' },
    })

    const csrfError = new CSRFError(
      'invalid_token_in_cookie',
      'Invalid CSRF token'
    )

    ;(csrf.validate as any).mockRejectedValue(csrfError)

    const response = await validateCsrfToken(mockRequest).catch((e) => e)

    expect(response.status).toBe(403)

    const responseBody = await response.text()
    expect(responseBody).toBe('Invalid CSRF token')
  })

  it('should throw an error if CSRF validation fails for reasons other than CSRFError', async () => {
    const mockRequest = new Request('https://example.com', {
      method: 'POST',
      headers: { csrf: 'any-token' },
    })

    const genericError = new Error('Some other error')
    ;(csrf.validate as any).mockRejectedValue(genericError)

    await expect(validateCsrfToken(mockRequest)).rejects.toThrowError(
      'Some other error'
    )
  })
})
