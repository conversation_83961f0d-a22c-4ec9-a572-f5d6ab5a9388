import { describe, it, expect } from 'vitest'
import { mergeMeta } from '@/lib/server/seo/seo-helpers'
import type { MetaFunction } from '@remix-run/node'

describe('mergeMeta', () => {
  it('should not modify the base meta if no match meta is provided', () => {
    const overrideFn: MetaFunction = () => []

    const argsWithoutMatches = { matches: [] }

    const result = mergeMeta(overrideFn)(argsWithoutMatches as never)

    expect(result).toEqual([])
  })
})
