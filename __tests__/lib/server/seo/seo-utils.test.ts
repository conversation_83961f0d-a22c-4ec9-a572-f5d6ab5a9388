import { describe, it, expect } from 'vitest'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { seoConfig } from '@/lib/brand/config'
import type { Seo } from '@/lib/server/seo/types'
import { ImageFormat } from '@/lib/api/entity'

describe('buildTags', () => {
  it('should generate meta tags with default seoConfig when no config is provided', () => {
    const result = buildTags(null)

    expect(result).toContainEqual({
      title: `${seoConfig.metaTitle}`,
    })

    expect(result).toContainEqual({
      name: 'title',
      content: seoConfig.metaTitle,
    })

    expect(result).toContainEqual({
      property: 'og:title',
      content: seoConfig.metaTitle,
    })

    expect(result).toContainEqual({
      name: 'description',
      content: seoConfig.metaDescription,
    })

    expect(result).toContainEqual({
      property: 'og:description',
      content: seoConfig.metaDescription,
    })

    expect(result).toContainEqual({
      name: 'keywords',
      content: seoConfig.keywords,
    })

    expect(result).toContainEqual({
      name: 'robots',
      content: seoConfig.metaRobots,
    })

    expect(result).toContainEqual({
      tagName: 'link',
      rel: 'canonical',
      href: import.meta.env.VITE_HOST_URL,
    })
  })

  it('should override meta tags with provided config', () => {
    const mockConfig: Seo = {
      id: 1,
      metaTitle: 'Custom Title',
      metaDescription: 'Custom Description',
      metaImage: {
        data: {
          attributes: {
            url: 'https://example.com/image.png',
            name: null,
            alternativeText: null,
            caption: null,
            width: 0,
            height: 0,
            formats: {} as ImageFormat,
            size: 0,
          },
          id: 0,
        },
      },
      metaSocial: [
        {
          id: 1,
          socialNetwork: 'facebook',
          title: 'Facebook Title',
          description: 'Facebook Description',
          image: 'https://example.com/facebook-image.png',
        },
      ],
      keywords: 'custom,keywords',
      metaRobots: 'noindex,nofollow',
      structuredData: null,
      metaViewport: 'width=device-width, initial-scale=1',
      canonicalURL: 'https://example.com/custom-canonical',
    }

    const result = buildTags(mockConfig)

    expect(result).toContainEqual({
      title: `${seoConfig.metaTitle} - Custom Title`,
    })

    expect(result).toContainEqual({
      name: 'title',
      content: mockConfig.metaTitle,
    })

    expect(result).toContainEqual({
      property: 'og:title',
      content: mockConfig.metaTitle,
    })

    expect(result).toContainEqual({
      name: 'description',
      content: mockConfig.metaDescription,
    })

    expect(result).toContainEqual({
      property: 'og:description',
      content: mockConfig.metaDescription,
    })

    expect(result).toContainEqual({
      name: 'keywords',
      content: mockConfig.keywords,
    })

    expect(result).toContainEqual({
      tagName: 'link',
      rel: 'canonical',
      href: mockConfig.canonicalURL,
    })

    expect(result).toContainEqual({
      property: 'og:facebook:title',
      content: 'Facebook Title',
    })

    expect(result).toContainEqual({
      property: 'og:facebook:description',
      content: 'Facebook Description',
    })

    expect(result).toContainEqual({
      property: 'og:facebook:image',
      content: 'https://example.com/facebook-image.png',
    })

    expect(result).toContainEqual({
      name: 'viewport',
      content: mockConfig.metaViewport,
    })
  })
})
