import {
  i18nConfig,
  SupportLocales,
} from '@/lib/server/translations/i18n.config'
import { LANGUAGE } from '@/constants/language'
import { it, expect, describe, vi } from 'vitest'

vi.stubEnv('DEV', true)

describe('i18nConfig', () => {
  it('should include the correct supported languages', () => {
    expect(SupportLocales).toContain(LANGUAGE.THA)
    expect(SupportLocales).toContain(LANGUAGE.ENG)
  })

  it('should have the correct fallback language', () => {
    expect(i18nConfig.fallbackLng).toBe(LANGUAGE.THA)
  })

  it('should have the correct namespaces', () => {
    expect(i18nConfig.defaultNS).toBe('common')
    expect(i18nConfig.ns).toContain('common')
  })
  it('should have correct React options', () => {
    expect(i18nConfig.react.useSuspense).toBe(false)
    expect(i18nConfig.debug).toBe(true)
  })

  it('should have correct interpolation settings', () => {
    expect(i18nConfig.interpolation.escapeValue).toBe(false)
  })
})
