/* eslint-disable @typescript-eslint/no-explicit-any */
import { vi, describe, it, expect } from 'vitest'
import { LANGUAGE } from '@/constants/language'
import { i18next } from '@/lib/server/translations/i18next.server'

vi.mock('remix-i18next/server', () => ({
  __esModule: true,
  RemixI18Next: vi.fn().mockImplementation(() => ({
    options: {
      i18next: {
        backend: {
          loadPath: '/mock/path/to/translations/{{lng}}/{{ns}}.json',
        },
      },
      detection: {
        supportedLanguages: [LANGUAGE.THA, LANGUAGE.ENG],
        fallbackLanguage: LANGUAGE.ENG,
      },
    },
    init: vi.fn(),
    use: vi.fn(),
  })),
}))

describe('i18next configuration', () => {
  it('should correctly initialize i18next with backend config', async () => {
    expect(i18next).toBeDefined()

    const backendConfig = (i18next as any).options.i18next.backend
    expect(backendConfig.loadPath).toBe(
      '/mock/path/to/translations/{{lng}}/{{ns}}.json'
    )
  })

  it('should include supported languages and fallback language', async () => {
    const supportedLanguages = (i18next as any).options.detection
      .supportedLanguages
    const fallbackLanguage = (i18next as any).options.detection.fallbackLanguage

    expect(supportedLanguages).toContain(LANGUAGE.THA)
    expect(supportedLanguages).toContain(LANGUAGE.ENG)
    expect(fallbackLanguage).toBe(LANGUAGE.ENG)
  })
})
