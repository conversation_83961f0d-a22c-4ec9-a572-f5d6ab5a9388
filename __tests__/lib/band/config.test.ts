import { describe, it, expect } from 'vitest'
import { seoConfig } from '@/lib/brand/config'

describe('seoConfig', () => {
  it('should have the correct properties and values', () => {
    expect(seoConfig).toEqual({
      id: 1,
      metaTitle: 'Aims Clinic',
      metaDescription:
        '<PERSON><PERSON> <PERSON>, a doctor who has been specializing in facial contouring for over 10 years, is ready to design individual treatments.',
      keywords: 'cosmetic services, clinic',
      metaRobots: 'index, follow',
      structuredData: null,
      metaViewport: 'width=device-width, initial-scale=1',
      canonicalURL: null,
      metaSocial: [],
      metaImage: null,
    })
  })

  it('should have the correct types for each property', () => {
    expect(typeof seoConfig.id).toBe('number')
    expect(typeof seoConfig.metaTitle).toBe('string')
    expect(typeof seoConfig.metaDescription).toBe('string')
    expect(typeof seoConfig.keywords).toBe('string')
    expect(typeof seoConfig.metaRobots).toBe('string')
    expect(seoConfig.structuredData).toBeNull()
    expect(typeof seoConfig.metaViewport).toBe('string')
    expect(seoConfig.canonicalURL).toBeNull()
    expect(Array.isArray(seoConfig.metaSocial)).toBe(true)
    expect(seoConfig.metaImage).toBeNull()
  })
})
