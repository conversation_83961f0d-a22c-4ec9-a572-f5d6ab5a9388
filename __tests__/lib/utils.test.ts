import { describe, it, expect } from 'vitest'
import { cn } from '@/lib/utils'

describe('cn utility function', () => {
  it('should combine class names correctly', () => {
    const result = cn('class1', 'class2')
    expect(result).toBe('class1 class2')
  })

  it('should handle conditional class names', () => {
    const result = cn('class1', false && 'class2', 'class3')
    expect(result).toBe('class1 class3')
  })

  it('should merge Tailwind classes correctly', () => {
    const result = cn('text-lg', 'text-sm', 'text-lg')
    expect(result).toBe('text-lg')
  })

  it('should handle mixed inputs', () => {
    const result = cn('bg-red-500', 'bg-blue-500', 'text-lg', 'text-sm')
    expect(result).toBe('bg-blue-500 text-sm')
  })

  it('should merge conflicting Tailwind classes', () => {
    const result = cn('bg-red-500', 'bg-blue-500')
    expect(result).toBe('bg-blue-500')
  })

  it('should handle empty inputs gracefully', () => {
    const result = cn()
    expect(result).toBe('')
  })

  it('should work with undefined or null values', () => {
    const result = cn('class1', undefined, null, 'class2')
    expect(result).toBe('class1 class2')
  })
})
