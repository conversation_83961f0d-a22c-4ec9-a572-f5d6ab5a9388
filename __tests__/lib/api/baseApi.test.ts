import { describe, it, expect, beforeEach, vi } from 'vitest'
import { BaseApi } from '@/lib/api/baseApi'
import { Xior } from 'xior'
import { APPLICATION_JSON, DELETE, POST, PUT } from '@/constants/http'

const mockInstance = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
}

vi.mock('xior', () => {
  return {
    Xior: vi.fn(() => mockInstance),
  }
})

describe('BaseApi', () => {
  let baseApi: BaseApi

  beforeEach(() => {
    baseApi = new BaseApi({
      baseURL: 'https://mock-api.com',
      token: 'mock-token',
      headers: { 'X-Custom-Header': 'custom-value' },
    })
  })

  it('should initialize with correct headers', () => {
    expect(Xior).toHaveBeenCalledWith({
      baseURL: 'https://mock-api.com',
      headers: {
        'Content-Type': APPLICATION_JSON,
        Authorization: 'Bearer mock-token',
        'X-Custom-Header': 'custom-value',
        'Access-Control-Allow-Methods': 'GET, HEAD, POST, PUT, DELETE',
        'Access-Control-Allow-Origin': '*',
      },
    })
  })

  it('should handle GET requests successfully', async () => {
    const mockData = { id: 1, name: 'Test' }

    mockInstance.get.mockResolvedValueOnce({
      status: 200,
      statusText: 'OK',
      data: mockData,
    })

    const result = await baseApi.get('/test-endpoint')

    expect(result).toEqual(mockData)
    expect(mockInstance.get).toHaveBeenCalledWith('/test-endpoint', {
      params: undefined,
    })
  })

  it('should handle POST requests successfully', async () => {
    const mockData = { success: true }
    mockInstance.post.mockResolvedValueOnce({ status: 201, data: mockData })

    const result = await baseApi.post('/test-endpoint', { name: 'Test' })
    expect(result).toEqual(mockData)
    expect(mockInstance.post).toHaveBeenCalledWith(
      '/test-endpoint',
      { name: 'Test' },
      {
        method: POST,
        headers: { 'Content-Type': APPLICATION_JSON },
      }
    )
  })

  it('should handle PUT requests successfully', async () => {
    const mockData = { updated: true }
    mockInstance.put.mockResolvedValueOnce({ status: 200, data: mockData })

    const result = await baseApi.put('/test-endpoint', { name: 'Updated Test' })
    expect(result).toEqual(mockData)
    expect(mockInstance.put).toHaveBeenCalledWith(
      '/test-endpoint',
      {
        name: 'Updated Test',
      },
      {
        method: PUT,
        headers: { 'Content-Type': APPLICATION_JSON },
      }
    )
  })

  it('should handle DELETE requests successfully', async () => {
    const mockData = { deleted: true }
    mockInstance.delete.mockResolvedValueOnce({ status: 200, data: mockData })

    const result = await baseApi.delete('/test-endpoint')
    expect(result).toEqual(mockData)
    expect(mockInstance.delete).toHaveBeenCalledWith('/test-endpoint', {
      method: DELETE,
    })
  })

  it('should handle response errors (non-2xx status)', async () => {
    mockInstance.get.mockResolvedValueOnce({
      status: 400,
      statusText: 'Bad Request',
    })

    await expect(baseApi.get('/error-endpoint')).rejects.toThrow(
      'Request failed: Bad Request (Status Code: 400)'
    )
    expect(mockInstance.get).toHaveBeenCalledWith('/error-endpoint', {
      params: undefined,
    })
  })

  it('should handle network errors', async () => {
    mockInstance.get.mockRejectedValueOnce(new Error('Network Error'))

    await expect(baseApi.get('/error-endpoint')).rejects.toThrow(
      'Error fetching data from /error-endpoint'
    )
    expect(mockInstance.get).toHaveBeenCalledWith('/error-endpoint', {
      params: undefined,
    })
  })

  it('should handle POST request errors', async () => {
    mockInstance.post.mockRejectedValueOnce(new Error('Network Error'))

    await expect(baseApi.post('/error-endpoint', {})).rejects.toThrow(
      'Error posting data to /error-endpoint'
    )
    expect(mockInstance.post).toHaveBeenCalledWith(
      '/error-endpoint',
      {},
      {
        method: POST,
        headers: { 'Content-Type': APPLICATION_JSON },
      }
    )
  })

  it('should handle PUT request errors', async () => {
    mockInstance.put.mockRejectedValueOnce(new Error('Network Error'))

    await expect(baseApi.put('/error-endpoint', {})).rejects.toThrow(
      'Error updating data at /error-endpoint'
    )
    expect(mockInstance.put).toHaveBeenCalledWith(
      '/error-endpoint',
      {},
      {
        method: PUT,
        headers: { 'Content-Type': APPLICATION_JSON },
      }
    )
  })

  it('should handle DELETE request errors', async () => {
    mockInstance.delete.mockRejectedValueOnce(new Error('Network Error'))

    await expect(baseApi.delete('/error-endpoint')).rejects.toThrow(
      'Error deleting data at /error-endpoint'
    )
    expect(mockInstance.delete).toHaveBeenCalledWith('/error-endpoint', {
      method: DELETE,
    })
  })
})
