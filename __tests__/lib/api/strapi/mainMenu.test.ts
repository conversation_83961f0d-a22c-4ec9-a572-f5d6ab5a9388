import { describe, it, expect, vi } from 'vitest'
import { strapiMainMenu } from '@/lib/api/strapi/mainMenu'
import { STRAPI_MAIN_MENU, STRAPI_API } from '@/constants/http'
import { StrapiSingleType } from '@/lib/api/types'
import { MainMenuEntity } from '@/lib/api/entity'

const mockInstance = {
  get: vi.fn(),
}

vi.mock('@/lib/api/internalApi', () => {
  return {
    InternalApi: vi.fn(() => mockInstance),
  }
})

describe('strapiMainMenu', () => {
  it('should call InternalApi.get with correct parameters', async () => {
    const mockResponse: StrapiSingleType<MainMenuEntity> = {
      data: {
        id: 1,
      },
      meta: null,
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiMainMenu('fields=name,url', 'eng')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_MAIN_MENU}?fields=name,url&locale=en`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should handle missing parameters gracefully', async () => {
    const mockResponse: StrapiSingleType<MainMenuEntity> = {
      data: { id: 1 },
      meta: null,
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiMainMenu()

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_MAIN_MENU}?undefined`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should throw an error when InternalApi.get fails', async () => {
    mockInstance.get.mockRejectedValueOnce(new Error('Network Error'))

    await expect(strapiMainMenu('fields=name,url', 'eng')).rejects.toThrow(
      'Network Error'
    )

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_MAIN_MENU}?fields=name,url&locale=en`,
    })
  })
})
