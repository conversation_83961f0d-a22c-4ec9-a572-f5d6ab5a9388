import { describe, it, expect, vi } from 'vitest'
import { strapiGetArticles } from '@/lib/api/strapi/article'
import { STRAPI_ARTICLE, STRAPI_API } from '@/constants/http'
import { StrapiCollectionType } from '@/lib/api/types'
import { ArticleEntity } from '@/lib/api/entity'

const mockInstance = {
  get: vi.fn(),
}

vi.mock('@/lib/api/internalApi', () => {
  return {
    InternalApi: vi.fn(() => mockInstance),
  }
})

describe('strapiGetArticles', () => {
  it('should call InternalApi.get with correct parameters', async () => {
    const mockResponse: StrapiCollectionType<ArticleEntity> = {
      data: [
        {
          id: 1,
        },
      ],
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetArticles('fields=title,slug', 'eng')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_ARTICLE}?fields=title,slug&locale=en`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should handle missing parameters gracefully', async () => {
    const mockResponse: StrapiCollectionType<ArticleEntity> = {
      data: [],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 0,
          total: 0,
        },
      },
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetArticles()

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_ARTICLE}?undefined`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should throw an error when InternalApi.get fails', async () => {
    mockInstance.get.mockRejectedValueOnce(new Error('Network Error'))

    await expect(strapiGetArticles('fields=title,slug', 'eng')).rejects.toThrow(
      'Network Error'
    )

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_ARTICLE}?fields=title,slug&locale=en`,
    })
  })
})
