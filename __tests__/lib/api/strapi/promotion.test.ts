import { describe, it, expect, vi } from 'vitest'
import { strapiGetPromotions } from '@/lib/api/strapi/promotion'
import { STRAPI_PROMOTIONS, STRAPI_API } from '@/constants/http'
import { StrapiCollectionType } from '@/lib/api/types'
import { PromotionEntity } from '@/lib/api/entity'

const mockInstance = {
  get: vi.fn(),
}

vi.mock('@/lib/api/internalApi', () => {
  return {
    InternalApi: vi.fn(() => mockInstance),
  }
})

describe('strapiGetPromotions', () => {
  it('should call InternalApi.get with correct parameters', async () => {
    const mockResponse: StrapiCollectionType<PromotionEntity> = {
      data: [
        {
          id: 1,
        },
      ],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 1,
          total: 1,
        },
      },
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetPromotions('fields=image,service', 'eng')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_PROMOTIONS}?fields=image,service&locale=en`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should handle missing parameters gracefully', async () => {
    const mockResponse: StrapiCollectionType<PromotionEntity> = {
      data: [],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 0,
          total: 0,
        },
      },
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetPromotions()

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_PROMOTIONS}?undefined`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should throw an error when InternalApi.get fails', async () => {
    mockInstance.get.mockRejectedValueOnce(new Error('Network Error'))

    await expect(
      strapiGetPromotions('fields=image,service', 'eng')
    ).rejects.toThrow('Network Error')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_PROMOTIONS}?fields=image,service&locale=en`,
    })
  })
})
