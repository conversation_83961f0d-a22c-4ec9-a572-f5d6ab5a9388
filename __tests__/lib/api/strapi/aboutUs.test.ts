import { describe, it, expect, vi } from 'vitest'
import { strapiGetAboutUs } from '@/lib/api/strapi/aboutUs'
import { STRAPI_ABOUT_US, STRAPI_API } from '@/constants/http'
import { StrapiSingleType } from '@/lib/api/types'
import { AboutUsEntity } from '@/lib/api/entity'

const mockInstance = {
  get: vi.fn(),
}

vi.mock('@/lib/api/internalApi', () => {
  return {
    InternalApi: vi.fn(() => mockInstance),
  }
})

describe('strapiGetAboutUs', () => {
  it('should call InternalApi.get with correct parameters', async () => {
    const mockResponse: StrapiSingleType<AboutUsEntity> = {
      data: {
        id: 1,
      },
      meta: null,
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetAboutUs('fields=landing,profile', 'eng')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_ABOUT_US}?fields=landing,profile&locale=en`,
    })
    expect(result).toEqual(mockResponse)
  })

  it('should handle missing parameters gracefully', async () => {
    const mockResponse: StrapiSingleType<AboutUsEntity> = {
      data: {
        id: 1,
      },
      meta: null,
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetAboutUs()

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_ABOUT_US}?undefined`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should throw an error when InternalApi.get fails', async () => {
    mockInstance.get.mockRejectedValueOnce(new Error('Network Error'))

    await expect(strapiGetAboutUs('fields=main', 'eng')).rejects.toThrow(
      'Network Error'
    )
  })
})
