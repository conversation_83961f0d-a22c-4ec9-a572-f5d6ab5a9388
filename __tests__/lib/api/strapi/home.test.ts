import { strapiGetHome } from '@/lib/api/strapi/home'
import { describe, it, expect, vi } from 'vitest'
import { STRAPI_API, STRAPI_HOME } from '@/constants/http'
import { StrapiSingleType } from '@/lib/api/types'
import { HomeEntity } from '@/lib/api/entity'

const mockInstance = {
  get: vi.fn(),
}

vi.mock('@/lib/api/internalApi', () => {
  return {
    InternalApi: vi.fn(() => mockInstance),
  }
})

describe('strapiGetHome', () => {
  it('should call InternalApi.get with correct parameters', async () => {
    const mockResponse: StrapiSingleType<HomeEntity> = {
      data: {
        id: 1,
      },
      meta: null,
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetHome('undefined', 'eng')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_HOME}?undefined&locale=en`,
    })
    expect(result).toEqual(mockResponse)
  })

  it('should handle missing parameters gracefully', async () => {
    const mockResponse: StrapiSingleType<HomeEntity> = {
      data: {
        id: 1,
      },
      meta: null,
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetHome()

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_HOME}?undefined`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should throw an error when InternalApi.get fails', async () => {
    mockInstance.get.mockRejectedValueOnce(new Error('Network Error'))

    await expect(strapiGetHome('fields=main', 'eng')).rejects.toThrow(
      'Network Error'
    )
  })
})
