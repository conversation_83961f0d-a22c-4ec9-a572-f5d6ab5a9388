import { describe, it, expect, vi } from 'vitest'
import { strapiGetContact } from '@/lib/api/strapi/contact'
import { STRAPI_CONTACT, STRAPI_API } from '@/constants/http'
import { StrapiSingleType } from '@/lib/api/types'
import { ContactEntity } from '@/lib/api/entity'

const mockInstance = {
  get: vi.fn(),
}

vi.mock('@/lib/api/internalApi', () => {
  return {
    InternalApi: vi.fn(() => mockInstance),
  }
})

describe('strapiGetContact', () => {
  it('should call InternalApi.get with correct parameters', async () => {
    const mockResponse: StrapiSingleType<ContactEntity> = {
      data: {
        id: 1,
      },
      meta: null,
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetContact('fields=title,subTitle', 'eng')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_CONTACT}?fields=title,subTitle&locale=en`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should handle missing parameters gracefully', async () => {
    const mockResponse: StrapiSingleType<ContactEntity> = {
      data: {
        id: 1,
      },
      meta: null,
    }

    // Mock the `get` method to resolve with the mock response
    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetContact()

    // Validate the correct call was made
    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_CONTACT}?undefined`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should throw an error when InternalApi.get fails', async () => {
    mockInstance.get.mockRejectedValueOnce(new Error('Network Error'))

    await expect(
      strapiGetContact('fields=title,subTitle', 'eng')
    ).rejects.toThrow('Network Error')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_CONTACT}?fields=title,subTitle&locale=en`,
    })
  })
})
