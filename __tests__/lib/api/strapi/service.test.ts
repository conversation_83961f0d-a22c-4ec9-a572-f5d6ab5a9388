import { describe, it, expect, vi } from 'vitest'
import { strapiGetServices } from '@/lib/api/strapi/service'
import { STRAPI_API, STRAPI_SERVICES } from '@/constants/http'
import { StrapiCollectionType } from '@/lib/api/types'
import { ServiceEntity } from '@/lib/api/entity'

const mockInstance = {
  get: vi.fn(),
}

vi.mock('@/lib/api/internalApi', () => {
  return {
    InternalApi: vi.fn(() => mockInstance),
  }
})

describe('strapiGetServices', () => {
  it('should call InternalApi.get with correct parameters when both param and lng are provided', async () => {
    const mockResponse: StrapiCollectionType<ServiceEntity> = {
      data: [
        {
          id: 1,
        },
      ],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 1,
          total: 1,
        },
      },
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetServices('fields=name,slug', 'eng')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_SERVICES}?fields=name,slug&locale=en`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should call InternalApi.get with correct parameters when only param is provided', async () => {
    const mockResponse: StrapiCollectionType<ServiceEntity> = {
      data: [],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 0,
          total: 0,
        },
      },
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetServices('fields=name,slug')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_SERVICES}?fields=name,slug`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should call InternalApi.get with correct parameters when only lng is provided', async () => {
    const mockResponse: StrapiCollectionType<ServiceEntity> = {
      data: [],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 0,
          total: 0,
        },
      },
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetServices(undefined, 'eng')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_SERVICES}?undefined&locale=en`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should throw an error when InternalApi.get fails', async () => {
    mockInstance.get.mockRejectedValueOnce(new Error('Network Error'))

    await expect(strapiGetServices('fields=name,slug', 'eng')).rejects.toThrow(
      'Network Error'
    )

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_SERVICES}?fields=name,slug&locale=en`,
    })
  })
})
