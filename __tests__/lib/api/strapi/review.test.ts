import { describe, it, expect, vi } from 'vitest'
import { strapiGetReviews } from '@/lib/api/strapi/review'
import { STRAPI_REVIEWS, STRAPI_API } from '@/constants/http'
import { StrapiCollectionType } from '@/lib/api/types'
import { ReviewEntity } from '@/lib/api/entity'

const mockInstance = {
  get: vi.fn(),
}

vi.mock('@/lib/api/internalApi', () => {
  return {
    InternalApi: vi.fn(() => mockInstance),
  }
})

describe('strapiGetReviews', () => {
  it('should call InternalApi.get with correct parameters', async () => {
    const mockResponse: StrapiCollectionType<ReviewEntity> = {
      data: [
        {
          id: 1,
        },
      ],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 1,
          total: 1,
        },
      },
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetReviews(
      'fields=image,service,isShowOnHome',
      'eng'
    )

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_REVIEWS}?fields=image,service,isShowOnHome&locale=en`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should handle missing parameters gracefully', async () => {
    const mockResponse: StrapiCollectionType<ReviewEntity> = {
      data: [],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 0,
          total: 0,
        },
      },
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetReviews()

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_REVIEWS}?undefined`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should throw an error when InternalApi.get fails', async () => {
    mockInstance.get.mockRejectedValueOnce(new Error('Network Error'))

    await expect(
      strapiGetReviews('fields=image,service,isShowOnHome', 'eng')
    ).rejects.toThrow('Network Error')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_REVIEWS}?fields=image,service,isShowOnHome&locale=en`,
    })
  })
})
