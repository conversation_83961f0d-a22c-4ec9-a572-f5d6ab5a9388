import { describe, it, expect, vi } from 'vitest'
import { strapiGetPage } from '@/lib/api/strapi/page'
import { STRAPI_PAGE, STRAPI_API } from '@/constants/http'
import { StrapiCollectionType } from '@/lib/api/types'
import { PageEntity } from '@/lib/api/entity'

const mockInstance = {
  get: vi.fn(),
}

vi.mock('@/lib/api/internalApi', () => {
  return {
    InternalApi: vi.fn(() => mockInstance),
  }
})

describe('strapiGetPage', () => {
  it('should call InternalApi.get with correct parameters', async () => {
    const mockResponse: StrapiCollectionType<PageEntity> = {
      data: [
        {
          id: 1,
        },
      ],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 1,
          total: 1,
        },
      },
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetPage('fields=slug,name', 'eng')

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_PAGE}?fields=slug,name&locale=en`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should handle missing parameters gracefully', async () => {
    const mockResponse: StrapiCollectionType<PageEntity> = {
      data: [],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 0,
          total: 0,
        },
      },
    }

    mockInstance.get.mockResolvedValueOnce(mockResponse)

    const result = await strapiGetPage()

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_PAGE}?undefined`,
    })

    expect(result).toEqual(mockResponse)
  })

  it('should throw an error when InternalApi.get fails', async () => {
    mockInstance.get.mockRejectedValueOnce(new Error('Network Error'))

    await expect(strapiGetPage('fields=slug,name', 'eng')).rejects.toThrow(
      'Network Error'
    )

    expect(mockInstance.get).toHaveBeenCalledWith(STRAPI_API, {
      endpoint: `${STRAPI_PAGE}?fields=slug,name&locale=en`,
    })
  })
})
