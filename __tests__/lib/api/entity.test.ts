import { describe, it, expect } from 'vitest'
import {
  PageEntity,
  MainMenuEntity,
  ServiceEntity,
  AboutUsEntity,
  ContactEntity,
  ImageType,
  ArticleEntity,
  ServiceContentImage,
  ImageFormat,
  HomeEntity,
} from '@/lib/api/entity'
import { LayoutAlignment } from '@/enums/layoutAlignment'

describe('Interface Validation', () => {
  it('should validate PageEntity', () => {
    const page: PageEntity = {
      id: 1,
      slug: 'home',
      name: 'Homepage',
      backgroundImage: {
        data: {
          id: 1,
          attributes: {
            name: 'Background',
            alternativeText: null,
            caption: null,
            width: 0,
            height: 0,
            formats: {} as ImageFormat,
            size: 0,
          },
        },
      },
      images: {
        data: [
          {
            id: 2,
            attributes: {
              name: 'Gallery Image',
              alternativeText: null,
              caption: null,
              width: 0,
              height: 0,
              formats: {} as ImageFormat,
              size: 0,
            },
          },
        ],
      },
      seo: {
        id: 0,
        metaTitle: null,
        metaDescription: null,
        metaImage: null,
        metaSocial: null,
        keywords: null,
        metaRobots: null,
        structuredData: null,
        metaViewport: null,
        canonicalURL: null,
      },
    }

    expect(page.id).toBe(1)
    expect(page.slug).toBe('home')
  })

  it('should validate MainMenuEntity', () => {
    const mainMenu: MainMenuEntity = {
      mainMenuItems: [
        {
          id: 1,
          __component: null,
          name: 'Menu Item',
          url: '/menu-item',
          subMenuLink: [
            {
              id: 2,
              __component: null,
              name: 'Sub Menu',
              image: null,
              structuredData: null,
              url: '/sub-menu',
              menu: [],
            },
          ],
        },
      ],
    }

    expect(mainMenu.mainMenuItems?.[0].name).toBe('Menu Item')
  })

  it('should validate ServiceEntity', () => {
    const service: ServiceEntity = {
      slug: 'service',
      name: 'Test Service',
      reviews: {
        data: [
          {
            id: 1,
            attributes: {
              id: 1,
              isShowOnHome: true,
              isShowOnService: true,
            },
          },
        ],
      },
      questionAnswer: [
        { id: 1, question: 'What is this?', answer: 'A test service.' },
      ],
      seo: {
        id: 0,
        metaTitle: null,
        metaDescription: null,
        metaImage: null,
        metaSocial: null,
        keywords: null,
        metaRobots: null,
        structuredData: null,
        metaViewport: null,
        canonicalURL: null,
      },
      tag: null,
    }

    expect(service.name).toBe('Test Service')
    expect(service.reviews).toBeDefined()
    expect(service.reviews?.data).toBeDefined()
    expect(service.reviews?.data?.[0]?.attributes?.isShowOnHome).toBe(true)
    expect(service.reviews?.data?.[0]?.attributes?.isShowOnService).toBe(true)
  })

  it('should validate AboutUsEntity', () => {
    const aboutUs: AboutUsEntity = {
      landing: {
        content: '<p>Landing Content</p>',
        media: {
          data: [
            {
              id: 1,
              attributes: {
                name: 'Landing Image',
                alternativeText: null,
                caption: null,
                width: 0,
                height: 0,
                formats: {} as ImageFormat,
                size: 0,
              },
            },
          ],
        },
        alignment: LayoutAlignment.Left,
      },
      profile: null,
      mainDescription: '<p>Main Description</p>',
      reason: null,
      experience: null,
      main: null,
    }

    expect(aboutUs.landing?.content).toBe('<p>Landing Content</p>')
  })

  it('should validate ContactEntity', () => {
    const contact: ContactEntity = {
      title: 'Contact Us',
      subTitle: 'We are here to help',
      openTitle: 'Open Hours',
      openTime: '9 AM - 5 PM',
      telephone: '************',
      email: '<EMAIL>',
      address: null,
      map: {
        id: 1,
        title: 'Office Location',
        detail: '123 Main Street',
        link: 'https://maps.example.com',
        zoomLevel: 10,
        markerLat: 12.345,
        markerLon: 67.89,
        locationLat: 12.345,
        locationLon: 67.89,
        linkDetail: null,
        telephone: null,
        address: null,
        centerLon: null,
        centerLat: null,
      },
      backgroundImage: null,
      images: null,
    }

    expect(contact.title).toBe('Contact Us')
    expect(contact.map?.zoomLevel).toBe(10)
  })

  it('should validate ImageType', () => {
    const image: ImageType = {
      name: 'Sample Image',
      alternativeText: 'Alt Text',
      caption: 'A sample image',
      width: 800,
      height: 600,
      formats: {
        thumbnail: {
          name: 'thumb',
          width: 150,
          height: 150,
          size: 20,
          url: '/thumb.jpg',
          hash: null,
          ext: null,
          mime: null,
        },
        thumbnail_webp: null,
        thumbnail_avif: null,
      },
      size: 100,
      url: '/image.jpg',
    }

    expect(image.name).toBe('Sample Image')
    expect(image.formats.thumbnail?.url).toBe('/thumb.jpg')
  })

  it('should validate ArticleEntity', () => {
    const article: ArticleEntity = {
      id: 1,
      title: 'Sample Article',
      slug: 'sample-article',
      content: '<p>This is content</p>',
      seo: {
        id: 0,
        metaTitle: null,
        metaDescription: null,
        metaImage: null,
        metaSocial: null,
        keywords: null,
        metaRobots: null,
        structuredData: null,
        metaViewport: null,
        canonicalURL: null,
      },
    }

    expect(article.title).toBe('Sample Article')
  })

  it('should detect invalid ServiceContentImage', () => {
    // @ts-expect-error: Missing required 'id' property
    const invalidContentImage: ServiceContentImage = {
      __component: 'service.content.image',
      description1: null,
      image1: null,
      description2: null,
      image2: null,
      type: 'image',
    }

    expect(invalidContentImage).not.toBeUndefined()
  })

  it('should detect invalid PageEntity', () => {
    // @ts-expect-error: Missing required 'id' property
    const invalidPage: PageEntity = {
      name: 'Invalid Page',
    }

    expect(invalidPage).not.toBeUndefined()
  })

  it('should validate Home Entity', () => {
    const home: HomeEntity = {
      banner: '<p>Landing Content</p>',
      sticky: '<p>Test</p>',
    }

    expect(home.banner).toBe('<p>Landing Content</p>')
  })
})
