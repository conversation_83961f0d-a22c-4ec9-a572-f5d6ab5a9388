import { describe, it, expect } from 'vitest'
import {
  XiorClientConfig,
  StrapiPagination,
  PaginationQuery,
  StrapiAttributes,
  StrapiCollectionType,
  StrapiSingleType,
} from '@/lib/api/types'

describe('XiorClientConfig', () => {
  it('should allow valid configurations', () => {
    const validConfig: XiorClientConfig = {
      baseURL: 'https://api.example.com',
      token: 'mock-token',
      headers: {
        'Custom-Header': 'value',
      },
    }

    expect(validConfig).toBeDefined()
    expect(validConfig.headers?.['Custom-Header']).toBe('value')
  })

  it('should catch invalid configurations', () => {
    // @ts-expect-error: Missing required 'baseURL' property
    const invalidConfig: XiorClientConfig = {
      token: 'mock-token',
    }

    expect(invalidConfig).not.toBeUndefined()
  })
})

describe('StrapiPagination', () => {
  it('should handle valid pagination metadata', () => {
    const pagination: StrapiPagination = {
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 5,
          total: 50,
        },
      },
    }

    expect(pagination.meta?.pagination?.page).toBe(1)
    expect(pagination.meta?.pagination?.total).toBe(50)
  })

  it('should allow missing optional pagination fields', () => {
    const partialPagination: StrapiPagination = {}

    expect(partialPagination.meta).toBeUndefined()
  })
})

describe('PaginationQuery', () => {
  it('should accept valid pagination queries', () => {
    const query: PaginationQuery = {
      withCount: true,
      start: 0,
      limit: 10,
      page: 1,
      pageSize: 10,
    }

    expect(query.page).toBe(1)
    expect(query.withCount).toBeTruthy()
  })

  it('should detect missing fields', () => {
    const invalidQuery: PaginationQuery = {
      withCount: true,
      limit: 10,
      page: 1,
      pageSize: 10,
    }

    expect(invalidQuery).not.toBeUndefined()
  })
})

describe('StrapiAttributes', () => {
  it('should handle attributes with valid types', () => {
    const attributes: StrapiAttributes<{ name: string }> = {
      id: 1,
      attributes: { name: 'Test' },
    }

    expect(attributes.id).toBe(1)
    expect(attributes.attributes?.name).toBe('Test')
  })

  it('should allow missing attributes', () => {
    const noAttributes: StrapiAttributes<unknown> = {
      id: 2,
    }

    expect(noAttributes.id).toBe(2)
    expect(noAttributes.attributes).toBeUndefined()
  })
})

describe('StrapiCollectionType', () => {
  it('should handle collection type data', () => {
    const collection: StrapiCollectionType<{ title: string }> = {
      data: [
        { id: 1, attributes: { title: 'First' } },
        { id: 2, attributes: { title: 'Second' } },
      ],
      meta: {
        pagination: {
          page: 1,
          pageSize: 10,
          pageCount: 1,
          total: 2,
        },
      },
    }

    expect(collection.data?.length).toBe(2)
    expect(collection.meta?.pagination?.total).toBe(2)
  })
})

describe('StrapiSingleType', () => {
  it('should handle single type data', () => {
    const single: StrapiSingleType<{ name: string }> = {
      data: { id: 1, attributes: { name: 'Single Item' } },
    }

    expect(single.data?.attributes?.name).toBe('Single Item')
  })

  it('should allow empty meta', () => {
    const singleWithoutMeta: StrapiSingleType<unknown> = {
      data: { id: 1 },
    }

    expect(singleWithoutMeta.data?.id).toBe(1)
    expect(singleWithoutMeta.meta).toBeUndefined()
  })
})
