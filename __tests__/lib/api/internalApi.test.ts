/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { InternalApi } from '@/lib/api/internalApi'

vi.stubEnv('VITE_HOST_URL', 'https://mock-host-url.com')

describe('InternalApi', () => {
  let internalApi: InternalApi

  beforeEach(() => {
    internalApi = new InternalApi()
  })

  it('should initialize with the correct baseURL', () => {
    expect((internalApi as any).xiorClient?.config.baseURL).toBe(
      'https://mock-host-url.com/api'
    )
  })

  it('should initialize with an empty token', () => {
    expect((internalApi as any).xiorClient.config.headers.Authorization).toBe(
      undefined
    )
  })
})
