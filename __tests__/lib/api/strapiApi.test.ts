/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { StrapiApi } from '@/lib/api/strapiApi'

vi.stubEnv('VITE_STRAPI_URL', 'https://mock-strapi-url.com')
vi.stubEnv('VITE_STRAPI_API_TOKEN', 'mock-strapi-token')

describe('StrapiApi', () => {
  let strapiApi: StrapiApi

  beforeEach(() => {
    strapiApi = new StrapiApi()
  })

  it('should initialize with correct baseURL', () => {
    expect((strapiApi as any).xiorClient?.config.baseURL).toBe(
      'https://mock-strapi-url.com'
    )
  })

  it('should initialize with correct token', () => {
    expect(
      (strapiApi as any).xiorClient.config.headers.Authorization
    ).toContain('mock-strapi-token')
  })
})
