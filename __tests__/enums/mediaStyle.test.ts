import { describe, it, expect } from 'vitest'
import { MediaStyle } from '@/enums/mediaStyle'

describe('MediaStyle Enum', () => {
  it('should contain the correct keys and values', () => {
    expect(MediaStyle).toHaveProperty('Round', 'Round')
    expect(MediaStyle).toHaveProperty('Circle', 'Circle')
    expect(MediaStyle).toHaveProperty('Oval', 'Oval')
  })

  it('should match the expected enum structure', () => {
    const expectedEnum = {
      Round: 'Round',
      Circle: 'Circle',
      Oval: 'Oval',
    }

    expect(MediaStyle).toEqual(expectedEnum)
  })

  it('should not contain extra keys', () => {
    const expectedKeys = ['Round', 'Circle', 'Oval']
    expect(Object.keys(MediaStyle)).toEqual(expectedKeys)
  })
})
