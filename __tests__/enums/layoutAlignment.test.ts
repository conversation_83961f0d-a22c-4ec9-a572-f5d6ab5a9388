import { describe, it, expect } from 'vitest'
import { LayoutAlignment } from '@/enums/layoutAlignment'

describe('LayoutAlignment Enum', () => {
  it('should contain the correct keys and values', () => {
    expect(LayoutAlignment).toHaveProperty('Left', 'Left')
    expect(LayoutAlignment).toHaveProperty('Right', 'Right')
  })

  it('should match the enum structure', () => {
    const expectedEnum = {
      Left: 'Left',
      Right: 'Right',
    }

    expect(LayoutAlignment).toEqual(expectedEnum)
  })
})
