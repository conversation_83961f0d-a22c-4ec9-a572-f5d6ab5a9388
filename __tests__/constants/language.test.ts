import { describe, it, expect } from 'vitest'
import { LANGUAGE } from '@/constants/language'

describe('LANGUAGE Constants', () => {
  it('should contain the correct keys', () => {
    expect(LANGUAGE).toHaveProperty('ENG')
    expect(LANGUAGE).toHaveProperty('THA')
  })

  it('should have correct values for ENG and THA', () => {
    expect(LANGUAGE.ENG).toBe('eng')
    expect(LANGUAGE.THA).toBe('tha')
  })

  it('should not contain extra keys', () => {
    const expectedKeys = ['ENG', 'THA']
    expect(Object.keys(LANGUAGE)).toEqual(expectedKeys)
  })
})
