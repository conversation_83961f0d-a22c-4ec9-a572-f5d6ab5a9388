import { describe, it, expect } from 'vitest'
import {
  APPLICATION_JSON,
  POST,
  STRAPI_ABOUT_US,
  STRAPI_ARTICLE,
  STRAPI_API,
  STRAPI_CONTACT,
  STRAPI_MAIN_MENU,
  STRAPI_PAGE,
  STRAPI_PROMOTIONS,
  STRAPI_REASON,
  STRAPI_REVIEWS,
  STRAPI_SERVICES,
  INTERNAL_IMAGE,
  INTERNAL_SEND_EMAIL,
} from '@/constants/http'

describe('String Constants', () => {
  it('should have correct APPLICATION_JSON value', () => {
    expect(APPLICATION_JSON).toBe('application/json')
  })

  it('should have correct HTTP POST value', () => {
    expect(POST).toBe('POST')
  })

  it('should have correct STRAPI constants', () => {
    expect(STRAPI_ABOUT_US).toBe('about-us')
    expect(STRAPI_ARTICLE).toBe('articles')
    expect(STRAPI_API).toBe('strapi')
    expect(STRAPI_CONTACT).toBe('contact')
    expect(STRAPI_MAIN_MENU).toBe('main-menu')
    expect(STRAPI_PAGE).toBe('pages')
    expect(STRAPI_PROMOTIONS).toBe('promotions')
    expect(STRAPI_REASON).toBe('reason')
    expect(STRAPI_REVIEWS).toBe('reviews')
    expect(STRAPI_SERVICES).toBe('services')
  })

  it('should have correct INTERNAL constants', () => {
    expect(INTERNAL_IMAGE).toBe('image')
    expect(INTERNAL_SEND_EMAIL).toBe('send-email')
  })
})
