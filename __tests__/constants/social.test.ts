import { describe, it, expect } from 'vitest'
import { SOCIAL_MEDIA } from '@/constants/social'

describe('SOCIAL_MEDIA Constants', () => {
  it('should contain correct keys', () => {
    expect(SOCIAL_MEDIA).toHaveProperty('LINE')
    expect(SOCIAL_MEDIA).toHaveProperty('FACEBOOK')
    expect(SOCIAL_MEDIA).toHaveProperty('TIKTOK')
    expect(SOCIAL_MEDIA).toHaveProperty('INSTAGRAM')
  })

  it('should have correct URLs for each key', () => {
    expect(SOCIAL_MEDIA.LINE).toBe('https://line.me/R/ti/p/@347luzyv')
    expect(SOCIAL_MEDIA.FACEBOOK).toBe(
      'https://www.facebook.com/AimsClinicByDoctorAmy'
    )
    expect(SOCIAL_MEDIA.TIKTOK).toBe(
      'https://www.tiktok.com/@dr.amy.aimsclinic'
    )
    expect(SOCIAL_MEDIA.INSTAGRAM).toBe(
      'https://www.instagram.com/aims.clinic/'
    )
  })

  it('should not contain extra keys', () => {
    const expectedKeys = ['LINE', 'FACEBOOK', 'TIKTOK', 'INSTAGRAM', 'PHONE']
    expect(Object.keys(SOCIAL_MEDIA)).toEqual(expectedKeys)
  })
})
