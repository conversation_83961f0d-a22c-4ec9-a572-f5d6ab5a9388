import { ENV } from '@/constants/environment'
import { describe, it, expect } from 'vitest'

describe('ENV Constants', () => {
  it('should have DEVELOPMENT and PRODUCTION keys', () => {
    expect(ENV).toHaveProperty('DEVELOPMENT')
    expect(ENV).toHaveProperty('PRODUCTION')
  })

  it('should have correct values for DEVELOPMENT and PRODUCTION', () => {
    expect(ENV.DEVELOPMENT).toBe('development')
    expect(ENV.PRODUCTION).toBe('production')
  })

  it('should not have additional keys', () => {
    expect(Object.keys(ENV)).toEqual(['DEVELOPMENT', 'PRODUCTION'])
  })
})
