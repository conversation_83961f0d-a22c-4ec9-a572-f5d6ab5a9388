import { describe, it, expect, vi } from 'vitest'
import { action } from '@/routes/_themeswitcher+/action.set-theme'
import { createThemeAction } from 'remix-themes'
import { themeSessionResolver } from '@/services/session.server'

vi.mock('remix-themes', () => ({
  createThemeAction: vi.fn((resolver) => async () => {
    if (!resolver) {
      throw new Error('Resolver is required')
    }
    return new Response(null, { status: 200 })
  }),
}))
vi.mock('@/services/session.server', () => ({
  themeSessionResolver: vi.fn(() => ({
    getSession: vi.fn(),
    commitSession: vi.fn(),
    destroySession: vi.fn(),
  })),
}))

describe('Theme Action', () => {
  it('should call createThemeAction with themeSessionResolver', async () => {
    const mockRequest = new Request('http://localhost', {
      method: 'POST',
      body: JSON.stringify({ theme: 'dark' }),
    })

    const result = await action({
      request: mockRequest,
      params: {},
      context: {},
    })

    expect(createThemeAction).toHaveBeenCalledWith(themeSessionResolver)

    expect(result).not.toBeNull()
    expect((result as Response).status).toBe(200)
  })
})
