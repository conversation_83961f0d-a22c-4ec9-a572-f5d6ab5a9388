/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi } from 'vitest'
import SlugPage, { loader } from '@/routes/$[slug]'
import { fetchPageBySlug } from '@/utils/fetchHelper'
import { i18nConfig } from '@/lib/server/translations/i18n.config'
import { render } from '@testing-library/react'

vi.mock('@/utils/fetchHelper', () => ({
  fetchPageBySlug: vi.fn(),
}))

describe('SlugPage loader', () => {
  it('should call fetchPageBySlug with correct slug and locale', async () => {
    const mockParams = { slug: 'test-slug' }
    const mockRequest = new Request('http://example.com?lng=fr')

    await loader({
      params: mockParams,
      request: mockRequest,
    } as any)

    expect(fetchPageBySlug).toHaveBeenCalledWith('test-slug', 'fr')
  })

  it('should use fallback locale if "lng" is not provided in the URL', async () => {
    const mockParams = { slug: 'test-slug' }
    const mockRequest = new Request('http://example.com')

    await loader({
      params: mockParams,
      request: mockRequest,
    } as any)

    expect(fetchPageBySlug).toHaveBeenCalledWith(
      'test-slug',
      i18nConfig.fallbackLng
    )
  })

  it('should call fetchPageBySlug with empty slug if no slug is provided', async () => {
    const mockParams = {}
    const mockRequest = new Request('http://example.com?lng=fr')

    await loader({
      params: mockParams,
      request: mockRequest,
    } as any)

    expect(fetchPageBySlug).toHaveBeenCalledWith('', 'fr')
  })
})

describe('SlugPage Component', () => {
  it('should render without crashing', () => {
    const { container } = render(<SlugPage />)
    expect(container).toBeEmptyDOMElement()
  })
})
