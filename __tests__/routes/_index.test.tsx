/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi } from 'vitest'
import Index, { loader } from '@/routes/_index'
import { fetchPageBySlug } from '@/utils/fetchHelper'
import {
  strapiGetContact,
  strapiGetAboutUs,
  strapiGetPromotions,
} from '@/lib/api/strapi'
import { warperSingleType } from '@/utils/strapiHelper'
import { AppContext } from '@/providers/appContext'
import { render, screen, waitFor } from '@testing-library/react'
import { useLoaderData } from '@remix-run/react'
import { BrowserRouter } from 'react-router-dom'
import { strapiGetReviews } from '@/lib/api/strapi/review'

vi.mock('@/utils/fetchHelper', () => ({
  fetchPageBySlug: vi.fn(),
  queryStringBuilder: vi.fn(() => 'mockedQueryString'),
}))

vi.mock('@/lib/api/strapi', () => ({
  strapiGetContact: vi.fn(),
  strapiGetAboutUs: vi.fn(),
  strapiGetPromotions: vi.fn(),
}))

vi.mock('@/lib/api/strapi/review', () => ({
  strapiGetReviews: vi.fn(),
}))

vi.mock('@/lib/api/strapi/home', () => ({
  strapiGetHome: vi.fn(),
}))

vi.mock(import('@/utils/strapiHelper'), async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    warperSingleType: vi.fn(),
  }
})

describe('loader', () => {
  it('should return the expected data structure', async () => {
    const mockRequest = new Request('http://example.com?lng=eng')
    const mockResponsePage = { title: 'Home Page' }
    const mockResponseContact = { email: '<EMAIL>' }
    const mockResponseAboutUs = { title: 'About Us' }

    ;(fetchPageBySlug as any).mockResolvedValueOnce(mockResponsePage)
    ;(strapiGetContact as any).mockResolvedValueOnce(mockResponseContact)
    ;(strapiGetAboutUs as any).mockResolvedValueOnce(mockResponseAboutUs)
    ;(warperSingleType as any).mockImplementation((data) => data)

    const result = await loader({ request: mockRequest } as any)

    expect(fetchPageBySlug).toHaveBeenCalledWith(
      'home',
      'eng',
      'mockedQueryString'
    )
    expect(strapiGetContact).toHaveBeenCalledWith('mockedQueryString', 'eng')
    expect(strapiGetAboutUs).toHaveBeenCalledWith('mockedQueryString', 'eng')
    expect(result).toEqual({
      dataPage: mockResponsePage,
      dataContact: mockResponseContact,
      dataAboutUs: mockResponseAboutUs,
      locale: 'eng',
    })
  })
})

vi.mock(import('@remix-run/react'), async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    useLoaderData: vi.fn(),
  }
})

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
    i18n: { changeLanguage: vi.fn() },
  }),
}))

vi.mock('resize-observer-polyfill', () => {
  return {
    default: class {
      observe() {}
      unobserve() {}
      disconnect() {}
    },
  }
})

describe('Index Component', () => {
  it('should render all sections correctly', async () => {
    const mockData = {
      dataPage: { title: 'Home Page' },
      dataContact: { email: '<EMAIL>' },
      dataAboutUs: { reason: { title: 'Why Us?' } },
      locale: 'eng',
    }

    const mockMainMenu = {
      mainMenuItems: [
        {
          id: 1,
          __component: null,
          name: 'service',
          url: 'service',
        },
      ],
    }
    const mockPromotion = {
      data: [
        {
          id: 1,
          attributes: {
            image: {},
            service: {
              data: {
                attributes: {
                  tag: 'test',
                },
              },
            },
          },
        },
      ],
    }

    const mockReview = {
      data: [
        {
          id: 1,
          attributes: {
            image: {},
            service: {
              data: {
                attributes: {
                  tag: 'test',
                },
              },
            },
          },
        },
      ],
    }

    ;(strapiGetReviews as any).mockResolvedValue(mockReview)
    ;(strapiGetPromotions as any).mockResolvedValue(mockPromotion)
    ;(useLoaderData as any).mockReturnValue(mockData)

    render(
      <BrowserRouter>
        <AppContext.Provider value={{ mainMenu: mockMainMenu, home: null }}>
          <Index />
        </AppContext.Provider>
      </BrowserRouter>
    )
    await waitFor(() => {
      expect(screen.getByText('Previous slide')).toBeInTheDocument()
      expect(screen.getByText('Next slide')).toBeInTheDocument()
      expect(screen.getAllByLabelText('Aims Logo Text')[0]).toBeInTheDocument()
    })
  })
})
