import { describe, it, expect, vi } from 'vitest'
import { loader } from '@/routes/robots[.txt]'
import { generateRobotsTxt } from '@/lib/server/robots/robots.server'

vi.mock('@/lib/server/robots/robots.server', () => ({
  generateRobotsTxt: vi.fn(),
}))

describe('loader', () => {
  it('should generate robots.txt with the correct policies', async () => {
    const mockGenerateRobotsTxt = vi.mocked(generateRobotsTxt)

    const extraPolicies = [
      { type: 'userAgent', value: 'Googlebot' },
      { type: 'disallow', value: '/nogooglebot/' },
      {
        type: 'sitemap',
        value: `${import.meta.env.VITE_HOST_URL}/sitemap.xml`,
      },
    ]

    const expectedOptions = {
      appendOnDefaultPolicies: true,
      headers: { 'Cache-Control': 'public, max-age=86400' },
    }

    const mockResponse = new Response('User-agent: *\nDisallow: /', {
      headers: { 'Cache-Control': 'public, max-age=86400' },
    })
    mockGenerateRobotsTxt.mockReturnValue(mockResponse as never)

    const response = await loader()

    expect(mockGenerateRobotsTxt).toHaveBeenCalledWith(
      extraPolicies,
      expectedOptions
    )
    expect(response).toBe(mockResponse)

    expect(response.headers.get('Cache-Control')).toBe('public, max-age=86400')
  })
})
