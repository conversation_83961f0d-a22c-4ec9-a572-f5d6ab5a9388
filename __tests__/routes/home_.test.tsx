import { describe, it, expect, vi } from 'vitest'
import HomePage, { loader } from '@/routes/home_'
import { defineRoute } from '@/utils/routeHelper'
import { getLocaleParam } from '@/utils/localeHelper'
import { redirect } from '@remix-run/node'
import { render } from '@testing-library/react'

vi.mock('@/utils/routeHelper', () => ({
  defineRoute: vi.fn(),
}))

vi.mock('@/utils/localeHelper', () => ({
  getLocaleParam: vi.fn(),
}))

vi.mock('@remix-run/node', () => ({
  redirect: vi.fn(),
}))

describe('HomePage Loader', () => {
  it('should redirect to the correct locale URL', async () => {
    vi.mocked(defineRoute).mockReturnValue({
      lng: 'eng',
      url: new URL('http://localhost'),
      path: '',
      slug: '',
    })
    vi.mocked(getLocaleParam).mockReturnValue('eng')

    const mockRequest = new Request('http://localhost')
    loader({ request: mockRequest })

    expect(defineRoute).toHaveBeenCalledWith(mockRequest)
    expect(getLocaleParam).toHaveBeenCalledWith('eng')
    expect(redirect).toHaveBeenCalledWith('/eng')
  })

  it('should handle a different locale correctly', async () => {
    vi.mocked(defineRoute).mockReturnValue({
      lng: 'fr',
      url: new URL('http://localhost'),
      path: '',
      slug: '',
    })
    vi.mocked(getLocaleParam).mockReturnValue('fr')

    const mockRequest = new Request('http://localhost')
    loader({ request: mockRequest })

    expect(defineRoute).toHaveBeenCalledWith(mockRequest)
    expect(getLocaleParam).toHaveBeenCalledWith('fr')
    expect(redirect).toHaveBeenCalledWith('/fr')
  })
})

describe('HomePage Component', () => {
  it('should render without crashing', () => {
    const { container } = render(<HomePage />)
    expect(container).toBeEmptyDOMElement()
  })
})
