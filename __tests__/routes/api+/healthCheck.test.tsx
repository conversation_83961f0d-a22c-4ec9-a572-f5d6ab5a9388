/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi } from 'vitest'
import { loader } from '@/routes/api+/healthCheck'
import { StrapiApi } from '@/lib/api/strapiApi'

vi.mock('@/lib/api/strapiApi', () => {
  const mockGet = vi.fn()
  return {
    StrapiApi: vi.fn(() => ({
      get: mockGet,
    })),
  }
})

describe('loader', () => {
  it('should return 200 when Strapi is available', async () => {
    const mockStrapiApi = new StrapiApi()
    ;(mockStrapiApi.get as any).mockResolvedValueOnce({})

    const response = (await loader({} as never)) as Response
    expect(response.status).toBe(200)

    const body = await response?.json()
    expect(body).toEqual({
      strapi: 'Available ✅',
      status: 200,
      datetime: expect.any(String),
    })
  })

  it('should return 500 when Strap<PERSON> is not available', async () => {
    const mockStrapiApi = new StrapiApi()
    ;(mockStrapiApi.get as any).mockRejectedValueOnce(
      new Error('Network Error')
    )

    const response = (await loader({} as never)) as Response
    expect(response.status).toBe(500)

    const body = await response.json()
    expect(body).toEqual({
      strapi: 'Not available ❌',
      status: 500,
      datetime: expect.any(String),
    })
  })
})
