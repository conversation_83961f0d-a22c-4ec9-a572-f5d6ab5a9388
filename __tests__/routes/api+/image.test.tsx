/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi } from 'vitest'
import { loader, myResolver } from '@/routes/api+/image'
import { imageLoader, fsResolver, fetchResolver } from 'remix-image/server'

vi.mock('remix-image/server', () => {
  return {
    fsResolver: vi.fn(),
    fetchResolver: vi.fn(),
    imageLoader: vi.fn(),
    MemoryCache: vi.fn().mockImplementation(() => ({
      get: vi.fn(),
      set: vi.fn(),
    })),
  }
})

vi.mock('@/lib/server/disk-cache.server', () => {
  return {
    DiskCache: vi.fn().mockImplementation(() => ({
      get: vi.fn(),
      set: vi.fn(),
      clear: vi.fn(),
    })),
  }
})

describe('Image Loader', () => {
  it('should call imageLoader with correct config and request', async () => {
    const mockRequest = new Request('http://localhost/image.png')

    const mockImageLoader = (imageLoader as any).mockResolvedValueOnce(
      new Response('Image data', { status: 200 })
    )

    const response = (await loader({
      request: mockRequest,
    } as never)) as Response

    expect(mockImageLoader).toHaveBeenCalledTimes(1)
    expect(response.status).toBe(200)
    const body = await response.text()
    expect(body).toBe('Image data')
  })

  it('should handle errors and return an appropriate response', async () => {
    const mockRequest = new Request('http://localhost/image.png')

    ;(imageLoader as any).mockResolvedValueOnce(
      new Response('Failed to load image', { status: 500 })
    )

    const response = (await loader({
      request: mockRequest,
    } as never)) as Response

    expect(response.status).toBe(500)
    const body = await response.text()
    expect(body).toContain('Failed to load image')
  })
})

describe('myResolver', () => {
  const mockOptions = { quality: 80 }
  const mockBasePath = '/base/path'

  it('should call fsResolver for local assets', async () => {
    const mockAsset = '/local/image.png'
    const mockUrl = 'http://localhost/image.png'

    const expectedResponse = 'fsResolvedData'
    vi.mocked(fsResolver).mockResolvedValueOnce(expectedResponse as never)

    const result = await myResolver(
      mockAsset,
      mockUrl,
      mockOptions,
      mockBasePath
    )

    expect(fsResolver).toHaveBeenCalledWith(
      mockAsset,
      mockUrl,
      mockOptions,
      mockBasePath
    )
    expect(fetchResolver).not.toHaveBeenCalled()
    expect(result).toBe(expectedResponse)
  })

  it('should call fetchResolver for remote assets', async () => {
    const mockAsset = 'https://example.com/image.png'
    const mockUrl = 'https://example.com/image.png'

    const expectedResponse = 'fetchResolvedData'
    vi.mocked(fetchResolver).mockResolvedValueOnce(expectedResponse as never)

    const result = await myResolver(
      mockAsset,
      mockUrl,
      mockOptions,
      mockBasePath
    )

    expect(fetchResolver).toHaveBeenCalledWith(
      mockAsset,
      mockUrl,
      mockOptions,
      mockBasePath
    )
    expect(result).toBe(expectedResponse)
  })

  it('should call fetchResolver for protocol-relative assets', async () => {
    const mockAsset = '//example.com/image.png'
    const mockUrl = 'https://example.com/image.png'

    const expectedResponse = 'fetchResolvedData'
    vi.mocked(fetchResolver).mockResolvedValueOnce(expectedResponse as never)

    const result = await myResolver(
      mockAsset,
      mockUrl,
      mockOptions,
      mockBasePath
    )

    expect(fetchResolver).toHaveBeenCalledWith(
      mockAsset,
      mockUrl,
      mockOptions,
      mockBasePath
    )
    expect(result).toBe(expectedResponse)
  })
})
