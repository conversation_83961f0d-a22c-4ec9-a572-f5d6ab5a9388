import { describe, it, expect, vi, beforeEach } from 'vitest'
import { loader } from '@/routes/sitemap[.]xml'
import { i18nConfig } from '@/lib/server/translations/i18n.config'
import { strapiGetServices } from '@/lib/api/strapi/service'
import { strapiGetArticles } from '@/lib/api/strapi/article'

vi.mock('@/lib/api/strapi/article', () => ({
  strapiGetArticles: vi.fn(),
}))

vi.mock('@/lib/api/strapi/service', () => ({
  strapiGetServices: vi.fn(),
}))

vi.stubGlobal('import.meta', {
  resolve: vi.fn().mockResolvedValue('../../build/server/index.js'),
})

vi.stubEnv('VITE_HOST_URL', 'http://localhost')

describe('loader', () => {
  const mockRequest = new Request('http://localhost/sitemap.xml')
  const mockSitemap = '<loc>http://localhost/</loc>'

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should call generateSitemap with correct parameters', async () => {
    const mockArticlesSlugs = {
      data: [{ slug: 'article-1', id: 1, title: '' }],
    }

    const mockServicesSlugs = {
      data: [{ slug: 'service-1', id: 1, title: '' }],
    }

    vi.mocked(strapiGetArticles).mockResolvedValueOnce(mockArticlesSlugs)
    vi.mocked(strapiGetServices).mockResolvedValueOnce(mockServicesSlugs)

    const response = await loader({ request: mockRequest } as never)

    expect(strapiGetArticles).toHaveBeenCalledWith(
      'fields[0]=slug',
      i18nConfig.fallbackLng
    )

    expect(strapiGetServices).toHaveBeenCalledWith(
      'fields[0]=slug',
      i18nConfig.fallbackLng
    )

    expect(response.status).toBe(200)
    const body = await response.text()
    expect(body).toContain(mockSitemap)
  })

  it('should handle errors gracefully', async () => {
    vi.mocked(strapiGetArticles).mockResolvedValueOnce({
      status: 500,
      statusText: 'Network Error',
      data: undefined,
    } as never)

    let response: Response | undefined = undefined
    try {
      await loader({ request: mockRequest } as never)
    } catch (ex: unknown) {
      response = ex as Response
    }
    expect(response).not.toBeNull()
    expect(response?.status).toBe(404)
  })
})
