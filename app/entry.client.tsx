import {
  init,
  browserTracingIntegration,
  replayIntegration,
} from '@sentry/remix'
import { RemixBrowser, useLocation, useMatches } from '@remix-run/react'
import { startTransition, useEffect } from 'react'
import { hydrateRoot } from 'react-dom/client'
import { i18nConfig } from '@/lib/server/translations/i18n.config'
import i18next from 'i18next'
import { I18nextProvider, initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import Backend from 'i18next-http-backend'
import { getInitialNamespaces } from 'remix-i18next/client'

if (import.meta.env.PROD) {
  init({
    dsn: import.meta.env.VITE_SENTRY_DNS,
    tracesSampleRate: 1,
    integrations: [
      browserTracingIntegration({
        useEffect,
        useLocation,
        useMatches,
      }),
      replayIntegration({
        maskAllText: true,
        blockAllMedia: true,
      }),
    ],
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1,
  })
}

async function hydrate() {
  try {
    // eslint-disable-next-line import/no-named-as-default-member
    await i18next
      .use(initReactI18next)
      .use(LanguageDetector)
      .use(Backend)
      .init({
        ...i18nConfig,
        ns: getInitialNamespaces(),
        backend: { loadPath: '/locales/{{lng}}/{{ns}}.json' },
        detection: {
          order: [
            'cookie',
            'localStorage',
            'sessionStorage',
            'navigator',
            'htmlTag',
            'path',
            'subdomain',
          ],
          lookupLocalStorage: 'lng',
        },
      })

    startTransition(() => {
      hydrateRoot(
        document,
        <I18nextProvider i18n={i18next}>
          <RemixBrowser />
        </I18nextProvider>
      )
    })
  } catch (error) {
    console.error('Error during i18next initialization or hydration:', error)
  }
}

if (window.requestIdleCallback) {
  window.requestIdleCallback(hydrate)
} else {
  window.setTimeout(hydrate, 1)
}
