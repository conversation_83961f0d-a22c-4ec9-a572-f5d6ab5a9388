import * as Sentry from '@sentry/remix'
import { PassThrough } from 'node:stream'
import type { AppLoadContext, EntryContext } from '@remix-run/node'
import { createReadableStreamFromReadable } from '@remix-run/node'
import { RemixServer } from '@remix-run/react'
import { isbot } from 'isbot'
import { renderToPipeableStream } from 'react-dom/server'
import { createInstance } from 'i18next'
import type { i18n } from 'i18next'
import { i18next } from '@/lib/server/translations/i18next.server'
import { I18nextProvider, initReactI18next } from 'react-i18next'
import Backend from 'i18next-fs-backend'
import { resolve } from 'node:path'
import { IsBotProvider } from './providers/is-bot'

export const handleError = Sentry.wrapHandleErrorWithSentry(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  (error, { request }) => {
    // Custom handleError implementation
  }
)

const ABORT_DELAY = 5000

// eslint-disable-next-line import/no-default-export
export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  loadContext: AppLoadContext
): Promise<Response> {
  const i18nInstance: i18n = await initializeI18n(request, remixContext)
  const isBotRequest: boolean = isbot(request.headers.get('user-agent') ?? '')
  return renderResponse(
    request,
    responseStatusCode,
    responseHeaders,
    remixContext,
    i18nInstance,
    isBotRequest
  )
}

const initializeI18n = async (
  request: Request,
  remixContext: EntryContext
): Promise<i18n> => {
  const i18nInstance: i18n = createInstance()
  const lng: string = await i18next.getLocale(request)
  const ns: string[] = i18next.getRouteNamespaces(remixContext)

  await i18nInstance
    .use(initReactI18next)
    .use(Backend)
    .init({
      lng,
      ns,
      backend: {
        loadPath: resolve('./public/locales/{{lng}}/{{ns}}.json'),
      },
      saveMissing: true,
    })

  return i18nInstance
}

const renderResponse = (
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext,
  i18n: i18n,
  isBot: boolean
): Promise<Response> => {
  return new Promise<Response>((resolve, reject) => {
    const renderOptions = {
      onAllReady() {
        if (isBot) {
          handleShellReady(
            resolve,
            responseHeaders,
            responseStatusCode,
            pipe,
            body
          )
        }
      },
      onShellReady() {
        if (!isBot) {
          handleShellReady(
            resolve,
            responseHeaders,
            responseStatusCode,
            pipe,
            body
          )
        }
      },
      onShellError: (error: unknown) => {
        reject(
          error instanceof Error
            ? error
            : new Error(`ShellError: ${String(error)}`)
        )
      },
      onError: (error: unknown) => {
        responseStatusCode = 500
        console.error(error)
        const wrappedError =
          error instanceof Error ? error : new Error(String(error))
        reject(wrappedError)
      },
    }

    const { pipe, abort } = renderToPipeableStream(
      <IsBotProvider isBot={isBot}>
        <I18nextProvider i18n={i18n}>
          <RemixServer
            context={remixContext}
            url={request.url}
            abortDelay={ABORT_DELAY}
          />
        </I18nextProvider>
      </IsBotProvider>,

      renderOptions
    )

    const body: PassThrough = new PassThrough()
    setTimeout(abort, ABORT_DELAY)
  })
}

const handleShellReady = (
  resolve: (response: Response) => void,
  responseHeaders: Headers,
  responseStatusCode: number,
  pipe: (stream: PassThrough) => void,
  body: PassThrough
): void => {
  const stream: ReadableStream = createReadableStreamFromReadable(body)
  responseHeaders.set('Content-Type', 'text/html')
  resolve(
    new Response(stream, {
      headers: responseHeaders,
      status: responseStatusCode,
    })
  )
  pipe(body)
}
