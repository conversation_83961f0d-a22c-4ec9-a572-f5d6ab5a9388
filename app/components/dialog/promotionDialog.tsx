import React, { ComponentPropsWithoutRef } from 'react'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { StrapiField } from '@/lib/api/types'
import { ImageType } from '@/lib/api/entity'
import { Button } from '../ui/button'
import { XMarkIcon } from '@heroicons/react/24/outline'

type PromotionDialogProps = {
  image: StrapiField<ImageType> | null | undefined
} & ComponentPropsWithoutRef<typeof DialogTrigger>

export const PromotionDialog = React.forwardRef<
  HTMLButtonElement,
  PromotionDialogProps
>(({ image, ...props }, ref) => {
  return (
    <Dialog>
      <DialogTrigger {...props} ref={ref}>
        <StrapiImage
          className="rounded-2xl shadow-lg  shadow-primary-400"
          value={image}
        />
      </DialogTrigger>
      <DialogContent
        className="max-w-[90dvw] max-h-[75dvh] aspect-square p-0 sm:w-auto h-auto border-none flex-auto bg-transparent"
        isHideCloseButton
      >
        <DialogTitle />
        <DialogDescription />
        <div className="w-full flex-1 h-full rounded-2xl overflow-clip">
          <StrapiImage className="object-cover flex-1" value={image} />
        </div>
        <DialogClose className="absolute -right-5 top-2" asChild>
          <Button variant="destructive" size="icon">
            <XMarkIcon className="size-10" />
          </Button>
        </DialogClose>
      </DialogContent>
    </Dialog>
  )
})
