import { Dialog, DialogClose, DialogContent } from '@/components/ui/dialog'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { Button } from '../ui/button'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { PopupPreview } from '@/lib/api/entity'
import { Link } from '@remix-run/react'
import { SOCIAL_MEDIA } from '@/constants/social'
import defaltButtonImage from '../../../public/assets/images/button-image.webp'

type PopupPreviewDialogProps = {
  data?: PopupPreview | null
}

export const PopupPreviewDialog = ({ data }: PopupPreviewDialogProps) => {
  const {
    isShow = false,
    isShowContactButton = true,
    image = null,
    buttonImage = null,
  } = data || {}

  if (!isShow) return null

  return (
    <Dialog key={data?.id} defaultOpen={isShow}>
      <DialogContent
        className="h-fit w-fit flex flex-col justify-center items-center p-4 border-none bg-transparent"
        isHideCloseButton
      >
        <DialogClose className="right-0 top-0 size-8 z-10 absolute" asChild>
          <Button variant="destructive" size="icon">
            <XMarkIcon className="size-10" />
          </Button>
        </DialogClose>
        <div className="min-w-72 rounded-3xl overflow-clip aspect-square">
          <StrapiImage className="object-cover" value={image} />
        </div>
        {isShowContactButton && (
          <Link to={SOCIAL_MEDIA.LINE}>
            {buttonImage?.data ? (
              <StrapiImage
                value={buttonImage}
                className="max-w-60 rounded-full object-cover"
              />
            ) : (
              <img
                src={defaltButtonImage}
                alt="contact us"
                className="max-w-60 rounded-full object-cover"
              />
            )}
          </Link>
        )}
      </DialogContent>
    </Dialog>
  )
}
