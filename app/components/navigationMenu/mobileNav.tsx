import {
  Sheet,
  She<PERSON><PERSON>lose,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON><PERSON>ger,
} from '@/components/ui/sheet'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/solid'
import { Link } from '@remix-run/react'
import { MenuAccordion } from '../accordion/menuAccordion'

import {
  AccordionContent,
  AccordionItem,
  AccordionRoot,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { SocialButtons } from '../socialButtons'
import { MainMenuEntity, MainMenuField } from '@/lib/api/entity'
import { AimsLogo } from '@/lib/assets/aims-logo'
import { useCallback, memo, useMemo } from 'react'

type MobileNavProps = {
  data: MainMenuEntity | null
}

const SheetHeader = memo(() => (
  <div className="sticky top-0 z-10 flex w-full justify-between bg-white border-b px-6 py-4">
    <SheetClose asChild>
      <Link className="max-w-fit flex-1 flex" to="/" aria-label="Go Home page">
        <AimsLogo className="h-20 text-primary" />
      </Link>
    </SheetClose>
    <SheetClose>
      <XMarkIcon className="h-6 w-6 text-primary" />
      <span className="sr-only">Close</span>
    </SheetClose>
  </div>
))

export default function MobileNav({ data }: Readonly<MobileNavProps>) {
  const renderSheetMenuList = useCallback((mainMenuItem: MainMenuField) => {
    const { name, subMenuLink, url } = mainMenuItem

    if (subMenuLink && subMenuLink.length > 0) {
      return (
        <AccordionRoot
          className="border-0 !mt-0"
          type="multiple"
          key={`mobile-nav-${name}`}
        >
          <AccordionItem
            className="pt-8"
            key={`nav-menu-${name}`}
            value={`menu-${name}`}
          >
            <AccordionTrigger
              className={cn(
                'px-4 py-2 text-secondary data-[state=open]:text-primary'
              )}
            >
              <p>{name}</p>
            </AccordionTrigger>
            <AccordionContent className="pb-0 pt-3">
              <MenuAccordion
                parentMenu={url ?? ''}
                menuList={subMenuLink ?? []}
              />
            </AccordionContent>
          </AccordionItem>
        </AccordionRoot>
      )
    }

    return (
      <SheetClose asChild key={`mobile-nav-${name}`}>
        <Button
          aria-label="Close navbar"
          asChild
          className="justify-start border-b w-full"
          radius="none"
          variant="linkSecondary"
        >
          <Link
            to={`${url ?? ''}`}
            className="text-left"
            aria-label="Close navbar"
          >
            {name}
          </Link>
        </Button>
      </SheetClose>
    )
  }, [])

  const menuItems = useMemo(() => data?.mainMenuItems ?? [], [data])

  return (
    <Sheet aria-label="Open mobile navbar">
      <SheetTrigger aria-label="Open mobile navbar trigger">
        <Bars3Icon className="size-6 shrink-0 lg:hidden text-primary" />
      </SheetTrigger>
      <SheetContent className="overflow-y-scroll p-0" isNoCloseButton>
        <SheetHeader />
        <div className="flex w-full flex-col space-y-8 p-6">
          {menuItems.map(renderSheetMenuList)}
          <SocialButtons className="flex space-x-4 self-center" />
        </div>
      </SheetContent>
    </Sheet>
  )
}
