import { Container } from '../container'
import { NavList } from './navList'
import { useTranslation } from 'react-i18next'
import { Button } from '@/components/ui/button'
import { useLocale } from 'remix-i18next/react'
import { Link, useLocation } from '@remix-run/react'
import MobileNav from './mobileNav'
import { SocialButtons } from '../socialButtons'
import { AimsLogo } from '@/lib/assets/aims-logo'
import { AppContext } from '@/providers/appContext'
import { useContext, useMemo } from 'react'
import { getLocaleParam, switchLanguage } from '@/utils/localeHelper'
import useClient from '@/hooks/use-client'

const Navbar = () => {
  const { mainMenu } = useContext(AppContext)
  const location = useLocation()
  const locale = useLocale()
  const { t } = useTranslation('navbar')
  const { isClient } = useClient()

  const toLanguage = useMemo(
    () => ({
      pathname: location.pathname,
      search: switchLanguage(locale),
    }),
    [locale, location.pathname]
  )

  const toHome = useMemo(
    () => ({
      pathname: '/',
      search: getLocaleParam(locale),
    }),
    [locale]
  )

  return (
    <nav className="sticky top-0 z-10 w-full bg-white py-2">
      <Container className="flex justify-between items-center">
        <div className="flex gap-x-2">
          <MobileNav data={mainMenu} />
          <Link
            className="max-w-fit flex-1 flex shrink-0 mr-2"
            to={toHome}
            aria-label="Go Home page by link"
          >
            <AimsLogo className="h-11 text-primary" />
          </Link>
        </div>

        <NavList data={mainMenu} />
        <div className="flex gap-2">
          <SocialButtons className="flex gap-2" />
          {isClient && (
            <Link to={toLanguage}>
              <Button
                variant="primary"
                size="icon"
                aria-label="Change Language of page"
              >
                {t('language')}
              </Button>
            </Link>
          )}
        </div>
      </Container>
    </nav>
  )
}

export default Navbar
