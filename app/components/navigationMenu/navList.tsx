import { memo, useCallback, forwardRef } from 'react'
import { cn } from '@/lib/utils'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
  NavigationMenuViewport,
} from '@/components/ui/navigation-menu'
import { Link, useLoaderData, useNavigate } from '@remix-run/react'
import { Button, buttonVariants } from '@/components/ui/button'
import { MainMenuEntity, MainMenuField, MenuField } from '@/lib/api/entity'
import { Play } from 'lucide-react'
import { getLocaleParam } from '@/utils/localeHelper'

const NavbarList = memo(
  ({ navTitle, href, locale }: Readonly<NavbarListProps>) => (
    <NavigationMenuItem>
      <NavigationMenuLink
        asChild
        className={cn(
          navigationMenuTriggerStyle(),
          buttonVariants({ radius: 'full', variant: 'ghost' }),
          'font-normal text-primary hover:!text-secondary-dark px-2 lg:px-4'
        )}
      >
        <Link
          to={{
            pathname: href,
            search: getLocaleParam(locale),
          }}
        >
          {navTitle}
        </Link>
      </NavigationMenuLink>
    </NavigationMenuItem>
  )
)

type NavbarListProps = {
  navTitle: string
  href: string
  locale: string
}

export function NavList({ data }: Readonly<NavListProps>) {
  const { locale } = useLoaderData<{ locale: string }>()
  const navigate = useNavigate()

  const renderMenuItem = useCallback(
    (mainMenuItem: MainMenuField) =>
      !mainMenuItem.subMenuLink?.length ? (
        <NavbarList
          key={`nav-bar-list-${mainMenuItem.name}`}
          navTitle={mainMenuItem.name ?? ''}
          href={mainMenuItem.url ?? ''}
          locale={locale}
        />
      ) : (
        <NavigationMenuItem key={`menu-item-${mainMenuItem.name}`}>
          <NavigationMenuTrigger
            className={cn(
              navigationMenuTriggerStyle(),
              buttonVariants({ radius: 'full', variant: 'ghost' }),
              'font-normal text-primary hover:!text-secondary-dark px-2 lg:px-4'
            )}
            onClick={() => {
              if (mainMenuItem.url)
                navigate(`${mainMenuItem.url}${getLocaleParam(locale)}`)
            }}
          >
            {mainMenuItem.name}
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="bg-primary-600 w-dvw py-8 flex flex-col gap-6">
              {mainMenuItem.url && (
                <Link
                  to={{
                    pathname: mainMenuItem.url,
                    search: getLocaleParam(locale),
                  }}
                >
                  <h2 className="text-white text-center">
                    {mainMenuItem.name}
                  </h2>
                </Link>
              )}
              <div className="grid sm:grid-cols-3 lg:grid-cols-4 pl-44 gap-6">
                {mainMenuItem.subMenuLink.map((subMenuitem) => (
                  <NavGroup
                    key={`nav-group-${subMenuitem.name}`}
                    title={subMenuitem.name ?? ''}
                    subMenu={subMenuitem.menu}
                    currentMenu={subMenuitem.url ?? ''}
                    parentMenu={mainMenuItem.url ?? ''}
                    locale={locale}
                  />
                ))}
              </div>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>
      ),
    [locale, navigate]
  )

  return (
    <NavigationMenu isViewportRelative={false} className="flex-1 max-lg:hidden">
      <NavigationMenuList className="w-full">
        {data?.mainMenuItems?.map(renderMenuItem)}
      </NavigationMenuList>
      <NavigationMenuViewport className="mt-3.5 border-none rounded-none" />
    </NavigationMenu>
  )
}

type NavListProps = {
  data: MainMenuEntity | null
}

type NavGroupProps = {
  parentMenu: string
  currentMenu: string
  title: string
  subMenu?: MenuField[]
  locale: string
}

const NavGroup = memo(
  ({ parentMenu, currentMenu, title, subMenu, locale }: NavGroupProps) => {
    return (
      <div className="flex flex-col gap-y-4 text-white w-80">
        <p className="px-3 py-2 rounded-full bg-white text-primary w-fit font-semibold">
          {!subMenu && currentMenu ? (
            <Link
              className="text-sm font-bold leading-none"
              to={{
                pathname: `${parentMenu}/${currentMenu}`,
                search: getLocaleParam(locale),
              }}
            >
              {title}
            </Link>
          ) : (
            title
          )}
        </p>
        {subMenu && (
          <div className="flex flex-col gap-2">
            {subMenu.map(({ name, url }) => (
              <div key={`nav-group-item-${name}`}>
                <NavigationMenuLink asChild>
                  <Link
                    className="text-sm font-medium leading-none"
                    to={{
                      pathname: `${parentMenu}/${url}`,
                      search: getLocaleParam(locale),
                    }}
                  >
                    <Button
                      aria-label={`Open ${name} page`}
                      className="text-white text-base font-medium flex gap-1"
                      size={'link'}
                      variant={'link'}
                    >
                      <Play className="text-white" fill="white" size={14} />
                      {name}
                    </Button>
                  </Link>
                </NavigationMenuLink>
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }
)

type ListItemProps = {
  className?: string
  title: string
  children: React.ReactNode
}

const ListItem = forwardRef<
  HTMLAnchorElement,
  React.ComponentPropsWithoutRef<typeof Link> & ListItemProps
>(({ className, title, children, ...props }, ref) => (
  <li>
    <NavigationMenuLink asChild>
      <Link
        ref={ref}
        className={cn(
          'block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground',
          className
        )}
        {...props}
      >
        <div className="text-sm font-medium leading-none">{title}</div>
        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
          {children}
        </p>
      </Link>
    </NavigationMenuLink>
  </li>
))

ListItem.displayName = 'ListItem'

export default ListItem
