import {
  AccordionContent,
  AccordionItem,
  AccordionRoot,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { SheetClose } from '@/components/ui/sheet'
import { cn } from '@/lib/utils'
import { Link, useLocation } from '@remix-run/react'
import { Button } from '@/components/ui/button'
import { SubMainMenuField } from '@/lib/api/entity'
import { useMemo, memo } from 'react'

export interface NavbarNavigationMenuProps {
  fullWidth?: boolean
  parentMenu: string
  menuList: SubMainMenuField[]
}

interface SubMenuItem {
  name: string | null
  url: string | null
}

const MemoizedAccordionItem: React.FC<{
  name: string | null
  menu: SubMenuItem[]
  parentMenu: string
  currentMenu?: string | null
  fullPath: string
}> = memo(({ name, menu, parentMenu, currentMenu, fullPath }) => {
  if (!name) return null
  if (!menu)
    return (
      <SheetClose className="ml-10 mt-5" asChild>
        <Button
          asChild
          className={cn('w-full justify-start !px-4 !py-2 text-secondary', {
            'text-primary': fullPath.includes(
              `${parentMenu}/${currentMenu ?? ''}`
            ),
          })}
          variant="link"
          size="link"
        >
          <Link to={`${parentMenu}/${currentMenu ?? ''}`} className="text-left">
            {name}
          </Link>
        </Button>
      </SheetClose>
    )

  return (
    <AccordionItem
      className="pt-8 pl-10"
      key={`nav-menu-${name}`}
      value={`menu-${name}`}
    >
      <AccordionTrigger
        className={cn(
          'px-4 py-2 text-secondary data-[state=open]:text-primary'
        )}
      >
        <p className="text-left">{name}</p>
      </AccordionTrigger>
      <AccordionContent className="pb-0 pt-3">
        <div className="flex w-full flex-col gap-3 rounded-md bg-neutral-white">
          {menu.map(({ name: subMenuName, url }) => (
            <MemoizedSubMenuButton
              key={`submenu-${subMenuName ?? ''}`}
              subMenuName={subMenuName ?? ''}
              url={url ?? ''}
              fullPath={fullPath}
              parentMenu={parentMenu}
            />
          ))}
        </div>
      </AccordionContent>
    </AccordionItem>
  )
})

const MemoizedSubMenuButton: React.FC<{
  subMenuName: string | null
  url?: string
  fullPath: string
  parentMenu: string
}> = memo(({ subMenuName, url, fullPath, parentMenu }) => {
  const displayName = subMenuName ?? ''

  return (
    <SheetClose className="ml-10" asChild>
      <Button
        asChild
        className={cn('w-full justify-start !px-4 !py-2 text-secondary', {
          'text-primary': fullPath.includes(`${parentMenu}/${url ?? ''}`),
        })}
        variant="link"
        size="link"
      >
        <Link to={`${parentMenu}/${url ?? ''}`} className="text-left">
          {displayName}
        </Link>
      </Button>
    </SheetClose>
  )
})

export const MenuAccordion: React.FC<NavbarNavigationMenuProps> = ({
  menuList,
  parentMenu,
}) => {
  const { pathname, search } = useLocation()

  const fullPath = useMemo(
    () => (search ? `${pathname}?${search}` : pathname),
    [pathname, search]
  )

  return (
    <AccordionRoot
      className="border-0 [&>div:first-child]:pt-0"
      type="multiple"
    >
      {menuList.map(({ name, menu, url }) => (
        <MemoizedAccordionItem
          key={name ?? 'unnamed'}
          name={name}
          currentMenu={url}
          menu={menu}
          parentMenu={parentMenu}
          fullPath={fullPath}
        />
      ))}
    </AccordionRoot>
  )
}
