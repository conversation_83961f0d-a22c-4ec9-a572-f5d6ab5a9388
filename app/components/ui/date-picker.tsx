import { useCallback, useState } from 'react'
import { format, startOfDay, subYears } from 'date-fns'
import { Calendar as IconCalendar } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '../ui/button'
import { Calendar } from '../ui/calendar'
import { FormControl } from '../ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover'

interface DatePickerProps {
  date: Date | undefined
  setDate: (date: Date | undefined) => void
  placeholder?: string
  includeTime?: boolean
  disabled?: (date: Date) => boolean
  formControl?: boolean
  className?: string
  isAutoClose?: boolean
  isLoading?: boolean

  fromYear?: number
  toYear?: number
  triggerDisable?: boolean
}

export default function DatePicker({
  date,
  setDate,
  placeholder,
  includeTime,
  disabled,
  formControl = true,
  className = '',
  isAutoClose = true,
  isLoading,
  triggerDisable,
  fromYear = Number(format(subYears(new Date(), 10), 'yyyy')), // 10 years ago
  toYear = Number(format(new Date(), 'yyyy')), // this year
}: DatePickerProps) {
  const [open, setOpen] = useState<boolean>(false)

  const onSelectDate = useCallback(
    (selectedDate: Date | undefined) => {
      if (isAutoClose && !includeTime) {
        setOpen(false)
      }
      setDate(
        selectedDate
          ? includeTime
            ? selectedDate
            : startOfDay(selectedDate)
          : undefined
      )
    },
    [includeTime, isAutoClose, setDate]
  )

  const buttonContent = (
    <Button
      variant="outline"
      className={cn(
        formControl ? 'w-full' : 'w-fit min-w-[150px]',
        'pl-3 text-left font-normal',
        className,
        !date && 'text-muted-foreground'
      )}
      disabled={triggerDisable}
      onClick={() => setOpen(true)}
    >
      {date ? (
        format(date, includeTime ? 'PP hh:mm:ss a' : 'PP')
      ) : (
        <span>
          {placeholder ?? (includeTime ? 'Pick a datetime' : 'Pick a date')}
        </span>
      )}
      <IconCalendar className="w-4 h-4 ml-auto opacity-50" />
    </Button>
  )

  return (
    <Popover open={open}>
      <PopoverTrigger asChild disabled={isLoading}>
        {formControl ? (
          <FormControl>{buttonContent}</FormControl>
        ) : (
          buttonContent
        )}
      </PopoverTrigger>
      <PopoverContent
        className="w-64 p-0 relative"
        align="start"
        onInteractOutside={() => setOpen(false)}
      >
        <Calendar
          mode="single"
          selected={date}
          onSelect={onSelectDate}
          disabled={disabled}
          fromYear={fromYear}
          captionLayout="dropdown-buttons"
          toYear={toYear}
          formControl={formControl}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  )
}
