import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/lib/utils'

const buttonVariants = cva(
  'inline-flex items-center transition-colors duration-300 justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      radius: {
        none: 'rounded-none',
        sm: 'rounded-sm',
        normal: 'rounded',
        md: 'rounded-md',
        lg: 'rounded-lg',
        full: 'rounded-full',
      },
      variant: {
        primary:
          'bg-primary dark:bg-white text-white font-semibold hover:bg-primary-800',
        destructive:
          'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline:
          'border border-input bg-background hover:text-accent-foreground',
        secondary: 'bg-secondary text-white hover:bg-secondary/80',
        tertiary: 'bg-white text-primary hover:text-primary-500',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline p-0 w-fit',
        linkSecondary:
          'text-secondary underline-offset-4 hover:underline p-0 w-fit',
        linkDark:
          'text-black underline-offset-4 hover:underline p-0 w-fit hover:text-primary',
      },
      size: {
        default: 'h-10 px-4 py-2 lg:px-8 lg:py-4',
        link: 'h-fit p-0',
        xs: 'h-6 px-2 py-1',
        sm: 'h-8 px-3 py-1',
        lg: 'h-11 px-8 py-4',
        icon: 'size-10',
        mediumIcon: 'size-14',
      },
    },
    defaultVariants: {
      radius: 'full',
      variant: 'primary',
      size: 'default',
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, radius, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button'
    return (
      <Comp
        className={cn(
          'cursor-pointer',
          buttonVariants({ variant, size, className, radius })
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = 'Button'

export { Button, buttonVariants }
