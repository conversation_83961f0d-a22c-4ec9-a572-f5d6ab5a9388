import React, { ComponentPropsWithRef } from 'react'
import { INTERNAL_IMAGE } from '@/constants/http'
import { remixImageLoader, Image } from 'remix-image'

type ImageCacheProps = ComponentPropsWithRef<'img'> &
  Omit<ComponentPropsWithRef<typeof Image>, 'unoptimized'>

export const ImageCache = React.forwardRef<HTMLImageElement, ImageCacheProps>(
  ({ src, alt, className, fetchPriority, ...props }, ref) => {
    if (!src) {
      return null
    }

    return (
      <Image
        src={src}
        alt={alt}
        ref={ref}
        className={className}
        loaderUrl={`/api/${INTERNAL_IMAGE}`}
        loader={remixImageLoader}
        {...(fetchPriority ? { fetchPriority } : {})} // Only pass if defined
        placeholder="blur"
        {...props}
        onError={({ currentTarget }) => {
          currentTarget.onerror = null
          currentTarget.src = '../assets/images/no-image-available.webp'
        }}
      />
    )
  }
)

ImageCache.displayName = 'ImageCache'
