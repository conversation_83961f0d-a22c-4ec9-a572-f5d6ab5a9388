import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { buttonVariants } from '../ui/button'
import { ComponentPropsWithoutRef } from 'react'
import { cn } from '@/lib/utils'

type Props = ComponentPropsWithoutRef<typeof Select> &
  ComponentPropsWithoutRef<typeof SelectTrigger> & {
    items: {
      text: string
      value: string
    }[]
    placeHolder?: string
  }

export function SelectDropdown({
  className,
  items,
  placeHolder = 'Select',
  ...props
}: Props) {
  return (
    <Select {...props}>
      <SelectTrigger className={cn(buttonVariants(), className)}>
        <SelectValue placeholder={placeHolder} />
      </SelectTrigger>
      <SelectContent className="bg-primary text-white">
        {items.map((option) => (
          <SelectItem
            key={`select-option-${option.value}`}
            value={option.value}
          >
            {option.text}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
