import { FC } from 'react'
import {
  useRouteError,
  isRouteErrorResponse,
  Meta,
  Links,
  Scripts,
  ErrorResponse,
} from '@remix-run/react'
import { ErrorPage } from '@/components/error/errorPage'
import { captureRemixErrorBoundaryError } from '@sentry/remix'
import { useTranslation } from 'react-i18next'
import { ERROR_CODE_404 } from '@/constants/error'
import { cn } from '@/lib/utils'

type ErrorBoundaryProps = {
  renderError?: (_error: unknown) => React.ReactNode
}

export const ErrorBoundary: FC<ErrorBoundaryProps> = ({ renderError }) => {
  const error = useRouteError()
  const { i18n } = useTranslation()

  const is404 = isRouteErrorResponse(error) && error.status === ERROR_CODE_404

  if (!is404 && isRouteErrorResponse(error)) {
    captureRemixErrorBoundaryError(error)
  }

  return (
    <html lang={i18n.language} dir={i18n.dir()} className="h-full">
      <head>
        <title>Aims Clinic - Error!</title>
        <Meta />
        <Links />
      </head>
      <body
        className={cn(
          'min-h-screen flex justify-center items-center text-center',
          is404 ? 'bg-primary-200' : 'bg-red-50'
        )}
      >
        <main className="animate-in fade-in zoom-in duration-500">
          {renderError ? (
            renderError(error)
          ) : (
            <ErrorPage error={error as ErrorResponse} />
          )}
        </main>
        <Scripts />
      </body>
    </html>
  )
}
