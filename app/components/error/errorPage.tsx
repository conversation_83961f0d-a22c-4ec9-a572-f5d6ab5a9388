/* eslint-disable jsx-a11y/anchor-is-valid */
import { But<PERSON> } from '@/components/ui/button'
import { isRouteErrorResponse, Link } from '@remix-run/react'
import { FC, useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { cn } from '@/lib/utils'
import { ArrowPathIcon, ClockIcon } from '@heroicons/react/24/solid'
import { ERROR_CODE_500, ERROR_CODE_404 } from '@/constants/error'
import { getLocaleParam } from '@/utils/localeHelper'
import { useLocale } from 'remix-i18next/react'

type ErrorPageProps = {
  error: unknown
}

export const ErrorPage: FC<ErrorPageProps> = ({ error }) => {
  const { t } = useTranslation('error')

  const locale = useLocale()

  let statusCode
  let message

  if (isRouteErrorResponse(error)) {
    statusCode = error.status
    message = error.statusText
  } else if (error instanceof Error) {
    statusCode = ERROR_CODE_500
    message = error.message
  } else {
    statusCode = ERROR_CODE_500
    message = ''
  }

  const toLanguage = useMemo(
    () => ({
      pathname: '/',
      search: getLocaleParam(locale),
    }),
    [locale]
  )

  const refreshPage = useCallback(
    (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
      e.preventDefault()
      window.location.reload()
    },
    []
  )

  const errorStyle = useMemo(
    () =>
      cn(
        'number',
        statusCode === ERROR_CODE_404 ? 'text-primary' : 'text-red-400'
      ),
    [statusCode]
  )
  const circleStyle = useMemo(
    () =>
      cn('circle', statusCode === ERROR_CODE_404 ? 'bg-primary' : 'bg-red-400'),
    [statusCode]
  )

  const statusCodeText = useMemo(() => statusCode.toString(), [statusCode])

  return (
    <div className="center">
      <div className="error">
        <div className={errorStyle}>{statusCodeText[0]}</div>
        <div className="illustration">
          <div className={circleStyle}></div>
          <div className="clip">
            <div className="paper">
              <div className="face">
                <div className="eyes">
                  <div className="eye eye-left"></div>
                  <div className="eye eye-right ml-12"></div>
                </div>
                <div className="rosyCheeks rosyCheeks-left"></div>
                <div className="rosyCheeks rosyCheeks-right"></div>
                <div className="mouth"></div>
              </div>
            </div>
          </div>
        </div>
        <div className={errorStyle}>{statusCodeText[2]}</div>
      </div>
      <div className="my-6 max-w-[500px]">
        {statusCode === ERROR_CODE_500 ? (
          <Alert variant="destructive">
            <AlertTitle className="text-2xl font-bold text-left">
              {t(`error.${statusCode}.title`)}
            </AlertTitle>
            <AlertDescription className="text-left py-3">
              <div>{t(`error.${statusCode}.meantime`)}:</div>
              <div className="flex mt-2 items-center">
                <a
                  href="#"
                  className="cursor-pointer"
                  role="button"
                  tabIndex={0}
                  onClick={refreshPage}
                >
                  <ArrowPathIcon className="size-6 mr-2" />
                </a>
                {t(`error.${statusCode}.refreshPage`)}
              </div>
              <div className="flex mt-2 items-center">
                <ClockIcon className="size-6 mr-2" />
                {t(`error.${statusCode}.waitMinutes`)}
              </div>
              {message && (
                <>
                  <hr className="h-1 w-3/5 mx-auto my-5 bg-red-300" />
                  <div dangerouslySetInnerHTML={{ __html: message }} />
                </>
              )}
            </AlertDescription>
          </Alert>
        ) : (
          <span
            className={cn(
              'text-2xl',
              statusCode !== ERROR_CODE_404 && 'bg-red-400'
            )}
          >
            {t(`error.${statusCode}`)}
          </span>
        )}
      </div>
      <Link to={toLanguage}>
        <Button
          variant={statusCode === ERROR_CODE_500 ? 'destructive' : undefined}
          aria-label="Change Language of page"
        >
          {t('btn.backHome')}
        </Button>
      </Link>
    </div>
  )
}
