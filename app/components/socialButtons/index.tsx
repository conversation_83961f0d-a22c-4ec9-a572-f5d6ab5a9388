import { Button } from '@/components/ui/button'
import { Link } from '@remix-run/react'
import { LANGUAGE, mappingLanguage } from '@/constants/language'
import { useLocale } from 'remix-i18next/react'
import {
  memo,
  FC,
  useCallback,
  useState,
  ComponentPropsWithoutRef,
} from 'react'
import { strapiGetSocialMedia } from '@/lib/api/strapi/social-media'
import { queryStringBuilder } from '@/utils/fetchHelper'
import { warperSingleType } from '@/utils/strapiHelper'
import useClientTask from '@/hooks/use-client-task'
import { SocialMediaEntity } from '@/lib/api/entity'
import { cn } from '@/lib/utils'

interface SocialButtonProps
  extends Omit<ComponentPropsWithoutRef<typeof Link>, 'to'> {
  className: string
  href: string
  label: string
  isBigIcon?: boolean
}

type SocialList = 'facebook' | 'line' | 'instagram' | 'tiktok'

export const SocialButton: FC<SocialButtonProps> = memo(
  ({ className, href, isBigIcon, label, ...props }) => {
    return (
      <Link
        className="max-lg:hidden"
        to={href}
        aria-label={`${label} by link`}
        {...props}
      >
        <Button
          variant="outline"
          size={isBigIcon ? 'mediumIcon' : 'icon'}
          aria-label={`${label} by button`}
        >
          <i className={className} />
        </Button>
      </Link>
    )
  }
)

interface SocialButtonsProps {
  buttonClassName?: string
  className?: string
  isBigIcon?: boolean
}

export const SocialButtons: FC<SocialButtonsProps> = memo(
  ({ buttonClassName = '', className = '', isBigIcon }) => {
    const locale = useLocale()
    const [dataSocialMedia, setDataSocialMedia] = useState<SocialMediaEntity>()

    const fetchSocialMedia = useCallback(async () => {
      const response = await strapiGetSocialMedia(
        queryStringBuilder(['socials']),
        locale
      )
      setDataSocialMedia(warperSingleType(response) ?? {})
    }, [locale])

    const getIconByType = useCallback(
      (type: SocialList) => {
        const iconSize = isBigIcon ? 'text-3xl' : 'text-xl'
        switch (type) {
          case 'facebook':
            return 'ri-facebook-fill text-primary ' + iconSize
          case 'instagram':
            return 'ri-instagram-fill text-primary ' + iconSize
          case 'line':
            return 'ri-line-fill text-primary ' + iconSize
          case 'tiktok':
            return 'ri-tiktok-fill text-primary ' + iconSize
          default:
            return ''
        }
      },
      [isBigIcon]
    )

    const getUrlByType = useCallback(
      (type: SocialList, url: string) => {
        const mappedTH = mappingLanguage[LANGUAGE.THA]
        const mappedEN = mappingLanguage[LANGUAGE.ENG]

        switch (type) {
          case 'instagram':
            return `${url}/?hl=${locale === mappedEN ? mappedEN : mappedTH}`
          case 'tiktok':
            return `${url}?lang=${locale === mappedEN ? mappedEN : mappedTH}`
          default:
            return url
        }
      },
      [locale]
    )

    const { isTaskPending } = useClientTask(fetchSocialMedia)

    if (isTaskPending) return null

    return (
      <div className={className}>
        {dataSocialMedia?.socials?.map((social) => (
          <SocialButton
            key={`social-${social.type}-button`}
            href={getUrlByType(social.type as SocialList, social.url)}
            className={cn(
              getIconByType(social.type as SocialList),
              buttonClassName
            )}
            isBigIcon={isBigIcon}
            label={`Visit ${social.type}`}
          />
        ))}
      </div>
    )
  }
)
