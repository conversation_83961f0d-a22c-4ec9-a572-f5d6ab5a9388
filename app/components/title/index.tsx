import { cn } from '@/lib/utils'
import React, { forwardRef } from 'react'

interface TitleProps extends React.ComponentPropsWithoutRef<'div'> {
  className?: string
  title?: string
  subTitle?: string
  caption?: string
  lineStyle?: string
  isCaptionBelow?: boolean
  rawHtml?: TrustedHTML
}

export const Title = forwardRef<HTMLDivElement, TitleProps>(
  (
    {
      className,
      title,
      subTitle,
      caption,
      lineStyle,
      isCaptionBelow,
      rawHtml,
      ...props
    },
    ref
  ) => {
    return (
      <div ref={ref} className={cn('text-center', className)} {...props}>
        {!isCaptionBelow && (
          <h2 className={cn('text-primary font-normal', className)}>
            {caption}
          </h2>
        )}
        {rawHtml ? (
          <div
            className={cn('text-primary ck ck-content', className)}
            dangerouslySetInnerHTML={{ __html: rawHtml }}
          />
        ) : (
          <>
            <h1 className={cn('text-primary', className)}>{title}</h1>
            <h2 className={cn('text-primary', className)}>{subTitle}</h2>
          </>
        )}

        {isCaptionBelow && (
          <h2 className={cn('text-primary font-normal', className)}>
            {caption}
          </h2>
        )}
        <hr className={cn('h-1 mx-auto my-5 bg-primary', lineStyle)} />
      </div>
    )
  }
)
