import { cn } from '@/lib/utils'
import { forwardRef } from 'react'

interface HeartBeatLoaderProps extends React.ComponentPropsWithoutRef<'div'> {
  className?: string
}

export const HeartBeatLoader = forwardRef<HTMLDivElement, HeartBeatLoaderProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'heartbeatloader mt-10 mb-32 animate-in fade-in zoom-in duration-500',
          className
        )}
        {...props}
      >
        <svg
          className="svgdraw"
          width="100%"
          height="100%"
          viewBox="0 0 150 400"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            className="path"
            d="M 0 200 l 40 0 l 5 -40 l 5 40 l 10 0 l 5 15 l 10 -140 l 10 220 l 5 -95 l 10 0 l 5 20 l 5 -20 l 30 0"
            fill="transparent"
            strokeWidth="4"
            stroke="black"
          ></path>
        </svg>
        <div className="innercircle"></div>
        <div className="outercircle"></div>
        <h1 className="text-primary text-nowrap -m-28 -ml-24 mt-24">
          AIMS Clinic
        </h1>
      </div>
    )
  }
)
