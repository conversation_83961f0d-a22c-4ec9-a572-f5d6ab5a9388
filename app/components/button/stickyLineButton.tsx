import { But<PERSON> } from '@/components/ui/button'
import { SOCIAL_MEDIA } from '@/constants/social'
import { cn } from '@/lib/utils'
import { AppContext } from '@/providers/appContext'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { Link } from '@remix-run/react'
import { RiLineFill, RiPhoneFill } from '@remixicon/react'
import { useContext, useState } from 'react'

export default function StickyLineButton() {
  const { home } = useContext(AppContext)
  const [isHide, setIsHide] = useState<boolean>(false)
  return (
    <>
      {/* Desktop Button */}
      <Button
        asChild
        className={cn(
          'fixed right-4 bottom-4 bg-white text-primary justify-between size-auto !py-2 !px-4 hover:bg-white shadow-lg ring-1 ring-primary-800',
          'hidden md:flex',
          {
            'opacity-0 pointer-events-none': isHide,
          }
        )}
      >
        <Link target="_blank" to={SOCIAL_MEDIA.LINE}>
          <span className="rounded-full bg-primary p-1 shrink-0 size-12 flex items-center justify-center">
            <RiLineFill className="size-10 shrink-0 text-white" />
          </span>
          <div className="flex flex-col text-center">
            <div
              className="ml-1 ck ck-content"
              dangerouslySetInnerHTML={{ __html: home?.sticky ?? '' }}
            />
          </div>
          <Button
            aria-label="Close Sticky Line Button"
            className="absolute right-0 -top-1/3 size-8 border-4 border-white shadow-md ring-1 ring-primary-200"
            onClick={(e) => {
              e.preventDefault()
              setIsHide((prev) => !prev)
            }}
            size="icon"
          >
            <XMarkIcon className="size-4 " />
          </Button>
        </Link>
      </Button>

      {/* Mobile Button */}
      <div className="fixed right-4 bottom-4 flex flex-col gap-2 md:hidden">
        <Button
          asChild
          size="icon"
          className="bg-primary-500 p-2 w-fit h-fit hover:primary-500 shadow-lg"
        >
          <Link to={SOCIAL_MEDIA.PHONE}>
            <RiPhoneFill className="size-10 text-white" />
          </Link>
        </Button>

        <Button
          asChild
          size="icon"
          className="bg-primary-500 p-2 w-fit h-fit hover:primary-500 shadow-lg"
        >
          <Link to={SOCIAL_MEDIA.LINE}>
            <RiLineFill className="size-10 text-white" />
          </Link>
        </Button>
      </div>
    </>
  )
}
