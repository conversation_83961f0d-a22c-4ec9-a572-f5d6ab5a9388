import { Seo } from '@/lib/server/seo/types'
import { Helmet } from 'react-helmet-async'

type HelmetSEOProps = {
  data?: Seo | null
}

const HelmetSEO: React.FC<HelmetSEOProps> = ({ data }) => {
  if (!data?.structuredData) return null
  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(data.structuredData)}
      </script>
    </Helmet>
  )
}

export { HelmetSEO }
