import { Container } from '@/components/container'
import { Link, useLoaderData } from '@remix-run/react'
import { Button } from '@/components/ui/button'
import { SocialButtons } from '../socialButtons'
import {
  MainMenuEntity,
  MainMenuField,
  SubMainMenuField,
} from '@/lib/api/entity'
import { AimsLogo } from '@/lib/assets/aims-logo'
import { Badge } from '../ui/badge'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'
import { Fragment } from 'react/jsx-runtime'
import { ImageCache } from '../image-cache'
import { AppContext } from '@/providers/appContext'
import { useContext, useMemo } from 'react'
import { SOCIAL_MEDIA } from '@/constants/social'
import { getLocaleParam } from '@/utils/localeHelper'
import { StrapiImage } from '../strapi/strapi-image'

type FooterMenuProps = {
  data: MainMenuEntity | null
  locale: string
}

const FooterMenu = ({ data, locale }: FooterMenuProps) => {
  if (!data?.mainMenuItems?.length) return null

  const renderSubMenu = (item: SubMainMenuField, parentMenu: string | null) => {
    if (item.menu) {
      return item.menu.map((itemMenu) => (
        <Link
          key={`footer-menu-${parentMenu}/${itemMenu.url ?? ''}`}
          className="text-white"
          to={{
            pathname: `${parentMenu}/${itemMenu.url ?? ''}`,
            search: getLocaleParam(locale),
          }}
        >
          {itemMenu.name}
        </Link>
      ))
    }

    return null
  }

  const renderMenu = (item: MainMenuField) => {
    if (!item.showFooter) return null

    return (
      <Fragment key={`footer-menu-${item.url ?? ''}`}>
        <Link
          className={cn(
            'w-fit cursor-pointer',
            item?.subMenuLink && item.subMenuLink.length > 0
              ? 'col-span-full'
              : ''
          )}
          to={{
            pathname: item.url ?? '',
            search: getLocaleParam(locale),
          }}
        >
          <Badge className="text-xl" variant="white">
            {item.name}
          </Badge>
        </Link>
        {!!item.subMenuLink?.length && (
          <div className="grid col-span-full grid-cols-2 sm:grid-cols-1 lg:grid-cols-2 gap-3 grid-auto-rows-max">
            {item.subMenuLink?.map((itemMenu) =>
              renderSubMenu(itemMenu, item.url)
            )}
          </div>
        )}
      </Fragment>
    )
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-1 lg:grid-cols-2 gap-3 grid-auto-rows-max my-2">
      {data.mainMenuItems?.map(renderMenu)}
    </div>
  )
}

export default function Footer() {
  const { locale } = useLoaderData<{ locale: string }>()
  const { mainMenu, home } = useContext(AppContext)
  const { t } = useTranslation('footer')

  const toHome = useMemo(
    () => ({
      pathname: '/',
      search: getLocaleParam(locale),
    }),
    [locale]
  )
  return (
    <footer className="p-6 bg-primary-600 lg:min-h-80 h-auto">
      <Container className="h-full grid max-sm:grid-rows-3 sm:grid-cols-3 gap-14">
        <div className="flex flex-col max-sm:items-center h-full gap-y-4">
          <Link
            className="max-w-fit flex self-center"
            to={toHome}
            aria-label="Go Home page by link"
          >
            <AimsLogo className="w-full text-white " />
          </Link>
          <SocialButtons className="flex gap-10 justify-center" isBigIcon />
          <div className="aspect-ratio-container">
            <ImageCache
              decoding="async"
              loading="lazy"
              alt="footer-banner"
              fetchPriority="high"
              className="aspect-video rounded-xl overflow-clip"
              src="../assets/images/bg-contact-us.webp"
            />
          </div>
        </div>
        <FooterMenu data={mainMenu} locale={locale} />
        <div className="flex flex-col gap-8 text-white">
          <Button
            asChild
            className="overflow-clip max-w-full aspect-[1569/485] h-fit p-0 max-h-32"
            size="lg"
          >
            <Link to={SOCIAL_MEDIA.LINE} aria-label="Visit Line">
              <div className="aspect-ratio-container-3">
                {home?.contactButton?.data ? (
                  <StrapiImage
                    value={home?.contactButton}
                    className="w-full rounded-full object-cover"
                  />
                ) : (
                  <ImageCache
                    decoding="async"
                    loading="lazy"
                    alt="footer-line-button"
                    fetchPriority="high"
                    src="../assets/images/social/line-footer.webp"
                  />
                )}
              </div>
            </Link>
          </Button>
          <div className="w-full flex flex-col gap-4">
            <p className="text-xl font-semibold underline underline-offset-2">
              {t('contactUs')}
            </p>
            <p className="text-base">{t('open')}</p>
            <p className="text-base">{t('location')}</p>
            <p className="text-base">{t('tel')}</p>
          </div>
        </div>
      </Container>
    </footer>
  )
}
