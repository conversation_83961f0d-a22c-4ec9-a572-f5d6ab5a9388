import { cn } from '@/lib/utils'
import { forwardRef } from 'react'

interface ContainerProps extends React.ComponentPropsWithoutRef<'div'> {
  noMargin?: boolean
  noPadding?: boolean
}

export const Container = forwardRef<HTMLDivElement, ContainerProps>(
  ({ children, className, noMargin, noPadding }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'container px-6 lg:px-24',
          {
            'py-0': noPadding,
            'mx-auto': !noMargin,
            'm-0': noMargin,
          },
          className
        )}
      >
        {children}
      </div>
    )
  }
)
