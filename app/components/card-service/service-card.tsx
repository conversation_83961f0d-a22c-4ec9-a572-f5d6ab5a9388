import { ServiceCardItem } from '../../routes/our-service_'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { Link } from '@remix-run/react'
import { cn } from '@/lib/utils'
import { getLocaleParam } from '@/utils/localeHelper'
import { ChevronRight } from 'lucide-react'

interface ServiceCardProps {
  data: ServiceCardItem
  locale: string
}

const ServicesCard: React.FC<ServiceCardProps> = ({ data, locale }) => {
  return (
    <Link
      to={{
        pathname: encodeURIComponent(data.link ?? ''),
        search: getLocaleParam(locale),
      }}
      className="bg-lemon-200 flex flex-col md:flex-row rounded-xl md:rounded-[50px] md:shadow-[1px 1px 1px 1px grey] w-4/5 max-h-44 duration-300 hover:scale-105"
    >
      <div className="relative overflow-hidden bg-brown-200 shadow-xl md:shadow-[1px 1px 1px 1px grey] text-white flex flex-col justify-center items-center text-start rounded-xl md:rounded-[50px] h-40 md:h-auto max-w-80">
        {data.image?.data ? (
          <StrapiImage
            className="w-fit h-full object-cover"
            value={data.image}
          />
        ) : (
          <h2>{data.name}</h2>
        )}
      </div>

      <div
        className={cn(
          'w-full justify-between items-center p-4 md:w-[65%] md:m-0 md:pl-12 pr-4 md:py-6 text-white flex-1 h-44 hidden md:flex',
          data.description ?? 'justify-content-end'
        )}
      >
        {data.description && (
          <div
            className="h-full w-11/12 flex flex-col !items-start line-clamp-4 text-force ck ck-content"
            dangerouslySetInnerHTML={{ __html: data.description }}
          />
        )}
        <ChevronRight className="size-8 text-white" />
      </div>
    </Link>
  )
}

export { ServicesCard }
