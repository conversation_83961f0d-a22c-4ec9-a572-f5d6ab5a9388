import { cn } from '@/lib/utils'
import { forwardRef } from 'react'

interface LoaderProps extends React.ComponentPropsWithoutRef<'div'> {
  className?: string
}

export const Loader = forwardRef<HTMLDivElement, LoaderProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('animate-in fade-in zoom-in duration-500', className)}
        {...props}
      >
        <div className="load-wrapp">
          <div className="load-1">
            <div className="line"></div>
            <div className="line"></div>
            <div className="line"></div>
          </div>
        </div>
      </div>
    )
  }
)
