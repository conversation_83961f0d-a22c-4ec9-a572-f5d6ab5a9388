import { But<PERSON> } from '@/components/ui/button'
import { useTranslation } from 'react-i18next'
import { FC, useContext } from 'react'
import { cn } from '@/lib/utils'
import { Link } from '@remix-run/react'
import { AimsLogoText } from '@/lib/assets/aims-logo-text'
import { getLocaleParam } from '@/utils/localeHelper'
import { LANGUAGE } from '@/constants/language'
import { AppContext } from '@/providers/appContext'

type CoverBannerProps = {
  className?: string
  locale: string
}

const CoverBanner: FC<CoverBannerProps> = ({ className, locale }) => {
  const { t } = useTranslation('home')
  const { home } = useContext(AppContext)

  return (
    <div
      className={cn(
        className,
        'absolute md:flex flex-col md:gap-1 lg:gap-6 xl:gap-8 p-4 md:p-8 lg:p-16 w-1/2 md:w-1/2 xl:w-2/5 h-full rounded-r-full bg-primary-700 z-0 top-0 text-white justify-center'
      )}
    >
      <div className="-ml-10 sm:ml-0 -mb-10 md:-mb-12 -mt-7 sm:-mt-12 md:-mt-16 -mx-4 w-[270px] min-[375px]:w-80 min-[500px]:w-96 sm:w-64 ">
        <AimsLogoText />
      </div>
      <h1 className="hidden sm:flex">{t('banner.aimsClinic')}</h1>
      <div className="hidden sm:flex flex-col gap-2 md:gap-4 xl:gap-8">
        {home?.banner && (
          <div
            className={cn(
              '*:font-light *:md:font-medium *:text-sm *:md:text-xl *:lg:text-2xl *:xl:text-3xl ck ck-content',
              {
                '*:font-english': locale === LANGUAGE.ENG,
                '*:font-thai': locale === LANGUAGE.THA,
              }
            )}
            dangerouslySetInnerHTML={{ __html: home.banner }}
          />
        )}
        <p className="font-light text-md xl:text-2xl">{t('banner.location')}</p>
      </div>
      <div className="hidden sm:flex gap-4 mt-5 lg:mt-2 ">
        <Link
          to={{
            pathname: '/about-us',
            search: getLocaleParam(locale),
          }}
          aria-label="Learn more about us"
        >
          <Button
            className="font-bold"
            variant="secondary"
            aria-label="Learn more about us"
          >
            {t('banner.contactUs')}
          </Button>
        </Link>
        <Link
          to={{
            pathname: '/our-service',
            search: getLocaleParam(locale),
          }}
          aria-label="Explore our services"
        >
          <Button
            className="font-bold"
            variant="secondary"
            aria-label="Explore our services"
          >
            {t('banner.ourService')}
          </Button>
        </Link>
      </div>
    </div>
  )
}

export default CoverBanner
