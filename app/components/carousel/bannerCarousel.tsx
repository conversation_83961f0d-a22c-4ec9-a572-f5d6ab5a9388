import {
  Carousel,
  CarouselContent,
  Carousel<PERSON>tem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { ImageType } from '@/lib/api/entity'
import { cn } from '@/lib/utils'
import Autoplay from 'embla-carousel-autoplay'
import { StrapiImage } from '../strapi/strapi-image'

type BannerCarouselProps = {
  images?: ImageType[]
  carouselKey: string
  className?: string
}

export default function BannerCarousel({
  images,
  className,
  carouselKey,
}: Readonly<BannerCarouselProps>) {
  return (
    <div className={cn('w-full -z-10', className)}>
      <Carousel
        opts={{ loop: true }}
        className="w-full flex-1"
        plugins={[
          Autoplay({
            stopOnInteraction: false,
            stopOnMouseEnter: true,
            delay: 3000,
          }),
        ]}
      >
        <CarouselContent className="flex-1 h-fit">
          {images?.map((item) => (
            <CarouselItem key={`${carouselKey}-${item.name}`}>
              <StrapiImage
                value={item}
                className="w-full h-fit"
                fetchPriority="high"
              />
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="left-12 hidden" />
        <CarouselNext className="right-12 hidden" />
      </Carousel>
    </div>
  )
}
