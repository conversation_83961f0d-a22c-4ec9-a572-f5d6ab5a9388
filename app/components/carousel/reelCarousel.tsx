import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { InstagramEmbed } from 'react-social-media-embed'
import { Container } from '../container'
import useClient from '@/hooks/use-client'
import { Loader } from '../loader'

export function ReelsCarousel({ reels }: Readonly<{ reels: string[] }>) {
  const { isClient } = useClient()
  return (
    <Container className="flex flex-col gap-16 lg:gap-24 py-8 lg:py-16">
      <Carousel className="w-full" opts={{ align: 'start' }}>
        <CarouselContent>
          {reels.map((reel) => (
            <CarouselItem
              className="lg:basis-1/2 xl:basis-1/3"
              key={`reel-${reel}`}
            >
              {isClient ? (
                <InstagramEmbed
                  className="bg-transparent -mt-24 aspect-[3/4]"
                  url={reel}
                  width="100%"
                />
              ) : (
                <Loader />
              )}
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="text-primary" />
        <CarouselNext className="text-primary" />
      </Carousel>
    </Container>
  )
}
