import { cn } from '@/lib/utils'
import { ComponentPropsWithoutRef, forwardRef, ReactNode } from 'react'
import { Container } from '../container'
import { Title } from '../title'
import { Carousel, CarouselContent, CarouselItem } from '../ui/carousel'
import Autoplay from 'embla-carousel-autoplay'
import { StrapiFields } from '@/lib/api/types'
import { ImageType } from '@/lib/api/entity'
import { StrapiImage } from '../strapi/strapi-image'

type ContentCardProps = ComponentPropsWithoutRef<'div'> & {
  content?: string
  img: StrapiFields<ImageType> | null
  isImageLeft?: boolean
  title?: string
  subTitle?: string
  children?: ReactNode
}

export const ContentCard = forwardRef<HTMLDivElement, ContentCardProps>(
  (
    { className, content, img, isImageLeft = true, title, subTitle, children },
    ref
  ) => {
    return (
      <div
        className={cn('w-full bg-primary-600 py-8 lg:py-12', className)}
        ref={ref}
      >
        <Container
          className={cn(
            'flex flex-col lg:flex-row items-center gap-10 lg:gap-6',
            { 'flex-col-reverse': !isImageLeft },
            className
          )}
        >
          {/* Image */}
          {img?.data?.length && (
            <div
              className={cn('flex-1 relative', {
                'order-last': !isImageLeft,
              })}
            >
              <Carousel
                className="w-full"
                opts={{ loop: true }}
                plugins={[
                  Autoplay({
                    stopOnInteraction: false,
                    stopOnMouseEnter: true,
                    delay: 3000,
                  }),
                ]}
                isShowDots={img?.data?.length > 1}
              >
                <CarouselContent>
                  {img?.data.map((item) => (
                    <CarouselItem
                      key={`content-card-${item.id}-${item.attributes?.name}-${item.attributes?.mime}`}
                      className="aspect-[1/1]"
                    >
                      <StrapiImage
                        className="rounded-3xl object-cover bg-primary-200 w-full aspect-square"
                        value={item.attributes}
                      />
                    </CarouselItem>
                  ))}
                </CarouselContent>
              </Carousel>
            </div>
          )}
          {/* Text*/}
          <div
            className={cn(
              ' h-full w-full lg:w-3/5 flex flex-col gap-0 lg:gap-8 justify-center items-center text-white text-center',
              className,
              {
                'pr-0 lg:pr-16': !isImageLeft,
                'pl-0 lg:pl-16': isImageLeft,
              }
            )}
          >
            <Title
              title={title}
              caption={subTitle}
              lineStyle="hidden"
              className={cn(
                'flex flex-col gap-1 lg:gap-4 text-white',
                className
              )}
            />
            {content?.length && <p className="text-normal">{content}</p>}
            {children}
          </div>
        </Container>
      </div>
    )
  }
)
