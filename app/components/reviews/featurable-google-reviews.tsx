import { ReactGoogleReviews } from 'react-google-reviews'
import { useTranslation } from 'react-i18next'

type FeaturableGoogleReviewsProps = {
  id: string
}

const FeaturableGoogleReviews: React.FC<FeaturableGoogleReviewsProps> = ({
  id,
}) => {
  const { t } = useTranslation('common')
  if (!id) {
    return
  }
  return (
    <ReactGoogleReviews
      readMoreLabel={t('googleReviews.readMore')}
      readLessLabel={t('googleReviews.readLess')}
      layout="carousel"
      reviewVariant="card"
      nameDisplay="firstAndLastInitials"
      logoVariant="none"
      showDots={false}
      featurableId={id}
    />
  )
}

export { FeaturableGoogleReviews }
