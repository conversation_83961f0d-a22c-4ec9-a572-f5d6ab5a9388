type StatusState = {
  isSubmitting: boolean
  successMessage: string | null
  errorMessage: string | null
}

type StatusAction =
  | { type: 'SUBMIT_START' }
  | { type: 'SUBMIT_SUCCESS'; message: string }
  | { type: 'SUBMIT_ERROR'; error: string }

export const initialState: StatusState = {
  isSubmitting: false,
  successMessage: null,
  errorMessage: null,
}

export const statusReducer = (
  state: StatusState,
  action: StatusAction
): StatusState => {
  switch (action.type) {
    case 'SUBMIT_START':
      return { isSubmitting: true, successMessage: null, errorMessage: null }
    case 'SUBMIT_SUCCESS':
      return {
        isSubmitting: false,
        successMessage: action.message,
        errorMessage: null,
      }
    case 'SUBMIT_ERROR':
      return {
        isSubmitting: false,
        successMessage: null,
        errorMessage: action.error,
      }
    default:
      return state
  }
}
