import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form'
import { useForm, useWatch } from 'react-hook-form'
import { valibotResolver } from '@hookform/resolvers/valibot'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { useEffect, useReducer, useState } from 'react'
import { InternalApi } from '@/lib/api/internalApi'
import { INTERNAL_SEND_EMAIL } from '@/constants/http'
import { useTranslation } from 'react-i18next'
import DatePicker from '@/components/ui/date-picker'
import { format, subYears } from 'date-fns'
import {
  ProfileRegistrationFormSchema,
  ProfileRegistrationFormType,
} from '@/validation/schema/profileRegistrationForm.schema'
import { ProfileField } from '@/components/form/profileRegistration/profileField'
import { ProfileCheckboxGrid } from './profileCheckboxGrid'
import {
  strapiCreateCustomers,
  strapiGetCustomers,
} from '@/lib/api/strapi/customers'
import { queryStringBuilder } from '@/utils/fetchHelper'
import { profileRegistrationTemplate } from '@/emailTemplates/profileRegistration.template'
import { initialState, statusReducer } from '@/components/form/status'
import {
  reasonOptions,
  sourceOptions,
} from '@/components/form/profileRegistration/profileOption'

export const ProfileRegistrationForm = ({
  services,
  onSuccess,
}: {
  services: { label: string; value: string }[]
  onSuccess: () => void
}) => {
  const { t, i18n } = useTranslation('profile-registration')
  const [status, dispatch] = useReducer(statusReducer, initialState)
  const [formSchema, setFormSchema] = useState(() =>
    ProfileRegistrationFormSchema(t)
  )
  const [otherUsedServices, setOtherUsedServices] = useState('')
  const [otherWantedServices, setOtherWantedServices] = useState('')

  const internalApi = new InternalApi()

  const form = useForm<ProfileRegistrationFormType>({
    resolver: valibotResolver(formSchema),
    defaultValues: {
      address: '',
      age: '' as never,
      birthDate: subYears(new Date(), 18),
      email: '',
      firstName: '',
      lastName: '',
      gender: '',
      message: '',
      mobileNo: '' as never,
      nickName: '',
      usedServices: Object.fromEntries(services.map((s) => [s.value, false])),
      wantedServices: Object.fromEntries(services.map((s) => [s.value, false])),
      sources: Object.fromEntries(sourceOptions.map((s) => [s, false])),
      reasons: Object.fromEntries(reasonOptions.map((r) => [r, false])),
    },
  })

  useWatch({ control: form.control, name: 'usedServices' })
  useWatch({
    control: form.control,
    name: 'wantedServices',
  })

  const getSelectedKeysAsString = (
    data: Record<string, boolean>,
    other?: string
  ) => {
    const selected = Object.keys(data)
      .filter((key) => data[key])
      .map((key) =>
        key === 'other' ? (other ? `other[${other}]` : 'other') : key
      )
    return selected.join(', ')
  }

  const onSubmit = async (data: ProfileRegistrationFormType) => {
    dispatch({ type: 'SUBMIT_START' })
    try {
      const findEmail = await strapiGetCustomers(
        queryStringBuilder([], {
          email: { $eqi: data.email },
        })
      )

      if ((findEmail?.data ?? []).length > 0) {
        dispatch({
          type: 'SUBMIT_ERROR',
          error: t('form.email.validate.duplicate', { email: data.email }),
        })
        return
      }

      const createPayload = {
        data: {
          ...data,
          usedServices: getSelectedKeysAsString(
            data.usedServices,
            otherUsedServices
          ),
          wantedServices: getSelectedKeysAsString(
            data.wantedServices,
            otherWantedServices
          ),
          sources: getSelectedKeysAsString(data.sources),
          reasons: getSelectedKeysAsString(data.reasons),
        },
      }

      await strapiCreateCustomers(createPayload)
      const emailPayload = {
        data: {
          subject: 'Customer Profile Information',
          content: profileRegistrationTemplate(createPayload.data),
        },
      }
      await internalApi.post<ResponseType>(INTERNAL_SEND_EMAIL, emailPayload)
      onSuccess()
    } catch {
      dispatch({
        type: 'SUBMIT_ERROR',
        error: t('form.error'),
      })
    }
  }

  useEffect(() => {
    setFormSchema(ProfileRegistrationFormSchema(t))
    form.clearErrors()
  }, [form, i18n.language, t])

  return (
    <Form {...form}>
      <form
        className="grid grid-cols-12 gap-4 py-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <ProfileField
          name="firstName"
          placeholder={t('form.firstName.label')}
          control={form.control}
          layout="half"
          required
        />
        <ProfileField
          name="lastName"
          placeholder={t('form.lastName.label')}
          control={form.control}
          layout="half"
          required
        />
        <ProfileField
          name="nickName"
          placeholder={t('form.nickName.label')}
          control={form.control}
          layout="half"
          required
        />
        <ProfileField
          name="gender"
          placeholder={t('form.gender.label')}
          control={form.control}
          layout="half"
          required
        />

        <FormField
          control={form.control}
          name="birthDate"
          render={({ field }) => (
            <FormItem className="col-span-6 flex flex-col animate-in fade-in duration-500 ">
              <FormLabel required>{t('form.birthDate.label')}</FormLabel>
              <FormControl>
                <DatePicker
                  date={field.value}
                  setDate={field.onChange}
                  fromYear={Number(format(subYears(new Date(), 65), 'yyyy'))}
                  className="rounded-xl"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <ProfileField
          name="age"
          placeholder={t('form.age.label')}
          control={form.control}
          layout="half"
          type="number"
          positiveNumber
          required
        />
        <ProfileField
          name="address"
          placeholder={t('form.address.label')}
          control={form.control}
          component={Textarea}
          required
        />
        <ProfileField
          name="mobileNo"
          placeholder={t('form.mobileNo.label')}
          control={form.control}
          layout="half"
          type="tel"
          required
        />
        <ProfileField
          name="email"
          placeholder={t('form.email.label')}
          control={form.control}
          layout="half"
          required
        />

        <ProfileCheckboxGrid
          control={form.control}
          name="usedServices"
          options={services.map((s) => s.value)}
          label={t('form.usedService.label')}
          otherValue={otherUsedServices}
          setOtherValue={setOtherUsedServices}
          required
        />

        <ProfileCheckboxGrid
          control={form.control}
          name="wantedServices"
          options={services.map((s) => s.value)}
          label={t('form.wantedService.label')}
          otherValue={otherWantedServices}
          setOtherValue={setOtherWantedServices}
          required
        />

        <ProfileCheckboxGrid
          control={form.control}
          name="sources"
          options={sourceOptions}
          label={t('form.source.label')}
          i18nPath="form.source.options"
          required
        />

        <ProfileCheckboxGrid
          control={form.control}
          name="reasons"
          options={reasonOptions}
          label={t('form.reason.label')}
          i18nPath="form.reason.options"
          required
        />

        <ProfileField
          name="message"
          placeholder={t('form.message.label')}
          control={form.control}
          component={Textarea}
        />

        <div className="col-span-full flex flex-col gap-2 fade-in duration-500 animate-in">
          {status.successMessage && (
            <p className="text-green-600 text-sm">{status.successMessage}</p>
          )}
          {status.errorMessage && (
            <p className="text-red-600 text-sm">{status.errorMessage}</p>
          )}
          <Button
            type="submit"
            disabled={
              status.isSubmitting ||
              Object.keys(form.formState.errors).length > 0
            }
            className="bg-primary text-white rounded-xl py-2 px-4 hover:bg-primary-400 min-w-0 w-auto"
          >
            {status.isSubmitting ? t('form.sending') : t('form.send')}
          </Button>
        </div>
      </form>
    </Form>
  )
}
