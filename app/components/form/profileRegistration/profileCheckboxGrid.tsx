import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Control, FieldValues, Path } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { useMemo } from 'react'

type ProfileCheckboxGridProps<TFieldValues extends FieldValues> = {
  control: Control<TFieldValues>
  name: Path<TFieldValues>
  options: string[]
  label: string
  i18nPath?: string
  otherValue?: string
  setOtherValue?: (v: string) => void
  required?: boolean
}

export function ProfileCheckboxGrid<TFieldValues extends FieldValues>({
  control,
  name,
  options,
  label,
  i18nPath,
  otherValue,
  setOtherValue,
  required = false,
}: ProfileCheckboxGridProps<TFieldValues>) {
  const { t } = useTranslation('profile-registration')
  const rules = useMemo(() => {
    return {
      validate: (value: Record<string, boolean>) => {
        const hasChecked = Object.values(value).some(Boolean)
        const otherChecked = value['other']

        if (required && !hasChecked) {
          return t(`form.${name}.validate.required`)
        }

        if (otherChecked && otherValue?.trim() === '') {
          return t(`form.${name}.validate.otherRequired`)
        }

        return true
      },
    }
  }, [required, name, t, otherValue])

  return (
    <FormField
      control={control}
      name={name}
      rules={rules}
      render={({ field }) => (
        <FormItem className="col-span-full space-y-3 ">
          <FormLabel
            className="fade-in duration-500 animate-in"
            required={required}
          >
            {label}
          </FormLabel>
          <FormControl>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              {options.map((option) => (
                <FormItem
                  key={option}
                  className="flex items-center space-x-3 space-y-0 fade-in slide-in-from-left-10 duration-500 animate-in"
                >
                  <FormControl>
                    <Checkbox
                      checked={field.value[option]}
                      onCheckedChange={(checked) => {
                        field.onChange({
                          ...field.value,
                          [option]: checked,
                        })
                      }}
                    />
                  </FormControl>
                  <FormLabel className="font-normal capitalize hover:cursor-pointer">
                    {i18nPath ? t(`${i18nPath}.${option}`) : option}
                  </FormLabel>
                </FormItem>
              ))}
              {field.value['other'] &&
                otherValue !== undefined &&
                setOtherValue && (
                  <Textarea
                    className="col-span-full resize-none"
                    value={otherValue}
                    onChange={(e) => setOtherValue(e.target.value)}
                  />
                )}
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
