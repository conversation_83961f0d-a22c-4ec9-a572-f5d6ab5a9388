import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { cn } from '@/lib/utils'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Control } from 'react-hook-form'
import { ProfileRegistrationFormType } from '@/validation/schema/profileRegistrationForm.schema'

type ProfileFieldProps = {
  name: Exclude<
    keyof ProfileRegistrationFormType,
    'birthDate' | 'usedServices' | 'wantedServices' | 'sources' | 'reasons'
  >
  placeholder: string
  component?: typeof Input | typeof Textarea
  control: Control<ProfileRegistrationFormType>
  layout?: 'full' | 'half' | 'quarter'
  required?: boolean
  type?: React.HTMLInputTypeAttribute
  positiveNumber?: boolean
}

export const ProfileField = ({
  name,
  placeholder,
  control,
  component: Component = Input,
  layout = 'full',
  required = false,
  type = 'text',
  positiveNumber = false,
}: ProfileFieldProps) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem
          key={name}
          className={cn('flex flex-col fade-in duration-500 animate-in', {
            'col-span-full': layout === 'full',
            'sm:col-span-6 col-span-full': layout === 'half',
            'sm:col-span-3 col-span-full': layout === 'quarter',
          })}
        >
          <FormLabel required={required}>{placeholder}</FormLabel>
          <FormControl>
            <Component
              {...field}
              type={type}
              className="form-input flex-auto"
              placeholder={placeholder}
              inputMode={
                type === 'number' || positiveNumber ? 'numeric' : undefined
              }
              onInput={(e) => {
                if (positiveNumber) {
                  const input = e.currentTarget
                  input.value = input.value.replace(/[^0-9]/g, '')
                }
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
