import { useForm } from 'react-hook-form'
import { valibotResolver } from '@hookform/resolvers/valibot'
import { useEffect, useReducer, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { Form } from '@/components/ui/form'
import { Button } from '@/components/ui/button'
import { ContactUsField } from './contactUsField'
import { initialState, statusReducer } from '@/components/form/status'
import { InternalApi } from '@/lib/api/internalApi'
import { INTERNAL_SEND_EMAIL } from '@/constants/http'
import {
  ContactUsFormSchema,
  ContactUsFormType,
} from '@/validation/schema/contactUsForm.schema'
import { contactUsTemplate } from '@/emailTemplates/contactUs.template'
import { strapiCreateCustomerContacts } from '@/lib/api/strapi/customerContact'

type ResponseType = { data: unknown; error?: ErrorType }
type ErrorType = { message: string; name: string; statusCode: number }

export const ContactUsForm = () => {
  const { t, i18n } = useTranslation('contact-us')
  const [status, dispatch] = useReducer(statusReducer, initialState)
  const internalApi = new InternalApi()

  const [formSchema, setFormSchema] = useState(() => ContactUsFormSchema(t))

  const form = useForm<ContactUsFormType>({
    resolver: valibotResolver(formSchema),
    defaultValues: { name: '', email: '', message: '' },
  })

  useEffect(() => {
    setFormSchema(ContactUsFormSchema(t))
    form.clearErrors()
  }, [form, i18n.language, t])

  const onSubmit = async (data: ContactUsFormType) => {
    dispatch({ type: 'SUBMIT_START' })
    try {
      const createPayload = {
        data,
      }

      await strapiCreateCustomerContacts(createPayload)

      const emailPayload = {
        data: {
          subject: 'New Contact Message',
          content: contactUsTemplate(createPayload.data),
        },
      }
      await internalApi.post<ResponseType>(INTERNAL_SEND_EMAIL, emailPayload)

      dispatch({
        type: 'SUBMIT_SUCCESS',
        message: t('form.success'),
      })
      form.reset()
    } catch {
      dispatch({
        type: 'SUBMIT_ERROR',
        error: t('form.error'),
      })
    }
  }

  return (
    <Form {...form}>
      <form
        className="flex flex-col gap-4 py-4"
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <ContactUsField
          name="name"
          placeholder={t('form.name.placeHolder')}
          control={form.control}
          required
        />

        <ContactUsField
          name="email"
          placeholder={t('form.email.placeHolder')}
          control={form.control}
          required
          type="email"
        />

        <ContactUsField
          name="message"
          placeholder={t('form.message.placeHolder')}
          control={form.control}
          required
          component="textarea"
        />

        {status.successMessage && (
          <p className="text-green-600 text-sm">{status.successMessage}</p>
        )}
        {status.errorMessage && (
          <p className="text-red-600 text-sm">{status.errorMessage}</p>
        )}

        <Button
          type="submit"
          disabled={status.isSubmitting}
          className="bg-primary text-white rounded-xl py-2 px-4 hover:bg-primary-400"
        >
          {status.isSubmitting ? t('email.sending') : t('email.send')}
        </Button>
      </form>
    </Form>
  )
}
