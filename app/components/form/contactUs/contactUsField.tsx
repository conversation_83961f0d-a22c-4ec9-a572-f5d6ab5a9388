import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { ContactUsFormType } from '@/validation/schema/contactUsForm.schema'
import { Control } from 'react-hook-form'

type ContactUsFieldProps = {
  name: keyof ContactUsFormType
  placeholder: string
  control: Control<ContactUsFormType>
  component?: 'input' | 'textarea'
  required?: boolean
  type?: React.HTMLInputTypeAttribute
  positiveNumber?: boolean
}

export const ContactUsField = ({
  name,
  placeholder,
  control,
  component = 'input',
}: ContactUsFieldProps) => {
  const Component = component === 'input' ? Input : Textarea
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem key={name} className="flex flex-col">
          <FormControl>
            <Component
              className="form-input"
              placeholder={placeholder}
              {...field}
              value={
                field.value as string | number | readonly string[] | undefined
              }
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
