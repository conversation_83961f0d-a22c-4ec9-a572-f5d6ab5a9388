import { MapComponent } from '@/lib/api/entity'
import { useCallback, useEffect, useMemo, useRef } from 'react'

interface LongdoMapType {
  Map: new (config: {
    placeholder: HTMLDivElement | null
    language: string
    zoom: number
  }) => LongdoMapInstance
  Marker: new (
    location: { lon: number; lat: number },
    options: { title: string; detail: string }
  ) => MarkerInstance
  Popup: new (
    location: { lon: number; lat: number },
    options: { html: string }
  ) => PopupInstance
}

interface LongdoMapInstance {
  Overlays: {
    add: (overlay: MarkerInstance | PopupInstance) => void
    bounce: (marker: MarkerInstance) => void
  }
  location: (location: { lon: number; lat: number }, animate: boolean) => void
  zoom: (level: number, animate: boolean) => void
}

type MarkerInstance = object
type PopupInstance = object

declare global {
  interface Window {
    longdo: LongdoMapType
  }
}

interface LongdoMapProps {
  id: string
  className?: string
  data: MapComponent
  locale: string
  callback?: () => void
}

export const LongdoMap: React.FC<LongdoMapProps> = ({
  id,
  className,
  callback,
  locale,
  data,
}) => {
  const mapContainerRef = useRef<HTMLDivElement | null>(null)
  const mapRef = useRef<LongdoMapInstance | null>(null)
  const longdoRef = useRef<LongdoMapType | null>(null)

  const html = useMemo(() => {
    return `
      <div class='w-80 bg-background p-3 rounded-xl shadow-xl shadow-primary-400'>
        <h4>${data.title}</h4>
        <p class='my-2'>${data.address}</p>
        <a href="${data.link}" target="_blank">${data.linkDetail}</a>
      </div>
    `.trim()
  }, [data.title, data.address, data.link, data.linkDetail])

  const initializeMap = useCallback(() => {
    const {
      title,
      detail,
      markerLon,
      markerLat,
      locationLat,
      locationLon,
      zoomLevel,
      centerLat,
      centerLon,
    } = data

    const longdo = window.longdo
    if (!longdo || !mapContainerRef.current) return

    const map = new longdo.Map({
      placeholder: mapContainerRef.current,
      language: locale,
      zoom: zoomLevel ?? 12,
    })

    const marker = new longdo.Marker(
      { lon: markerLon ?? 0, lat: markerLat ?? 0 },
      { title: title ?? '', detail: detail ?? '' }
    )

    const popup = new longdo.Popup(
      { lon: centerLon ?? 0, lat: centerLat ?? 0 },
      { html }
    )

    map.location({ lon: locationLon ?? 0, lat: locationLat ?? 0 }, true)
    map.zoom(zoomLevel ?? 12, true)

    if (map.Overlays) {
      map.Overlays.add(popup)
      map.Overlays.bounce(marker)
    }

    mapRef.current = map
    longdoRef.current = longdo
  }, [data, locale, html])

  useEffect(() => {
    const scriptId = 'longdoMapScript'
    let script = document.getElementById(scriptId) as HTMLScriptElement

    if (!script) {
      script = document.createElement('script')
      script.src = `https://api.longdo.com/map/?key=${
        import.meta.env.VITE_LONG_DO_MAP_API_TOKEN
      }`
      script.id = scriptId
      script.async = true
      script.defer = true
      document.body.appendChild(script)

      script.onload = () => {
        initializeMap()
        if (callback) callback()
      }
    } else {
      initializeMap()
      if (callback) callback()
    }

    return () => {
      mapRef.current = null
      longdoRef.current = null
    }
  }, [callback, initializeMap])

  return (
    <div
      id={id}
      className={className}
      ref={mapContainerRef}
      style={{ width: '100%', height: '100%' }}
    />
  )
}
