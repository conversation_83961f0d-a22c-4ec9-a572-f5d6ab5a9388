import { Container } from '@/components'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { LayoutAlignment } from '@/enums/layoutAlignment'
import { ContentMultiMedia, ImageType } from '@/lib/api/entity'
import { StrapiFields } from '@/lib/api/types'
import { cn } from '@/lib/utils'
import React, { FC, useMemo } from 'react'
import { Carousel, CarouselContent, CarouselItem } from '../ui/carousel'
import Autoplay from 'embla-carousel-autoplay'
import { Card, CardContent } from '@/components/ui/card'
import useClient from '@/hooks/use-client'
import { Loader } from '../loader'

type StrapiContentMultiMediaProps = {
  data: ContentMultiMedia | null
  imageClassName?: string
  className?: string
  contentChild?: React.ReactNode
  clientRender?: boolean
}

type MediaCarouselProps = {
  media: StrapiFields<ImageType> | null | undefined
  className?: string
  clientRender?: boolean
}

type ContentProps = {
  content: TrustedHTML | null | undefined
  className?: string
  contentChild?: React.ReactNode
}

const MediaCarousel: FC<MediaCarouselProps> = ({
  media,
  className,
  clientRender = false,
}) => {
  const { isClient } = useClient()
  const isRender = useMemo(
    () => (clientRender ? isClient : true),
    [clientRender, isClient]
  )

  if (!media?.data) return null

  return (
    <div className={cn(className, 'flex-1 animate-in duration-500 md:pr-10')}>
      <Carousel
        isShowDots
        className="w-full h-full"
        dotsClassName="!ring-white [&.active]:!bg-white"
        plugins={[
          Autoplay({
            stopOnInteraction: false,
            stopOnMouseEnter: true,
            delay: 3000,
          }),
        ]}
      >
        <CarouselContent>
          {media.data.map((item) => (
            <CarouselItem key={item.id}>
              <Card className="bg-transparent border-none rounded-[40px] md:rounded-[80px] ">
                <CardContent className="flex items-center justify-center p-0 aspect-square ">
                  {item.attributes &&
                    (isRender ? (
                      <StrapiImage
                        className="rounded-[40px] md:rounded-[80px] object-cover w-full h-full"
                        value={item.attributes}
                      />
                    ) : (
                      <Loader />
                    ))}
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
    </div>
  )
}

const Content: FC<ContentProps> = ({ content, className, contentChild }) => {
  if (!content) return null

  return (
    <div
      className={cn(
        className,
        'flex-1 flex flex-col space-y-5 gap-y-2 py-4 animate-in duration-500'
      )}
    >
      <div dangerouslySetInnerHTML={{ __html: content }} />
      {contentChild}
    </div>
  )
}

const Layout: FC<StrapiContentMultiMediaProps> = ({
  data,
  contentChild,
  className,
  clientRender,
}) => {
  if (!data) return null

  return data?.alignment === LayoutAlignment.Left ? (
    <>
      <MediaCarousel
        clientRender={clientRender}
        className="slide-in-from-left self-center"
        media={data?.media}
      />
      <Content
        className={cn(className, 'slide-in-from-right')}
        content={data?.content}
        contentChild={contentChild}
      />
    </>
  ) : (
    <>
      <Content
        className="slide-in-from-left"
        content={data?.content}
        contentChild={contentChild}
      />
      <MediaCarousel
        clientRender={clientRender}
        className={cn(className, 'slide-in-from-right self-center')}
        media={data?.media}
      />
    </>
  )
}

export const StrapiContentMultiMedia: FC<StrapiContentMultiMediaProps> = ({
  data,
  imageClassName,
  className,
  contentChild,
  clientRender,
}) => {
  if (!data) return null

  return (
    <div className={className}>
      <Container className="flex flex-col md:flex-row relative w-full justify-between gap-6 py-12 text-white">
        <div className="w-full flex flex-col lg:flex-row gap-20 items-center lg:items-start justify-between">
          <Layout
            clientRender={clientRender}
            data={data}
            imageClassName={imageClassName}
            contentChild={contentChild}
          />
        </div>
      </Container>
    </div>
  )
}
