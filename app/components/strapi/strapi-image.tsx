import { FC, Fragment, ReactElement } from 'react'
import { MimeType } from 'remix-image'
import { cn } from '@/lib/utils'
import { ImageBody, ImageType } from '@/lib/api/entity'
import { <PERSON>rapiField } from '@/lib/api/types'
import { ImageCache } from '../image-cache'

type Priority = 'high' | 'low' | 'auto'

type StrapiImageProps = {
  value:
    | StrapiField<ImageType>
    | ImageType
    | Array<StrapiField<ImageType> | ImageType>
    | null
    | undefined
  className?: string
  classNameFallBack?: string
  fetchPriority?: Priority
}

type ImageSize = {
  key: string
  max: number
}

const imageExtension = import.meta.env.VITE_IMAGE_EXTENSION || 'avif'

const sizes: ImageSize[] = [
  { key: 'xs', max: 300 },
  { key: 'sm', max: 768 },
  { key: 'md', max: 1280 },
  { key: 'lg', max: 1920 },
  { key: 'xl', max: 2840 },
]

const getImageAttributes = (
  data: StrapiField<ImageType> | ImageType
): ImageType | null => {
  if (!data) return null
  return 'formats' in data ? data : data?.data?.attributes ?? null
}

const renderImage = (
  data: StrapiField<ImageType> | ImageType,
  className?: string,
  classNameFallBack?: string,
  fetchPriority: Priority = 'auto'
): ReactElement => {
  const attributes = getImageAttributes(data)
  if (!attributes) return <div className={cn(className, classNameFallBack)} />

  const { formats, url, alternativeText, hash } = attributes
  const original = formats['original']
  const fallbackImageUrl = sizes.reduce<string | null>(
    (acc, { key }) => acc ?? formats?.[key]?.url ?? null,
    url ?? null
  )

  const altText = alternativeText ?? 'A image empty'
  const prefixUrl = import.meta.env.DEV ? import.meta.env.VITE_STRAPI_URL : ''
  const getFullUrl = (imgUrl: string | null): string =>
    imgUrl?.startsWith('http') ? imgUrl : `${prefixUrl}${imgUrl}`

  const { width, height } = attributes

  const filterFormat = Object.keys(formats)
    .filter((key) =>
      original
        ? key.endsWith(`_${imageExtension}`) && key.startsWith('original')
        : true
    )
    .map((key) => formats[key] as ImageBody)

  const generateSrcset = (filterFormat) => {
    return filterFormat
      .map((image) => `${image.url} ${image.width}w`)
      .join(', ')
  }

  return (
    <picture
      key={hash}
      className={cn(`aspect-[${width}/${height}]`, className)}
    >
      {filterFormat.map((item) => {
        const imgUrl = item.url ?? fallbackImageUrl
        const mime = item?.mime ?? MimeType.JPEG
        return fallbackImageUrl || item.url ? (
          <source
            key={`strapi-image-${item.name}-${item.mime}-max-${item.width}`}
            media={`(max-width: ${item.width}px)`}
            srcSet={getFullUrl(imgUrl)}
            type={mime}
          />
        ) : null
      })}
      <ImageCache
        decoding="async"
        loading="eager"
        fetchPriority={fetchPriority}
        src={getFullUrl(fallbackImageUrl)}
        srcSet={generateSrcset(filterFormat)}
        alt={altText}
        className={cn(`aspect-[${width}/${height}]`, className)}
        width={width}
        height={height}
      />
    </picture>
  )
}

export const StrapiImage: FC<StrapiImageProps> = ({
  value,
  className,
  classNameFallBack,
  fetchPriority,
}) => {
  const imageFallBack = classNameFallBack ?? 'bg-no-image size-60'
  if (!value) return <div className={cn(className, imageFallBack)} />

  return Array.isArray(value) ? (
    <>
      {value.map((item) => (
        <Fragment
          key={`strapi-image-${(item as StrapiField<ImageType>).data?.attributes
            ?.name}`}
        >
          {renderImage(item, className, imageFallBack, fetchPriority)}
        </Fragment>
      ))}
    </>
  ) : (
    renderImage(value, className, imageFallBack, fetchPriority)
  )
}
