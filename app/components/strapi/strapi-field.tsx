import { Field } from '@/lib/api/entity'
import { createElement, FC, useMemo } from 'react'
import { ImageCache } from '../image-cache'
import { cn } from '@/lib/utils'

type StrapiFieldProps = {
  value: Field[] | Field | null
  className?: string
}

const headingLevels = {
  1: 'text-4xl md:text-5xl font-bold',
  2: 'text-3xl md:text-4xl font-bold',
  3: 'text-2xl md:text-3xl',
  4: 'text-xl md:text-2xl',
  5: 'text-lg md:text-xl',
  6: 'text-base md:text-lg',
}

const renderField = (
  item: Field,
  prefixUrl: string,
  className: string | undefined
) => {
  switch (item.type) {
    case 'heading':
      return item.children.map((child) => {
        const level =
          item.level && item.level >= 1 && item.level <= 6 ? item.level : 6 // Ensure the level is between 1 and 6
        return (
          <div key={`strapi-field-${item.type}-${child.text}`}>
            {createElement(
              `h${level}`,
              { className: headingLevels[level] },
              child.text
            )}
          </div>
        )
      })

    case 'paragraph':
      return item.children.map((child) => (
        <p
          key={`strapi-field-${item.type}-${child.text}`}
          className={cn(className, 'text-base md:text-lg')}
        >
          {child.text}
        </p>
      ))

    case 'ul':
      return (
        <ul
          key={`strapi-field-${item.type}`}
          className="list-disc list-inside ml-5"
        >
          {item.children.map((listItem) => (
            <li key={`strapi-field-${listItem.text}`}>{listItem.text}</li>
          ))}
        </ul>
      )

    case 'ol':
      return (
        <ol
          key={`strapi-field-${item.type}`}
          className="list-decimal list-inside ml-5"
        >
          {item.children.map((listItem) => (
            <li key={`strapi-field-${listItem.text}`}>{listItem.text}</li>
          ))}
        </ol>
      )

    case 'image':
      return (
        <div key={`strapi-field-${item.type}`} className="my-4">
          <ImageCache
            decoding="async"
            loading="eager"
            className="max-w-full h-auto rounded"
            srcSet={`${prefixUrl}${item.src ?? ''}`}
            alt={item.alt || 'Image'}
          />
        </div>
      )

    case 'quote':
      return (
        <blockquote
          key={`strapi-field-${item.type}`}
          className="border-l-4 border-primary pl-4 italic"
        >
          {item.children.map((child) => (
            <p
              key={`strapi-field-${item.type}-${child.text}`}
              className="text-base md:text-lg"
            >
              {child.text}
            </p>
          ))}
        </blockquote>
      )

    case 'code':
      return (
        <pre
          key={`strapi-field-${item.type}`}
          className="bg-gray-800 text-white p-4 rounded-md overflow-auto"
        >
          <code>{item.text}</code>
        </pre>
      )

    case 'link':
      return (
        <a
          key={`strapi-field-${item.type}`}
          href={item.href}
          className="text-blue-500 underline"
        >
          {item.text}
        </a>
      )

    case 'video':
      return (
        <div key={`strapi-field-${item.type}`} className="my-4">
          <iframe
            width="100%"
            height="400"
            src={`${prefixUrl}${item.src ?? ''}`}
            title={item.title ?? 'Video'}
            allowFullScreen
          ></iframe>
        </div>
      )

    default:
      return null
  }
}

export const StrapiField: FC<StrapiFieldProps> = ({ value, className }) => {
  const prefixUrl = useMemo(
    () => (import.meta.env.DEV ? import.meta.env.VITE_STRAPI_URL : ''),
    []
  )

  if (!value) return null

  if (Array.isArray(value)) {
    return <>{value.map((item) => renderField(item, prefixUrl, className))}</>
  }

  return <>{renderField(value, prefixUrl, className)}</>
}
