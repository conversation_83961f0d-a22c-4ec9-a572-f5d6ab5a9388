import { Container } from '@/components'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { LayoutAlignment } from '@/enums/layoutAlignment'
import { MediaStyle } from '@/enums/mediaStyle'
import { ContentMedia, ImageType } from '@/lib/api/entity'
import { StrapiField } from '@/lib/api/types'
import { cn } from '@/lib/utils'
import { FC } from 'react'

type StrapiContentMediaProps = {
  data: ContentMedia | null
  imageClassName?: string
  className?: string
}

type MediaProps = {
  media: StrapiField<ImageType> | null | undefined
  className?: string
  imageClassName?: string
  mediaStyle: MediaStyle | null | undefined
}

type ContentProps = {
  content: TrustedHTML | null | undefined
  className?: string
}

const Media: FC<MediaProps> = ({
  media,
  className,
  imageClassName,
  mediaStyle,
}) => {
  if (!media?.data) return null
  let mediaClassName

  if (mediaStyle === MediaStyle.Circle) {
    mediaClassName = 'rounded-full w-4/5 aspect-square'
  } else if (mediaStyle === MediaStyle.Round) {
    mediaClassName = 'rounded-3xl'
  } else {
    mediaClassName =
      'rounded-full w-[300px] h-[300px] md:w-[400px] md:h-[400px] lg:w-[500px] lg:h-[500px]'
  }

  return (
    <div className={cn(className, 'flex-1 animate-in duration-500 md:pr-10')}>
      <StrapiImage
        className={cn(imageClassName, mediaClassName)}
        value={media}
      />
    </div>
  )
}

const Content: FC<ContentProps> = ({ content, className }) => {
  if (!content) return null

  return (
    <div
      className={cn(
        className,
        'flex-1 flex flex-col space-y-5 gap-y-2 py-4 animate-in duration-500'
      )}
    >
      <div
        className="ck ck-content"
        dangerouslySetInnerHTML={{ __html: content }}
      />
    </div>
  )
}

const Layout: FC<StrapiContentMediaProps> = ({
  data,
  imageClassName,
  className,
}) => {
  if (!data) return null

  return data?.alignment === LayoutAlignment.Left ? (
    <>
      <Media
        className={cn(className, 'slide-in-from-left self-center text-center')}
        imageClassName={imageClassName}
        media={data?.media}
        mediaStyle={data.mediaStyle}
      />
      <Content className="slide-in-from-right" content={data?.content} />
    </>
  ) : (
    <>
      <Content className="slide-in-from-left" content={data?.content} />
      <Media
        className={cn(className, 'slide-in-from-right self-center text-center')}
        imageClassName={imageClassName}
        media={data?.media}
        mediaStyle={data?.mediaStyle}
      />
    </>
  )
}

export const StrapiContentMedia: FC<StrapiContentMediaProps> = ({
  data,
  imageClassName,
  className,
}) => {
  if (!data) return null

  return (
    <div className={className}>
      <Container className="flex flex-col md:flex-row relative w-full justify-between gap-6 py-4 lg:py-12">
        <div className="w-full flex flex-col lg:flex-row gap-20 items-center lg:items-start justify-between">
          <Layout data={data} imageClassName={imageClassName} />
        </div>
      </Container>
    </div>
  )
}
