import { ImageType } from '@/lib/api/entity'
import { StrapiField } from '@/lib/api/types'
import { FC, ReactNode, useMemo } from 'react'

type StrapiBackgroundImageProps = {
  value: StrapiField<ImageType> | null | undefined
  className?: string
  style?: React.CSSProperties
  children?: ReactNode
}

export const StrapiBackgroundImage: FC<StrapiBackgroundImageProps> = ({
  value,
  className = '',
  style = {},
  children,
}) => {
  const backgroundImageUrl = useMemo(() => {
    const attributes = value?.data?.attributes
    return (
      attributes?.formats?.large?.url ??
      attributes?.formats?.medium?.url ??
      attributes?.formats?.small?.url ??
      attributes?.url ??
      null
    )
  }, [value])

  const prefixUrl = useMemo(() => {
    return import.meta.env.DEV ? import.meta.env.VITE_STRAPI_URL : ''
  }, [])

  const getFullUrl = (imgUrl: string | null): string =>
    imgUrl?.startsWith('http') ? imgUrl : `${prefixUrl}${imgUrl}`

  return (
    <div
      className={`bg-cover bg-repeat-round ${className}`}
      style={{
        ...style,
        ...(backgroundImageUrl && {
          backgroundImage: `url(${getFullUrl(backgroundImageUrl)})`,
        }),
      }}
    >
      {children}
    </div>
  )
}
