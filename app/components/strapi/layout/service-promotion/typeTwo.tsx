import { Container } from '@/components'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { SOCIAL_MEDIA } from '@/constants/social'
import { ImageType, PromotionEntity } from '@/lib/api/entity'
import { Link } from '@remix-run/react'
import { FC, useMemo } from 'react'
import { StrapiImage } from '../../strapi-image'
import { useTranslation } from 'react-i18next'
import { ServicePromotionProps } from '.'
import { StrapiField } from '@/lib/api/types'

type ServicePromotionTypeTwoProps = ServicePromotionProps

interface PromotionCarouselProps {
  data: PromotionEntity[]
  slug: string
  hasMultiplePromotions: boolean
}

const PromotionHeader = ({
  backgroundColor,
}: {
  backgroundColor: string | null
}) => {
  const { t } = useTranslation('promotion')

  return (
    <div
      className="w-full flex flex-col text-primary items-center gap-4"
      style={
        backgroundColor
          ? {
              color: backgroundColor,
            }
          : {}
      }
    >
      <div
        className="border-b-4 border-primary pb-4 h-fit space-y-1 text-center"
        style={
          backgroundColor
            ? {
                borderColor: backgroundColor,
              }
            : {}
        }
      >
        <h2 className="text-md lg:text-5xl">Promotion</h2>
        <h4>{t('title')}</h4>
      </div>
    </div>
  )
}

const PromotionCarousel: FC<PromotionCarouselProps> = ({
  data,
  slug,
  hasMultiplePromotions,
}) => (
  <div className="flex justify-center relative p-10 lg:px-0">
    <Carousel
      className="w-full text-center max-w-3xl"
      opts={{ active: hasMultiplePromotions, align: 'start' }}
      isShowDots={hasMultiplePromotions}
      dotsClassName="!ring-primary [&.active]:!bg-primary sm:size-3"
    >
      <CarouselContent>
        {data.map(({ createdAt, id, image }) => (
          <CarouselItem
            key={`service-promotion-${slug}-${id}-${createdAt}-${image?.data?.attributes?.name}`}
          >
            <StrapiImage
              value={image}
              className="aspect-square rounded-3xl object-cover bg-primary-200 max-w-96"
            />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious className="text-primary" />
      <CarouselNext className="text-primary" />
    </Carousel>
  </div>
)

const PromotionButton = ({
  buttonImage,
}: {
  buttonImage: StrapiField<ImageType> | ImageType
}): React.ReactElement => (
  <div className="button-image">
    <Link className="w-fit" to={SOCIAL_MEDIA.LINE}>
      <StrapiImage
        value={buttonImage}
        className="max-w-60 rounded-full object-cover"
      />
    </Link>
  </div>
)

export const ServicePromotionLayoutTypeTwo: FC<
  ServicePromotionTypeTwoProps
> = ({ backgroundColor, buttonImage, data, slug }) => {
  const hasMultiplePromotions = useMemo(() => data.length > 1, [data])

  return (
    <div className="bg-secondary/10 w-full py-8 lg:py-24">
      <Container className="flex flex-col items-center gap-3 lg:gap-6">
        <PromotionHeader backgroundColor={backgroundColor} />
        <PromotionCarousel
          data={data}
          slug={slug}
          hasMultiplePromotions={hasMultiplePromotions}
        />
        <PromotionButton buttonImage={buttonImage} />
      </Container>
    </div>
  )
}
