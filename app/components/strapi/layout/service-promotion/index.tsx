import { ImageType, PromotionEntity, ServicePromotion } from '@/lib/api/entity'
import { FC, useMemo } from 'react'
import { ServicePromotionLayoutTypeOne } from './typeOne'
import { ServicePromotionLayoutTypeTwo } from './typeTwo'
import { ServicePromotionLayoutTypeThree } from './typeThree'
import { StrapiField } from '@/lib/api/types'

interface ServicePromotionLayoutProps {
  data?: ServicePromotion
  slug: string
}

export interface ServicePromotionProps {
  data: PromotionEntity[]
  slug: string
  buttonImage: StrapiField<ImageType>
  backgroundColor: string | null
}

export const ServicePromotionLayout: FC<ServicePromotionLayoutProps> = ({
  data,
  slug,
}) => {
  const dataPromotions: PromotionEntity[] = useMemo(
    () =>
      data?.promotions?.data?.map((_) => _.attributes as PromotionEntity) ?? [],
    [data?.promotions?.data]
  )
  if (!data) return null

  const { backgroundColor, type, buttonImage } = data
  switch (type?.toLocaleLowerCase()) {
    case 'one':
      return (
        <ServicePromotionLayoutTypeOne
          backgroundColor={backgroundColor}
          buttonImage={buttonImage}
          data={dataPromotions}
          slug={slug}
        />
      )
    case 'two':
      return (
        <ServicePromotionLayoutTypeTwo
          backgroundColor={backgroundColor}
          buttonImage={buttonImage}
          data={dataPromotions}
          slug={slug}
        />
      )
    case 'three':
      return (
        <ServicePromotionLayoutTypeThree
          backgroundColor={backgroundColor}
          buttonImage={buttonImage}
          data={dataPromotions}
          slug={slug}
        />
      )
    default:
      return null
  }
}
