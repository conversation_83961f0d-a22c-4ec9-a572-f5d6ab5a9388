import { Container } from '@/components'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { SOCIAL_MEDIA } from '@/constants/social'
import { ImageType, PromotionEntity } from '@/lib/api/entity'
import { Link } from '@remix-run/react'
import { FC } from 'react'
import { StrapiImage } from '../../strapi-image'
import { useTranslation } from 'react-i18next'
import { StrapiField } from '@/lib/api/types'
import { ServicePromotionProps } from '.'

type ServicePromotionTypeThreeProps = ServicePromotionProps

interface PromotionCarouselProps {
  data: PromotionEntity[]
  slug: string
}

export const PromotionHeader = ({
  backgroundColor,
  buttonImage,
}: {
  backgroundColor: string | null
  buttonImage: StrapiField<ImageType> | ImageType
}): React.ReactElement => {
  const { t } = useTranslation('promotion')

  return (
    <div className="w-full flex text-primary justify-between flex-wrap gap-4">
      <div
        className="border-l-4 border-primary pl-4 h-fit space-y-1"
        style={
          backgroundColor
            ? {
                color: backgroundColor,
                borderColor: backgroundColor,
              }
            : {}
        }
      >
        <h2 className="text-md lg:text-5xl">Promotion</h2>
        <h4>{t('title')}</h4>
      </div>
      <div className="button-image ">
        <Link to={SOCIAL_MEDIA.LINE}>
          <StrapiImage
            value={buttonImage}
            className="max-w-40 sm:max-w-50 lg:max-w-60 rounded-full object-cover"
          />
        </Link>
      </div>
    </div>
  )
}

const PromotionCarousel: FC<PromotionCarouselProps> = ({ data, slug }) => {
  const hasMultiplePromotions = data.length > 1

  return (
    <div className="relative">
      <Carousel
        className="w-full justify-items-center text-center"
        opts={{ active: hasMultiplePromotions, align: 'start' }}
        isShowDots={hasMultiplePromotions}
        dotsClassName="!ring-primary [&.active]:!bg-primary sm:size-3"
      >
        <CarouselContent>
          {data.map(({ id, createdAt, image }) => (
            <CarouselItem
              key={`service-promotion-${slug}-${id}-${createdAt}-${image?.data?.attributes?.name}`}
            >
              <StrapiImage
                value={image}
                className="aspect-square rounded-3xl object-cover bg-primary-200 max-w-xs lg:max-w-lg"
              />
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="text-primary" />
        <CarouselNext className="text-primary" />
      </Carousel>
    </div>
  )
}

export const ServicePromotionLayoutTypeThree: FC<
  ServicePromotionTypeThreeProps
> = ({ backgroundColor, buttonImage, data, slug }) => (
  <div className="bg-secondary/10 w-full flex py-8 lg:py-24">
    <Container className="w-full flex flex-col gap-3 lg:gap-6 justify-between">
      <PromotionHeader
        backgroundColor={backgroundColor}
        buttonImage={buttonImage}
      />
      {data && <PromotionCarousel data={data} slug={slug} />}
    </Container>
  </div>
)
