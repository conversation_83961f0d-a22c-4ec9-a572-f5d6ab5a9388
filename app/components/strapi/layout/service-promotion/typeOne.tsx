import { Container } from '@/components'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { ImageType, PromotionEntity } from '@/lib/api/entity'
import { Link } from '@remix-run/react'
import { FC, useMemo } from 'react'
import { StrapiImage } from '../../strapi-image'
import { SOCIAL_MEDIA } from '@/constants/social'
import { useTranslation } from 'react-i18next'
import { StrapiField } from '@/lib/api/types'
import { ServicePromotionProps } from '.'

type ServicePromotionTypeOneProps = ServicePromotionProps

interface PromotionCarouselProps {
  data: PromotionEntity[]
  slug: string
  hasMultiplePromotions: boolean
}

const PromotionHeader = ({
  backgroundColor,
  buttonImage,
}: {
  backgroundColor: string | null
  buttonImage: StrapiField<ImageType> | ImageType
}): React.ReactElement => {
  const { t } = useTranslation('promotion')

  return (
    <div
      className="flex md:flex-col text-primary gap-4 max-md:justify-between"
      style={
        backgroundColor
          ? {
              color: backgroundColor,
            }
          : {}
      }
    >
      <div
        className="border-l-4 border-primary pl-4"
        style={
          backgroundColor
            ? {
                borderColor: backgroundColor,
              }
            : {}
        }
      >
        <h2 className="text-md lg:text-5xl">Promotion</h2>
        <h4>{t('title')}</h4>
      </div>
      <div className="button-image">
        <Link to={SOCIAL_MEDIA.LINE}>
          <StrapiImage
            value={buttonImage}
            className="max-w-50 lg:max-w-60 rounded-full object-cover"
          />
        </Link>
      </div>
    </div>
  )
}

const PromotionCarousel: FC<PromotionCarouselProps> = ({
  data,
  slug,
  hasMultiplePromotions,
}) => {
  return (
    <div className="relative">
      <Carousel
        className="w-fit justify-items-center text-center max-w-3xl"
        opts={{ active: hasMultiplePromotions, align: 'start' }}
        isShowDots={hasMultiplePromotions}
      >
        <CarouselContent>
          {data.map(({ createdAt, id, image }) => (
            <CarouselItem
              key={`service-promotion-${slug}-${id}-${createdAt}-${image?.data?.attributes?.name}`}
            >
              <div className="bg-primary-200 aspect-square rounded-3xl max-w-72 lg:max-w-xl overflow-hidden">
                <StrapiImage value={image} className="object-cover" />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="text-primary" />
        <CarouselNext className="text-primary" />
      </Carousel>
    </div>
  )
}

export const ServicePromotionLayoutTypeOne: FC<
  ServicePromotionTypeOneProps
> = ({ backgroundColor, buttonImage, data, slug }) => {
  const hasMultiplePromotions = useMemo(() => data.length > 1, [data])

  return (
    <div className="bg-secondary/10 w-full py-8 lg:py-24">
      <Container className="flex flex-col w-full md:flex-row justify-between">
        <PromotionHeader
          backgroundColor={backgroundColor}
          buttonImage={buttonImage}
        />

        {data && (
          <PromotionCarousel
            data={data}
            slug={slug}
            hasMultiplePromotions={hasMultiplePromotions}
          />
        )}
      </Container>
    </div>
  )
}
