import { Container } from '@/components/container'
import { ServiceContentDynamic } from '@/lib/api/entity'
import { FC, useMemo } from 'react'
import { StrapiBackgroundImage } from '../strapi-background-image'
import { cn } from '@/lib/utils'
import { StrapiImage } from '../strapi-image'
import { Link } from '@remix-run/react'
import { SOCIAL_MEDIA } from '@/constants/social'

interface ServiceContentDynamicLayoutProps {
  data?: ServiceContentDynamic | null
}

export const ServiceContentDynamicLayout: FC<
  ServiceContentDynamicLayoutProps
> = ({ data }) => {
  const style = useMemo(
    () =>
      data?.backgroundColor ? { backgroundColor: data.backgroundColor } : {},
    [data?.backgroundColor]
  )

  if (!data) return null

  return (
    <StrapiBackgroundImage
      value={data?.backgroundImage}
      style={style}
      className="relative w-full"
    >
      <Container
        className={cn(
          'w-full',
          data.hiddenGap ? 'py-4' : 'gap-y-8 py-8 lg:py-24'
        )}
      >
        {data?.content && (
          <div
            className="ck ck-content flex flex-col items-center"
            dangerouslySetInnerHTML={{ __html: data?.content }}
          />
        )}
        {data?.buttonImage?.data && (
          <div className="w-full flex justify-center">
            <Link to={SOCIAL_MEDIA.LINE}>
              <div className="transition-transform duration-500 hover:scale-110">
                <StrapiImage
                  value={data?.buttonImage}
                  className="max-w-80 rounded-full object-cover shadow-xl"
                />
              </div>
            </Link>
          </div>
        )}
      </Container>
    </StrapiBackgroundImage>
  )
}
