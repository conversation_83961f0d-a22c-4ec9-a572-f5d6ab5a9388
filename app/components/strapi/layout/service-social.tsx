import { Container } from '@/components'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { ServiceSocial } from '@/lib/api/entity'
import { FC, useMemo } from 'react'
import { InstagramEmbed } from 'react-social-media-embed'
import { StrapiImage } from '../strapi-image'
import useClient from '@/hooks/use-client'
import { Loader } from '@/components/loader'

interface ServiceSocialLayoutProps {
  data?: ServiceSocial | null
  slug: string
}

export const ServiceSocialLayout: FC<ServiceSocialLayoutProps> = ({
  data,
  slug,
}) => {
  const listLinks = useMemo(
    () => data?.links?.map((_, index) => ({ index: index, item: _ })) ?? [],
    [data?.links]
  )
  const { isClient } = useClient()
  if (!data?.links?.length) return null

  return (
    <div className="relative w-full">
      {data.backgroundImage?.data && (
        <StrapiImage
          className="absolute top-0 left-0 w-full h-full"
          value={data.backgroundImage}
        />
      )}
      <Container className="py-8 lg:py-16">
        <Carousel className="w-full" opts={{ align: 'start' }}>
          <CarouselContent>
            {listLinks.map(({ index, item }) => (
              <CarouselItem
                key={`service-social-${slug}-index:${index}-id:${item.id}`}
                className="lg:basis-1/2 xl:basis-1/3"
              >
                {isClient ? (
                  <InstagramEmbed url={item.url ?? ''} width="100%" />
                ) : (
                  <Loader />
                )}
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="text-pink-300" />
          <CarouselNext className="text-pink-300" />
        </Carousel>
      </Container>
    </div>
  )
}
