import { Container } from '@/components'
import { Title } from '@/components/title'
import { ServiceVdo } from '@/lib/api/entity'
import { FC } from 'react'

interface ServiceVdoTypeOneProps {
  data: ServiceVdo
}

export const ServiceVdoLayoutTypeOne: FC<ServiceVdoTypeOneProps> = ({
  data,
}) => {
  if (!data) return null
  const { title, vdoUrl } = data
  return (
    <div className="bg-our-service w-full flex flex-col py-12 lg:py-32">
      <Container className="text-center flex flex-col gap-4 lg:gap-8">
        <Title
          lineStyle="bg-white w-2/5 lg:w-1/5"
          className="text-white flex flex-col -mt-2 lg:-mt-4 gap-1 lg:gap-4"
          subTitle={title ?? ''}
        />

        <div className="relative aspect-video h-fit w-full object-cover">
          <iframe
            className="absolute bottom-0 left-0 right-0 top-0 h-full w-full shadow-md shadow-zinc-500"
            src={vdoUrl ?? ''}
            title={title ?? ''}
          />
        </div>
      </Container>
    </div>
  )
}
