import { ServiceVdo } from '@/lib/api/entity'
import { FC } from 'react'
import { ServiceVdoLayoutTypeOne } from './typeOne'
import { ServiceVdoLayoutTypeTwo } from './typeTwo'
import { ServiceVdoLayoutTypeThree } from './typeThree'

interface ServiceVdoLayoutProps {
  data?: ServiceVdo
}

export const ServiceVdoLayout: FC<ServiceVdoLayoutProps> = ({ data }) => {
  if (!data) return null

  const { type } = data
  switch (type?.toLocaleLowerCase()) {
    case 'one':
      return <ServiceVdoLayoutTypeOne data={data} />
    case 'two':
      return <ServiceVdoLayoutTypeTwo data={data} />
    case 'three':
      return <ServiceVdoLayoutTypeThree data={data} />
    default:
      return null
  }
}
