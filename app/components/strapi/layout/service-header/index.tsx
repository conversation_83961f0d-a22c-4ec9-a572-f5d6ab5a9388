import { ServiceHeader } from '@/lib/api/entity'
import { FC } from 'react'
import { ServiceHeaderTypeOne } from './typeOne'
import { ServiceHeaderTypeTwo } from './typeTwo'
import { ServiceHeaderTypeThree } from './typeThree'

interface ServiceHeaderLayoutProps {
  data?: ServiceHeader | null
  slug: string
}

export const ServiceHeaderLayout: FC<ServiceHeaderLayoutProps> = ({
  data,
  slug,
}) => {
  if (!data) return null

  const { type } = data
  switch (type?.toLocaleLowerCase()) {
    case 'one':
      return <ServiceHeaderTypeOne data={data} />
    case 'two':
      return <ServiceHeaderTypeTwo data={data} />
    case 'three':
      return <ServiceHeaderTypeThree data={data} slug={slug} />
    default:
      return null
  }
}
