import { Container } from '@/components'
import { ServiceHeader } from '@/lib/api/entity'
import { FC } from 'react'

interface ServiceHeaderTypeOneProps {
  data: ServiceHeader
}

export const ServiceHeaderTypeOne: FC<ServiceHeaderTypeOneProps> = ({
  data,
}) => {
  if (!data) return null

  const { title, description } = data

  return (
    <div className="bg-secondary/20">
      <Container className="text-center w-full flex flex-col lg:flex-row items-center gap-4 lg:gap-12 py-8 lg:py-24">
        {title && <h2 className="flex-1 text-primary">{title}</h2>}
        {description && (
          <div
            className="flex-1 text-normal italic ck ck-content"
            dangerouslySetInnerHTML={{ __html: description }}
          />
        )}
      </Container>
    </div>
  )
}
