import { Container } from '@/components'
import { ServiceHeader } from '@/lib/api/entity'
import { FC } from 'react'
import { StrapiImage } from '../../strapi-image'

interface ServiceHeaderTypeThreeProps {
  data: ServiceHeader
  slug: string
}

export const ServiceHeaderTypeThree: FC<ServiceHeaderTypeThreeProps> = ({
  data,
  slug,
}) => {
  if (!data) return null

  const { title, image } = data

  return (
    <div className="bg-secondary/20">
      <Container className="text-center w-full flex flex-col lg:flex-row items-center gap-4 lg:gap-12 py-5">
        {title && (
          <p className="flex-1 text-xl md:text-3xl text-primary">{title}</p>
        )}
        {(image?.data?.length ?? 0) > 0 && (
          <div className="flex p-2 space-x-2">
            {image?.data?.map(({ id, attributes }) => (
              <StrapiImage
                key={`service-header-type-three-${slug}-${id}-${attributes?.name}`}
                className="max-w-40 md:max-w-80 rounded-2xl"
                value={attributes}
              />
            ))}
          </div>
        )}
      </Container>
    </div>
  )
}
