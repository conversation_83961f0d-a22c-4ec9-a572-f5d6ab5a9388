import { Container } from '@/components'
import { ServiceHeader } from '@/lib/api/entity'
import { FC } from 'react'

interface ServiceHeaderTypeTwoProps {
  data: ServiceHeader
}

export const ServiceHeaderTypeTwo: FC<ServiceHeaderTypeTwoProps> = ({
  data,
}) => {
  if (!data) return null

  const { title, description } = data

  return (
    <div className="bg-gradient-to-r from-blue-mist to-primary-700">
      <Container className="text-center w-full items-center gap-4 lg:gap-12 py-8 lg:py-24 space-y-4">
        {title && <h2 className="flex-1">{title}</h2>}
        {description && (
          <div className="flex-1 text-normal ">
            <div
              className="ck ck-content"
              dangerouslySetInnerHTML={{ __html: description }}
            />
          </div>
        )}
      </Container>
    </div>
  )
}
