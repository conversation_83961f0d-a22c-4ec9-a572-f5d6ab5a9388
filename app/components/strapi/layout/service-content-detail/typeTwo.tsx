import { ServiceContentDetail } from '@/lib/api/entity'
import { Container } from '@/components'
import { StrapiImage } from '../../strapi-image'
import { FC } from 'react'

interface ServiceContentDetailLayoutTypeTwoProps {
  data: ServiceContentDetail
  slug: string
}

export const ServiceContentDetailLayoutTypeTwo: FC<
  ServiceContentDetailLayoutTypeTwoProps
> = ({ data, slug }) => {
  if (!data) return null

  const { description, images } = data
  return (
    <div className="flex flex-col bg-gradient-to-r from-primary-600 to-primary-800">
      <Container className="w-full lg:w-4/6 py-8 lg:py-12 text-white ">
        {description && (
          <div
            className="ck ck-content"
            dangerouslySetInnerHTML={{ __html: description }}
          />
        )}
      </Container>

      {images && (
        <div className="w-full grid grid-cols-2 md:grid-cols-4 gap-4 px-4 lg:px-16 py-8 lg:py-24 bg-primary-200">
          {images.map(({ id, image, description }) => (
            <div key={`service-content-detail-layout-type-two-${slug}-${id}`}>
              <StrapiImage value={image} className="rounded-xl" />
              {description && (
                <div className="text-secondary text-center">{description}</div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
