import { Container } from '@/components'
import { ServiceContentDetail } from '@/lib/api/entity'
import { FC } from 'react'

interface ServiceContentDetailLayoutTypeOneProps {
  data: ServiceContentDetail
}

export const ServiceContentDetailLayoutTypeOne: FC<
  ServiceContentDetailLayoutTypeOneProps
> = ({ data }) => {
  if (!data) return null

  const { description } = data
  return (
    <div className="bg-gradient-to-r from-primary-600 to-primary-800">
      <Container className="w-full lg:w-4/6 gap-y-8 py-8 lg:py-24 text-white ">
        {description && (
          <div
            className="ck ck-content"
            dangerouslySetInnerHTML={{ __html: description }}
          />
        )}
      </Container>
    </div>
  )
}
