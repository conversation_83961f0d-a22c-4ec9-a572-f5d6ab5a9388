import { ServiceContentDetail } from '@/lib/api/entity'
import { FC } from 'react'
import { ServiceContentDetailLayoutTypeOne } from './typeOne'
import { ServiceContentDetailLayoutTypeTwo } from './typeTwo'

interface ServiceContentDetailLayoutProps {
  data?: ServiceContentDetail
  slug: string
}

export const ServiceContentDetailLayout: FC<
  ServiceContentDetailLayoutProps
> = ({ data, slug }) => {
  if (!data) return null

  const { type } = data
  switch (type?.toLocaleLowerCase()) {
    case 'one':
      return <ServiceContentDetailLayoutTypeOne data={data} />
    case 'two':
      return <ServiceContentDetailLayoutTypeTwo data={data} slug={slug} />
    default:
      return null
  }
}
