import { Container } from '@/components'
import {
  AccordionContent,
  AccordionItem,
  AccordionRoot,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { ServiceQuestionAnswer } from '@/lib/api/entity'
import { FC } from 'react'

interface ServiceQuestionAnswerLayoutProps {
  data?: ServiceQuestionAnswer[] | null
  slug: string
}

export const ServiceQuestionAnswerLayout: FC<
  ServiceQuestionAnswerLayoutProps
> = ({ data, slug }) => {
  return (
    <div className="w-full py-8 lg:py-24">
      <Container>
        <div className="w-full flex flex-col md:flex-row gap-3 lg:gap-6 justify-between">
          <div className="w-full flex justify-center items-center max-md:border-b max-md:border-b-primary">
            <div className="w-fit flex flex-col items-center text-primary">
              <h2 className="w-fit text-3xl lg:text-5xl">Q&A</h2>
            </div>
          </div>
          <div className="w-full">
            <AccordionRoot
              className="border-0 [&>div:first-child]:pt-0"
              type="multiple"
            >
              {data?.map((item, index) => (
                <AccordionItem
                  key={`service-question-answer-${slug}-${item.question}-${item.id}`}
                  value={`menu-${item.question}-${item.id}`}
                >
                  <AccordionTrigger className="px-4 py-2 text-white bg-primary">
                    <h3 className="text-left">
                      Q{index + 1} : {item.question}
                    </h3>
                  </AccordionTrigger>
                  <AccordionContent className="pb-0 py-3 pl-16">
                    <div
                      className="ck ck-content"
                      dangerouslySetInnerHTML={{ __html: item.answer ?? '' }}
                    />
                  </AccordionContent>
                </AccordionItem>
              ))}
            </AccordionRoot>
          </div>
        </div>
      </Container>
    </div>
  )
}
