import { Container } from '@/components'
import { Card, CardDescription } from '@/components/ui/card'
import { ServiceContentBoxes } from '@/lib/api/entity'
import { FC } from 'react'
import { StrapiImage } from '../../strapi-image'
import { Title } from '@/components/title'

interface ServiceContentBoxesLayoutTypeTwoProps {
  data: ServiceContentBoxes
  slug: string
}

export const ServiceContentBoxesLayoutTypeTwo: FC<
  ServiceContentBoxesLayoutTypeTwoProps
> = ({ data, slug }) => {
  const { title, images, description } = data
  return (
    <div className="bg-background-service-special">
      <Container>
        <div className="flex flex-col items-center gap-8 py-8 lg:py-24 text-primary">
          {title && (
            <Title
              className="text-primary text-center"
              lineStyle="bg-primary w-2/3 md:w-1/3"
              rawHtml={title}
            />
          )}
          {description && (
            <div
              className="ck ck-content"
              dangerouslySetInnerHTML={{ __html: description }}
            />
          )}
          {images && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-10 px-4 lg:px-0 pt-8">
              {images.map(({ id, image, description }) => (
                <Card
                  key={`service-content-boxes-${slug}-${id}`}
                  className="flex flex-col items-center px-14 py-7 gap-2 w-fit rounded-full overflow-clip bg-primary"
                >
                  <StrapiImage
                    value={image}
                    className="aspect-square flex-1 max-w-32 !bg-transparent"
                  />
                  {description && (
                    <CardDescription className="text-white text-center font-semibold max-w-32">
                      {description}
                    </CardDescription>
                  )}
                </Card>
              ))}
            </div>
          )}
        </div>
      </Container>
    </div>
  )
}
