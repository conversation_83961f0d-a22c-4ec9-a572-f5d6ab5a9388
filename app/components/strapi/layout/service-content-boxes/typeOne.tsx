import { Title } from '@/components/title'
import { Card, CardDescription } from '@/components/ui/card'
import { ServiceContentBoxes } from '@/lib/api/entity'
import { FC } from 'react'
import { StrapiImage } from '../../strapi-image'

interface ServiceContentBoxesLayoutTypeOneProps {
  data: ServiceContentBoxes
  slug: string
}

export const ServiceContentBoxesLayoutTypeOne: FC<
  ServiceContentBoxesLayoutTypeOneProps
> = ({ data, slug }) => {
  const { images, title } = data

  return (
    <div className="flex flex-col items-center gap-8 py-8 lg:py-24 text-secondary bg-background-secondary">
      {title && (
        <Title
          className="text-secondary text-center"
          lineStyle="bg-secondary w-2/3 md:w-1/3"
          rawHtml={title}
        />
      )}
      {images && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 px-4 lg:px-0">
          {images.map(({ id, image, description }) => (
            <Card
              key={`service-content-boxes-${slug}-${id}`}
              className="flex flex-col items-center p-7 gap-2 w-full max-w-xs mx-auto"
            >
              <StrapiImage value={image} className="w-24" />
              {description && (
                <CardDescription className="text-secondary text-center">
                  {description}
                </CardDescription>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
