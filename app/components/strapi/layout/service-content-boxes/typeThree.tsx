import { Title } from '@/components/title'
import { Card, CardDescription } from '@/components/ui/card'
import { ServiceContentBoxes } from '@/lib/api/entity'
import { FC } from 'react'
import { StrapiImage } from '../../strapi-image'
import { Container } from '@/components/container'

interface ServiceContentBoxesLayoutTypeThreeProps {
  data: ServiceContentBoxes
  slug: string
}

export const ServiceContentBoxesLayoutTypeThree: FC<
  ServiceContentBoxesLayoutTypeThreeProps
> = ({ data, slug }) => {
  const { title, images } = data
  return (
    <div className="bg-primary">
      <Container>
        <div className="flex flex-col items-center gap-8 py-8 lg:py-24 text-secondary">
          {title && (
            <Title
              className="text-white text-center"
              lineStyle="bg-white w-2/3 md:w-1/3"
              rawHtml={title}
            />
          )}
          {images && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 lg:gap-10 px-4 lg:px-0 w-full">
              {images.map(({ id, image, description }) => (
                <Card
                  key={`service-content-boxes-${slug}-${id}`}
                  className="flex items-center p-7 lg:p-12 gap-2 w-full"
                >
                  <StrapiImage
                    value={image}
                    className="aspect-square flex-1 max-w-14"
                  />
                  {description && (
                    <CardDescription className="text-primary font-semibold">
                      {description}
                    </CardDescription>
                  )}
                </Card>
              ))}
            </div>
          )}
        </div>
      </Container>
    </div>
  )
}
