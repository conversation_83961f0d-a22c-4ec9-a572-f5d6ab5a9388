import { ServiceContentBoxes } from '@/lib/api/entity'
import { FC } from 'react'
import { ServiceContentBoxesLayoutTypeOne } from './typeOne'
import { ServiceContentBoxesLayoutTypeTwo } from './typeTwo'
import { ServiceContentBoxesLayoutTypeThree } from './typeThree'

interface ServiceContentBoxesLayoutProps {
  data?: ServiceContentBoxes
  slug: string
}

export const ServiceContentBoxesLayout: FC<ServiceContentBoxesLayoutProps> = ({
  data,
  slug,
}) => {
  if (!data) return null

  const { type } = data
  switch (type?.toLocaleLowerCase()) {
    case 'one':
      return <ServiceContentBoxesLayoutTypeOne data={data} slug={slug} />
    case 'two':
      return <ServiceContentBoxesLayoutTypeTwo data={data} slug={slug} />
    case 'three':
      return <ServiceContentBoxesLayoutTypeThree data={data} slug={slug} />
    default:
      return null
  }
}
