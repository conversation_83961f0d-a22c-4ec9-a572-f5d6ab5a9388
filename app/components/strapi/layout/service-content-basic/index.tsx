import { ServiceContentBasic } from '@/lib/api/entity'
import { FC } from 'react'
import { ServiceContentBasicLayoutTypeOne } from './typeOne'
import { ServiceContentBasicLayoutTypeTwo } from './typeTwo'

interface ServiceContentBasicLayoutProps {
  data?: ServiceContentBasic
  slug: string
}

export const ServiceContentBasicLayout: FC<ServiceContentBasicLayoutProps> = ({
  data,
  slug,
}) => {
  if (!data) return null

  const { type } = data
  switch (type?.toLocaleLowerCase()) {
    case 'one':
      return <ServiceContentBasicLayoutTypeOne data={data} slug={slug} />
    case 'two':
      return <ServiceContentBasicLayoutTypeTwo data={data} slug={slug} />
    default:
      return null
  }
}
