import { ServiceContentBasic } from '@/lib/api/entity'
import { Container } from '@/components'
import { StrapiImage } from '../../strapi-image'
import { FC } from 'react'

interface ServiceContentBasicLayoutTypeTwoProps {
  data: ServiceContentBasic
  slug: string
}

export const ServiceContentBasicLayoutTypeTwo: FC<
  ServiceContentBasicLayoutTypeTwoProps
> = ({ data, slug }) => {
  const { title, description, images } = data
  return (
    <div className="bg-gradient-to-l from-primary to-primary-500 w-full py-8 lg:py-24">
      <Container className="flex flex-col gap-3 lg:gap-8 ">
        <div
          className="ck ck-content text-white"
          dangerouslySetInnerHTML={{ __html: title ?? '' }}
        />
        <div
          className="ck ck-content text-white"
          dangerouslySetInnerHTML={{ __html: description ?? '' }}
        />
        {images && (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 px-4 lg:px-0 justify-center items-center text-center">
            {images.map(({ id, image, description }) => (
              <div key={`service-content-basic-${slug}-${id}-${description}`}>
                <StrapiImage value={image} className="w-24" />
                {description && (
                  <span className="text-center text-sm text-white">
                    {description}
                  </span>
                )}
              </div>
            ))}
          </div>
        )}
      </Container>
    </div>
  )
}
