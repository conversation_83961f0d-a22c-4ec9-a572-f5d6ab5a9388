import { ReviewEntity, ServiceReview } from '@/lib/api/entity'
import { FC, useMemo } from 'react'
import { ServiceReviewLayoutTypeOne } from './typeOne'
import { ServiceReviewLayoutTypeTwo } from './typeTwo'

interface ServiceReviewLayoutProps {
  data?: ServiceReview
  slug: string
  locale: string
}

export const ServiceReviewLayout: FC<ServiceReviewLayoutProps> = ({
  data,
  slug,
  locale,
}) => {
  const dataReview: ReviewEntity[] = useMemo(
    () =>
      data?.reviews?.data
        ?.map((_) => _.attributes as ReviewEntity)
        .filter((item) => item.isShowOnService) ?? [],
    [data?.reviews?.data]
  )
  if (!data || dataReview.length < 1) return null

  const { type } = data
  if (type?.toLocaleLowerCase() === 'one') {
    return (
      <ServiceReviewLayoutTypeOne
        data={dataReview}
        slug={slug}
        locale={locale}
      />
    )
  }
  if (type?.toLocaleLowerCase() === 'two') {
    return (
      <ServiceReviewLayoutTypeTwo
        data={dataReview}
        slug={slug}
        locale={locale}
      />
    )
  }

  return null
}
