import { FC, memo } from 'react'
import { StrapiImage } from '../../strapi-image'
import { Container } from '@/components'
import { ReviewEntity } from '@/lib/api/entity'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { Card } from '@/components/ui/card'
import { Link } from '@remix-run/react'
import { Button } from '@/components/ui/button'
import { ChevronRightIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { getLocaleParam } from '@/utils/localeHelper'

interface ServiceReviewTypeOneProps {
  data: ReviewEntity[]
  slug: string
  locale: string
}

const ServiceReviewHeader: FC<{ locale: string }> = memo(({ locale }) => {
  const { t } = useTranslation('home')

  return (
    <div className="w-full flex justify-between items-end">
      <div className="text-primary text-start animate-in fade-in slide-in-from-left duration-500 text-white">
        <h2>{t('review.title')}</h2>
        <h3 className="font-normal">{t('review.detail')}</h3>
      </div>
      <Link
        to={{
          pathname: '/review',
          search: getLocaleParam(locale),
        }}
        className="animate-in slide-in-from-right duration-500"
        aria-label="Learn more review"
      >
        <Button
          variant="primary"
          className="w-fit text-primary flex items-center justify-center gap-2 bg-white"
          aria-label="Learn more review"
        >
          {t('ourService.seeMore')}
          <ChevronRightIcon className="size-4 text-primary" />
        </Button>
      </Link>
    </div>
  )
})

const ServiceReviewCarouselItem: FC<{ item: ReviewEntity }> = memo(
  ({ item }) => (
    <CarouselItem
      key={item.id}
      className="md:basis-1/2 lg:basis-1/3 aspect-square"
    >
      <div className="p-1 animate-in zoom-in duration-500">
        <Card className="bg-opacity-80 bg-transparent border-none shadow-none">
          <StrapiImage className="rounded-2xl shadow-md" value={item.image} />
        </Card>
      </div>
    </CarouselItem>
  )
)

export const ServiceReviewLayoutTypeOne: FC<ServiceReviewTypeOneProps> = ({
  data,
  slug,
  locale,
}) => (
  <div className="bg-gradient-to-r from-primary-500 to-primary w-full py-8 lg:py-24">
    <Container className="flex flex-col gap-3 lg:gap-8">
      <ServiceReviewHeader locale={locale} />
      <div className="w-full h-0.5 bg-white" />
      <Carousel
        className="w-full"
        opts={{ align: 'start' }}
        isShowDots
        dotsClassName="!ring-white [&.active]:!bg-white"
      >
        <CarouselContent>
          {data.map((item) => (
            <ServiceReviewCarouselItem
              key={`service-review-layout-type-one-${slug}-${item.createdAt}`}
              item={item}
            />
          ))}
        </CarouselContent>
        <CarouselPrevious className="-left-12" color="white" />
        <CarouselNext className="-right-12" color="white" />
      </Carousel>
    </Container>
  </div>
)
