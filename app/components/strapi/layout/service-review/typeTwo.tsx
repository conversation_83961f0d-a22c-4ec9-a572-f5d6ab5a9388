import { FC, memo } from 'react'
import { StrapiImage } from '../../strapi-image'
import { Container } from '@/components'
import { ReviewEntity } from '@/lib/api/entity'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@/components/ui/carousel'
import { Card } from '@/components/ui/card'
import { Link } from '@remix-run/react'
import { Button } from '@/components/ui/button'
import { ChevronRightIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { getLocaleParam } from '@/utils/localeHelper'

interface ServiceReviewTypeTwoProps {
  data: ReviewEntity[]
  slug: string
  locale: string
}

const ServiceReviewHeader: FC<{ locale: string }> = memo(({ locale }) => {
  const { t } = useTranslation('home')

  return (
    <Container className="w-full flex justify-between items-end">
      <div className="text-primary-800 text-start animate-in fade-in slide-in-from-left duration-500">
        <h2>{t('review.title')}</h2>
        <h3 className="font-normal">{t('review.detail')}</h3>
      </div>
      <Link
        to={{
          pathname: '/review',
          search: getLocaleParam(locale),
        }}
        className="animate-in slide-in-from-right duration-500"
        aria-label="Learn more review"
      >
        <Button
          variant="primary"
          className="w-fit text-white flex items-center justify-center gap-2 bg-primary-800"
          aria-label="Learn more review"
        >
          {t('ourService.seeMore')}
          <ChevronRightIcon className="size-4 text-white" />
        </Button>
      </Link>
    </Container>
  )
})

const ServiceReviewCarouselItem: FC<{ item: ReviewEntity }> = memo(
  ({ item }) => (
    <CarouselItem
      key={item.id}
      className="md:basis-1/2 lg:basis-1/3 aspect-square"
    >
      <div className="animate-in zoom-in duration-500">
        <Card className="bg-opacity-80 bg-transparent border-none shadow-none">
          <StrapiImage className="shadow-sm" value={item.image} />
        </Card>
      </div>
    </CarouselItem>
  )
)

export const ServiceReviewLayoutTypeTwo: FC<ServiceReviewTypeTwoProps> = ({
  data,
  slug,
  locale,
}) => (
  <div className="bg-primary-300 w-full py-8 lg:py-20 space-y-12">
    <ServiceReviewHeader locale={locale} />
    <Carousel
      className="w-full"
      opts={{ align: 'start' }}
      isShowDots
      dotsClassName="!ring-white [&.active]:!bg-white"
    >
      <CarouselContent>
        {data.map((item) => (
          <ServiceReviewCarouselItem
            key={`service-review-layout-type-one-${slug}-${item.createdAt}`}
            item={item}
          />
        ))}
      </CarouselContent>
    </Carousel>
  </div>
)
