import { ServiceContentImage } from '@/lib/api/entity'
import { FC } from 'react'
import { ServiceContentImageLayoutTypeOne } from './typeOne'
import { ServiceContentImageLayoutTypeTwo } from './typeTwo'
import { ServiceContentImageLayoutTypeThree } from './typeThree'

interface ServiceContentImageLayoutProps {
  data?: ServiceContentImage
}

export const ServiceContentImageLayout: FC<ServiceContentImageLayoutProps> = ({
  data,
}) => {
  if (!data) return null

  const { type } = data
  switch (type?.toLocaleLowerCase()) {
    case 'one':
      return <ServiceContentImageLayoutTypeOne data={data} />
    case 'two':
      return <ServiceContentImageLayoutTypeTwo data={data} />
    case 'three':
      return <ServiceContentImageLayoutTypeThree data={data} />
    default:
      return null
  }
}
