import { Container } from '@/components'
import { ServiceContentImage } from '@/lib/api/entity'
import { FC } from 'react'
import { StrapiImage } from '../../strapi-image'
import { cn } from '@/lib/utils'

interface ServiceContentImageLayoutTypeTwoProps {
  data: ServiceContentImage
}

export const ServiceContentImageLayoutTypeTwo: FC<
  ServiceContentImageLayoutTypeTwoProps
> = ({ data }) => {
  if (!data) return null

  const { image1, description1, image2, description2 } = data

  return (
    <div className="w-full bg-primary-600 py-8 lg:py-12">
      <Container className="gap-10 space-y-10">
        <div
          className={cn('grid items-center gap-10', {
            'grid-cols-1 md:grid-cols-2': image1?.data,
          })}
        >
          <div className="h-full w-full flex flex-col gap-4 md:gap-8 justify-center items-center text-white text-center md:order-1 order-2">
            {description1 && (
              <div
                className="ck ck-content"
                dangerouslySetInnerHTML={{ __html: description1 ?? '' }}
              />
            )}
          </div>
          <StrapiImage
            className="w-full justify-self-center max-w-xs md:max-w-md rounded-2xl md:order-2 order-1"
            value={image1}
          />
        </div>
        <div
          className={cn('grid items-center gap-10', {
            'grid-cols-1 md:grid-cols-2': image2?.data,
          })}
        >
          <StrapiImage
            className="w-100 justify-self-center max-w-xs md:max-w-md rounded-2xl"
            value={image2}
          />
          <div className="h-full w-full flex flex-col gap-4 md:gap-8 justify-center items-center text-white text-center">
            {description2 && (
              <div
                className="ck ck-content"
                dangerouslySetInnerHTML={{ __html: description2 ?? '' }}
              />
            )}
          </div>
        </div>
      </Container>
    </div>
  )
}
