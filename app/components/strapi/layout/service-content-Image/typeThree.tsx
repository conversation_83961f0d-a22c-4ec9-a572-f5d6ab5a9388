import { Container } from '@/components'
import { ServiceContentImage } from '@/lib/api/entity'
import { cn } from '@/lib/utils'
import { FC } from 'react'
import { StrapiImage } from '../../strapi-image'

interface ServiceContentImageLayoutTypeThreeProps {
  data: ServiceContentImage
}

export const ServiceContentImageLayoutTypeThree: FC<
  ServiceContentImageLayoutTypeThreeProps
> = ({ data }) => {
  if (!data) return null

  const { image1, description1, image2, description2 } = data

  return (
    <div className="w-full flex flex-col">
      <Container className="py-7">
        <div
          className={cn('grid items-center gap-10 ', {
            'grid-cols-1 md:grid-cols-2': image1?.data,
          })}
        >
          <div className="h-full w-full flex flex-col gap-4 md:gap-8 justify-center items-center text-center md:order-1 order-2 text-primary">
            {description1 && (
              <div
                className="ck ck-content"
                dangerouslySetInnerHTML={{ __html: description1 ?? '' }}
              />
            )}
          </div>
          <StrapiImage
            className="w-full justify-self-center max-w-xs md:max-w-md rounded-2xl md:order-2 order-1"
            value={image1}
          />
        </div>
      </Container>

      <div
        className={cn('grid items-center text-primary', {
          'grid-cols-1 md:grid-cols-2': image2?.data,
        })}
      >
        <div className="flex-1 bg-primary-900 p-7">
          <StrapiImage
            className="w-100 justify-self-center max-w-xs md:max-w-md rounded-2xl"
            value={image2}
          />
        </div>
        <div className="h-full w-full flex flex-col gap-4 md:gap-8 justify-center items-center bg-secondary-200 p-7">
          {description2 && (
            <div
              className="[&_ul]:list-inside [&_ol]:list-inside ck ck-content"
              dangerouslySetInnerHTML={{ __html: description2 ?? '' }}
            />
          )}
        </div>
      </div>
    </div>
  )
}
