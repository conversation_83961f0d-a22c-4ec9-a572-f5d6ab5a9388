import { Contact } from '@/routes/home/<USER>'
import { AboutUs } from '@/routes/home/<USER>'
import { Promotion } from '@/routes/home/<USER>'
import { Banner } from '@/routes/home/<USER>'
import { OurService } from '@/routes/home/<USER>'
import { strapiGetAboutUs, strapiGetContact } from '@/lib/api/strapi'
import { LoaderFunction, MetaFunction } from '@remix-run/node'
import { useLoaderData } from '@remix-run/react'
import {
  AboutUsEntity,
  ContactEntity,
  PageEntity,
  PromotionEntity,
  ReviewEntity,
} from '@/lib/api/entity'
import { warperSingleType } from '@/utils/strapiHelper'
import { fetchPageBySlug, queryStringBuilder } from '@/utils/fetchHelper'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { StrapiContentMedia } from '@/components/strapi/strapi-content-media'
import { defineRoute, withCors } from '@/utils/routeHelper'
import { lazy, Suspense, useContext } from 'react'
import { AppContext } from '@/providers/appContext'
import { Loader } from '@/components/loader'
import useClient from '@/hooks/use-client'
import { HelmetSEO } from '@/components/helmet/helmet-seo'

const Review = lazy(() => import('@/routes/home/<USER>'))
const Social = lazy(() => import('@/routes/home/<USER>'))
const GoogleReviews = lazy(() => import('@/routes/home/<USER>'))

export type LoaderData = {
  dataPage: PageEntity
  dataContact: ContactEntity
  dataAboutUs: AboutUsEntity
  dataPromotion: PromotionEntity[]
  dataReview: ReviewEntity[]
  locale: string
}

export const meta: MetaFunction = ({ data }) => {
  const { dataPage } = data as LoaderData
  return buildTags(dataPage.seo)
}

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const { lng } = defineRoute(request)
  const [responsePage, responseContact, responseAboutUs] = await Promise.all([
    fetchPageBySlug(
      'home',
      lng,
      queryStringBuilder(['images', 'seo', 'seo.metaSocial', 'seo.metaImage'])
    ),
    strapiGetContact(queryStringBuilder(['map', 'backgroundImage']), lng),
    strapiGetAboutUs(
      queryStringBuilder([
        'landing',
        'landing.media',
        'reason',
        'reason.media',
      ]),
      lng
    ),
  ])

  return {
    dataPage: responsePage,
    dataContact: warperSingleType(responseContact),
    dataAboutUs: warperSingleType(responseAboutUs),
    locale: lng,
  }
}

export default function Index() {
  const { dataPage, dataContact, dataAboutUs, locale } =
    useLoaderData<LoaderData>()
  const { isClient } = useClient()
  const { mainMenu } = useContext(AppContext)
  return (
    <div className="flex flex-col w-full">
      <HelmetSEO data={dataPage.seo} />
      <Banner data={dataPage} locale={locale} />
      <AboutUs data={dataAboutUs} locale={locale} />
      <OurService data={mainMenu} locale={locale} />
      {isClient && (
        <Suspense fallback={<Loader />}>
          <Promotion locale={locale} />
        </Suspense>
      )}
      <StrapiContentMedia
        data={dataAboutUs.reason}
        className="bg-gradient-to-r from-primary-700 via-primary-700 to-primary text-white"
      />
      {isClient && (
        <>
          <Suspense fallback={<Loader />}>
            <Review locale={locale} />
          </Suspense>
          <Suspense fallback={<Loader />}>
            <Social locale={locale} />
          </Suspense>
          <Suspense fallback={<Loader />}>
            <GoogleReviews />
          </Suspense>
        </>
      )}
      <Contact data={dataContact} locale={locale} />
    </div>
  )
}
