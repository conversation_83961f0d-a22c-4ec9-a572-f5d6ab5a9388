import { Container } from '@/components'
import { ContentCard } from '@/components/content/contentCard'
import { ContactUsForm } from '@/components/form/contactUs'
import { HelmetSEO } from '@/components/helmet/helmet-seo'
import { Loader } from '@/components/loader'
import { LongdoMap } from '@/components/longdo-map'
import { SocialButtons } from '@/components/socialButtons'
import { StrapiField } from '@/components/strapi/strapi-field'
import { StrapiImage } from '@/components/strapi/strapi-image'
import useClient from '@/hooks/use-client'
import { ContactEntity, PageEntity } from '@/lib/api/entity'
import { strapiGetContact } from '@/lib/api/strapi'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { fetchPageBySlug, queryStringBuilder } from '@/utils/fetchHelper'
import { defineRoute, withCors } from '@/utils/routeHelper'
import { warperSingleType } from '@/utils/strapiHelper'
import { LoaderFunction, MetaFunction } from '@remix-run/node'
import { useLoaderData } from '@remix-run/react'
import { useTranslation } from 'react-i18next'

type LoaderData = {
  dataPage: PageEntity
  dataContact: ContactEntity
  locale: string
}

export const meta: MetaFunction = ({ data }) => {
  const { dataPage } = data as LoaderData
  return buildTags(dataPage.seo)
}

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const { slug, lng } = defineRoute(request)

  const [responsePage, responseContact] = await Promise.all([
    fetchPageBySlug(
      slug,
      lng,
      queryStringBuilder([
        'backgroundImage',
        'images',
        'seo',
        'seo.metaSocial',
        'seo.metaImage',
      ])
    ),
    strapiGetContact(queryStringBuilder(['map']), lng),
  ])

  return {
    dataContact: warperSingleType(responseContact),
    dataPage: responsePage,
    locale: lng,
  }
}

const ContactUsPage = () => {
  const { t } = useTranslation('contact-us')
  const { dataContact, dataPage, locale } = useLoaderData<LoaderData>()
  const { isClient } = useClient()

  return (
    <div className="w-full flex flex-col">
      <HelmetSEO data={dataPage.seo} />
      <StrapiImage
        value={dataPage.backgroundImage}
        className="animate-in fade-in duration-500 w-full h-fit"
      />
      <ContentCard
        className="bg-transparent text-primary items-center lg:items-start gap-2 lg:gap-4 mt-3 lg:mt-0"
        img={dataPage.images}
        subTitle={t('contact.subTitle')}
        title={t('contact.title')}
        isImageLeft={false}
      >
        <SocialButtons className="flex gap-2 justify-between" />
        <div className="text-start text-primary-900 space-y-4 md:space-y-6 pt-8 w-4/5 lg:w-3/5">
          <h4 className="text-base md:text-xl">
            {dataContact.openTitle}: {dataContact.openTime}
          </h4>
          <h4 className="text-base md:text-xl">
            {t('contact.tel')}: {dataContact.telephone}
          </h4>
          <h4 className="normal-case text-base md:text-xl">
            {t('contact.email')}: {dataContact.email}
          </h4>
          {dataContact.address?.map((address) => (
            <StrapiField
              className="font-bold md:font-semibold lg:text-xl text-base"
              key={`contact-${address.children[0].text}-${address.title}`}
              value={address}
            />
          ))}
        </div>
      </ContentCard>
      <Container className="flex flex-col lg:h-150 lg:flex-row justify-between items-center gap-0 lg:gap-10 pb-16">
        {dataContact.map &&
          (isClient ? (
            <div className="w-full h-full aspect-square md:aspect-auto">
              <LongdoMap
                id="longdo-map"
                data={dataContact.map}
                locale={locale}
                className="w-full md:min-h-100 grid long-do-map opacity-80 animate-in zoom-in duration-500 "
              />
            </div>
          ) : (
            <div className="flex justify-center items-center w-full">
              <Loader />
            </div>
          ))}
        <div className="w-full lg:w-1/2 flex flex-col gap-8 mt-14 lg:mt-0">
          <p className="px-2 lg:px-8 text-center text-primary">
            {t('email.title')}
          </p>
          <ContactUsForm />
        </div>
      </Container>
    </div>
  )
}

export default ContactUsPage
