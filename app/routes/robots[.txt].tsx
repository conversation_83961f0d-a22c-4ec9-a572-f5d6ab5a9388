import { generateRobotsTxt } from '@/lib/server/robots/robots.server'
import type { RobotsPolicy } from '@/lib/server/robots/types'

export function loader() {
  const extraPolicies: RobotsPolicy[] = [
    { type: 'userAgent', value: 'Googlebot' },
    { type: 'disallow', value: '/nogooglebot/' },
    {
      type: 'sitemap',
      value: `${import.meta.env.VITE_HOST_URL}/sitemap.xml`,
    },
  ]

  return generateRobotsTxt(extraPolicies, {
    appendOnDefaultPolicies: true,
    headers: { 'Cache-Control': 'public, max-age=86400' },
  })
}
