import { Container } from '@/components'
import { ArticleEntity } from '@/lib/api/entity'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { fetchArticleBySlug, queryStringBuilder } from '@/utils/fetchHelper'
import { LoaderFunction, MetaFunction } from '@remix-run/node'
import { useLoaderData, Link } from '@remix-run/react'
import {
  CalendarDays,
  ChevronLeftCircle,
  ChevronRightCircle,
  FileText,
} from 'lucide-react'
import { Loader } from '@/components/loader'
import { Button } from '@/components/ui/button'
import { SocialButtons } from '@/components/socialButtons'
import { useTranslation } from 'react-i18next'
import { Separator } from '@/components/ui/separator'
import { defineRoute, withCors } from '@/utils/routeHelper'
import { format } from 'date-fns'
import { enUS, th } from 'date-fns/locale'
import { useState, useCallback } from 'react'
import { strapiGetArticles } from '@/lib/api/strapi/article'
import { warperCollectionType } from '@/utils/strapiHelper'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { PaginationQuery } from '@/lib/api/types'
import useClientTask from '@/hooks/use-client-task'
import { LANGUAGE, mappingLanguage } from '@/constants/language'
import { getLocaleParam } from '@/utils/localeHelper'
import { HelmetSEO } from '@/components/helmet/helmet-seo'

type LoaderData = {
  dataArticle: ArticleEntity
  locale: string
}

export const meta: MetaFunction = ({ data }) => {
  const { dataArticle } = data as LoaderData
  return buildTags(dataArticle?.seo)
}

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const { slug, lng } = defineRoute(request)

  const dataArticle = await fetchArticleBySlug(
    slug,
    lng,
    queryStringBuilder([
      'seo',
      'seo',
      'seo.metaSocial',
      'seo.metaImage',
      'image',
    ])
  )

  return { dataArticle, locale: lng }
}

export default function ArticleDetailPage(): React.JSX.Element {
  const { t } = useTranslation('article')
  const { dataArticle, locale } = useLoaderData<LoaderData>()
  const [relatedArticles, setRelatedArticles] = useState<ArticleEntity[]>([])
  const [navigationArticles, setNavigationArticles] = useState<{
    next: ArticleEntity | null
    previous: ArticleEntity | null
  }>({ next: null, previous: null })

  const formatDate = dataArticle?.publishedAt
    ? format(new Date(dataArticle.publishedAt), 'MMM dd, y', {
        locale: locale === mappingLanguage[LANGUAGE.ENG] ? enUS : th,
      })
    : ''

  const fetchArticles = useCallback(async () => {
    const [relatedResponse, nextResponse, previousResponse] = await Promise.all(
      [
        strapiGetArticles(
          queryStringBuilder(['image'], undefined, ['publishedAt:desc'], {
            limit: 5,
            start: 0,
          } as PaginationQuery),
          locale
        ),
        strapiGetArticles(
          queryStringBuilder(
            [],
            { publishedAt: { $lt: dataArticle.publishedAt } },
            undefined,
            { limit: 1 } as PaginationQuery
          ),
          locale
        ),
        strapiGetArticles(
          queryStringBuilder(
            [],
            { publishedAt: { $gt: dataArticle.publishedAt } },
            undefined,
            { limit: 1 } as PaginationQuery
          ),
          locale
        ),
      ]
    )

    setRelatedArticles(warperCollectionType(relatedResponse) ?? [])
    setNavigationArticles({
      next: warperCollectionType(nextResponse)?.[0] ?? null,
      previous: warperCollectionType(previousResponse)?.[0] ?? null,
    })
  }, [dataArticle.publishedAt, locale])

  const renderArticleCard = (article: ArticleEntity) => (
    <div
      key={`article-card-${article.title}`}
      className="w-full as flex gap-x-2"
    >
      <StrapiImage
        className="w-full h-fit rounded-xl max-w-24 shadow-md shadow-primary-400 aspect-square object-cover"
        classNameFallBack="bg-image-coming"
        value={article.image}
      />
      <div className="w-full flex flex-col gap-y-2 flex-1 h-full">
        <p>{article.title}</p>
        <Button
          asChild
          className="!p-0 w-fit underline mt-auto"
          variant="linkDark"
        >
          <Link
            to={{
              pathname: `/article/${encodeURIComponent(article.slug)}`,
              search: getLocaleParam(locale),
            }}
            reloadDocument
          >
            {t('readMore')}
          </Link>
        </Button>
      </div>
    </div>
  )

  const renderNavigationButton = (
    article: ArticleEntity | null,
    direction: 'prev' | 'next'
  ) =>
    article ? (
      <Link
        to={{
          pathname: `/article/${encodeURIComponent(article.slug)}`,
          search: getLocaleParam(locale),
        }}
        reloadDocument
      >
        <Button className="gap-x-2" size="sm">
          {direction === 'prev' && (
            <ChevronLeftCircle className="size-6 shrink-0" />
          )}
          {direction === 'prev' ? t('prev') : t('next')}
          {direction === 'next' && (
            <ChevronRightCircle className="size-6 shrink-0" />
          )}
        </Button>
      </Link>
    ) : null

  const { isTaskPending } = useClientTask(fetchArticles)
  return (
    <div className="w-full flex flex-col pb-6">
      <HelmetSEO data={dataArticle.seo} />
      <div className="bg-primary flex flex-col w-full py-8 text-white">
        <Container>
          <div className="flex gap-12 py-8">
            <div className="flex gap-2 items-end">
              <FileText className="size-6 shrink-0" />
              <h4 className="font-light leading-none">{t('article')}</h4>
            </div>
            <div className="flex gap-2 items-end">
              <CalendarDays className="size-6 shrink-0" />
              <h4 className="font-light leading-none">{formatDate}</h4>
            </div>
          </div>
          <h2>{dataArticle.title}</h2>
          {dataArticle?.subtitle && (
            <h5 className="font-light mt-5">{dataArticle.subtitle}</h5>
          )}
        </Container>
      </div>
      <Container className="py-8 flex gap-x-8 lg:justify-between">
        <div className="w-full flex flex-col">
          <span
            className="typography ck ck-content"
            dangerouslySetInnerHTML={{ __html: dataArticle.content ?? '' }}
          />
          <Separator className="my-8" />
          {isTaskPending ? (
            <div className="flex justify-center items-center w-full">
              <Loader />
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-4">
              <div className="text-left">
                {renderNavigationButton(navigationArticles.previous, 'prev')}
              </div>
              <div className="text-right">
                {renderNavigationButton(navigationArticles.next, 'next')}
              </div>
            </div>
          )}
        </div>
        <aside className="w-80 flex flex-col gap-y-8 shrink-0 max-lg:hidden">
          <h3 className="text-primary">{t('related')}</h3>
          {isTaskPending ? (
            <div className="flex justify-center items-center w-full">
              <Loader />
            </div>
          ) : (
            <div className="flex flex-col w-full gap-y-4">
              {relatedArticles.map(renderArticleCard)}
            </div>
          )}

          <div className="w-full flex gap-x-2 items-center">
            <h3 className="text-primary w-fit">{t('follow')}</h3>
            <SocialButtons className="flex gap-2" />
          </div>
        </aside>
      </Container>
    </div>
  )
}
