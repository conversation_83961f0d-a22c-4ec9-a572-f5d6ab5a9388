import { Container } from '@/components'
import { ProfileRegistrationForm } from '@/components/form/profileRegistration'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { Title } from '@/components/title'
import { Button } from '@/components/ui/button'
import { PageEntity, ServiceEntity } from '@/lib/api/entity'
import { strapiGetServices } from '@/lib/api/strapi'
import { CheckMarkIcon } from '@/lib/assets/check-mark-icon'
import { cn } from '@/lib/utils'
import { fetchPageBySlug, queryStringBuilder } from '@/utils/fetchHelper'
import { getLocaleParam } from '@/utils/localeHelper'
import { defineRoute, withCors } from '@/utils/routeHelper'
import { warperCollectionType } from '@/utils/strapiHelper'
import { Link, useLoaderData } from '@remix-run/react'
import { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { LoaderFunction, MetaFunction } from '@remix-run/node'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { HelmetSEO } from '@/components/helmet/helmet-seo'

type LoaderData = {
  dataPage: PageEntity
  dataService: ServiceEntity[]
  locale: string
}

export const meta: MetaFunction = ({ data }) => {
  const { dataPage } = data as LoaderData
  return buildTags(dataPage.seo)
}

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const { slug, lng } = defineRoute(request)

  const [responsePage, responseService] = await Promise.all([
    fetchPageBySlug(
      slug,
      lng,
      queryStringBuilder([
        'backgroundImage',
        'seo',
        'seo.metaSocial',
        'seo.metaImage',
      ])
    ),
    strapiGetServices(queryStringBuilder(['header', 'thumbnail']), lng),
  ])

  return {
    dataPage: responsePage,
    dataService: warperCollectionType(responseService),
    locale: lng,
  }
}

export default function ProfileRegistrationPage() {
  const { dataService, dataPage, locale } = useLoaderData<LoaderData>()
  const { t } = useTranslation('profile-registration')
  const [isSuccess, setIsSuccess] = useState<boolean>(false)
  const { backgroundImage } = dataPage
  const handleSuccess = useCallback(() => setIsSuccess((prev) => !prev), [])
  const serviceOptions = useMemo(
    () => [
      ...dataService.map((service) => ({
        label: service.name as string,
        value: service.slug as string,
      })),
      {
        label: t('form.wantedService.others.label'),
        value: 'other',
      },
    ],
    [dataService, t]
  )

  return (
    <div
      className={cn(
        'justify-center items-center',
        !isSuccess && 'min-h-screen flex flex-col'
      )}
    >
      <HelmetSEO data={dataPage.seo} />
      {backgroundImage?.data && (
        <StrapiImage
          className="animate-in fade-in duration-500 w-full mb-5"
          value={backgroundImage}
        />
      )}
      {isSuccess ? (
        <Container className="mb-20 text-center animate-in fade-in zoom-in duration-500 py-20">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mx-auto mb-6">
            <CheckMarkIcon className="w-20 h-20 text-green-600" />
          </div>
          <h2 className="text-2xl font-semibold text-green-700 my-5">
            {t('form.success')}
          </h2>
          <p className="text-gray-600 max-w-md mx-auto">{t('form.thankYou')}</p>

          <Link
            to={{
              pathname: '/',
              search: getLocaleParam(locale),
            }}
            className="animate-in slide-in-from-right duration-500"
            aria-label="Learn more our service"
          >
            <Button className="mt-10 px-6 py-3  text-white rounded-xl text-sm sm:text-base font-medium transition duration-300">
              {t('form.backToHome')}
            </Button>
          </Link>
        </Container>
      ) : (
        <>
          <Title
            className="animate-in fade-in zoom-in duration-500 my-10"
            title={t('title')}
          />
          <Container className="mb-20">
            <ProfileRegistrationForm
              services={serviceOptions}
              onSuccess={handleSuccess}
            />
          </Container>
        </>
      )}
    </div>
  )
}
