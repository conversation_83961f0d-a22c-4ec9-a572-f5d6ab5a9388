import { AboutUsEntity, PageEntity } from '@/lib/api/entity'
import { fetchPageBySlug, queryStringBuilder } from '@/utils/fetchHelper'
import { useLoaderData } from '@remix-run/react'
import { LoaderFunction, MetaFunction } from '@remix-run/node'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { strapiGetAboutUs } from '@/lib/api/strapi'
import { warperSingleType } from '@/utils/strapiHelper'
import { StrapiContentMedia } from '@/components/strapi/strapi-content-media'
import { defineRoute, withCors } from '@/utils/routeHelper'
import { Container } from '@/components'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { HelmetSEO } from '@/components/helmet/helmet-seo'
type LoaderData = {
  dataPage: PageEntity
  dataAboutUs: AboutUsEntity
  locale: string
}

export const meta: MetaFunction = ({ data }) => {
  const { dataPage } = data as LoaderData
  return buildTags(dataPage.seo)
}

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const { slug, lng } = defineRoute(request)

  const [responsePage, responseAboutUs] = await Promise.all([
    fetchPageBySlug(
      slug,
      lng,
      queryStringBuilder([
        'backgroundImage',
        'seo',
        'seo.metaSocial',
        'seo.metaImage',
      ])
    ),
    strapiGetAboutUs(
      queryStringBuilder([
        'profile',
        'profile.media',
        'experience',
        'experience.media',
        'main',
        'main.media',
      ]),
      lng
    ),
  ])

  return {
    dataPage: responsePage,
    dataAboutUs: warperSingleType(responseAboutUs),
    locale: lng,
  }
}

const AboutUsPage = () => {
  const {
    dataAboutUs: { profile, experience, landing, main, mainDescription },
    dataPage: { backgroundImage, seo = null },
  } = useLoaderData<LoaderData>()

  if (!profile && !experience && !landing && !main && !mainDescription)
    return null

  return (
    <div>
      <HelmetSEO data={seo} />
      <StrapiImage
        value={backgroundImage}
        className="animate-in fade-in duration-500 w-full h-fit"
      />
      <StrapiContentMedia
        data={profile}
        className="bg-background-primary text-white"
      />
      <StrapiContentMedia
        data={experience}
        className="bg-background-primary text-white"
      />
      <StrapiContentMedia
        data={main}
        className="bg-background-secondary text-primary"
      />
      {mainDescription && (
        <div className="bg-background-secondary text-primary">
          <Container className="pb-20 pt-5">
            <div
              className="ck ck-content"
              dangerouslySetInnerHTML={{ __html: mainDescription }}
            />
          </Container>
        </div>
      )}
    </div>
  )
}

// eslint-disable-next-line import/no-default-export
export default AboutUsPage
