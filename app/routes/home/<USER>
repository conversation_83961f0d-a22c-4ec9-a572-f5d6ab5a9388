import { Container } from '@/components'
import { Loader } from '@/components/loader'
// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
// import { cn } from '@/lib/utils'
import { FC, useMemo, useState } from 'react'
import { InstagramEmbed } from 'react-social-media-embed'
import useClient from '@/hooks/use-client'

// type SocialType = {
//   label: string
//   value: string
//   icon: string
// }

type SocialProps = {
  locale: string
}

const Social: FC<SocialProps> = ({ locale }) => {
  const [selectTab] = useState<string>('instagram')
  const { isClient } = useClient()

  // const socialList: SocialType[] = [
  //   {
  //     label: 'Instagram',
  //     value: 'instagram',
  //     icon: 'ri-instagram-fill text-base',
  //   },
  //   { label: 'Tiktok', value: 'tiktok', icon: 'ri-tiktok-fill text-base' },
  // ]

  const renderToSocial: React.JSX.Element = useMemo(() => {
    switch (selectTab) {
      case 'instagram':
        return (
          <InstagramEmbed
            className="bg-transparent"
            url={`https://www.instagram.com/aims.clinic/?hl=${locale}`}
            width="100%"
          />
        )

      // case 'tiktok':
      //   return (
      //     <div className="grid justify-center">
      //       <iframe
      //         className="h-[388px] w-full md:h-[495px] bg-transparent mb-5 md:w-[780px] rounded-b-2xl"
      //         src={`https://www.tiktok.com/embed/@dr.amy.aimsclinic?lang=${locale}`}
      //         title="TikTok Video"
      //         allowFullScreen
      //         allow="encrypted-media"
      //       />
      //     </div>
      //   )
      default:
        return <></>
    }
  }, [locale, selectTab])

  return (
    <div className="bg-primary/20">
      <Container>
        {isClient ? (
          <>
            {/* <Tabs
              defaultValue={selectTab}
              className="text-center mt-2 animate-in fade-in slide-in-from-left"
              onValueChange={setSelectTab}
            >
              <TabsList>
                {socialList.map((social) => (
                  <TabsTrigger key={`tab-${social.label}`} value={social.value}>
                    <i className={cn('mr-2', social.icon)} />
                    {social.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs> */}
            <div className="my-2 animate-in fade-in duration-500">
              {renderToSocial}
            </div>
          </>
        ) : (
          <div className="pt-10 justify-items-center">
            <Loader />
          </div>
        )}
      </Container>
    </div>
  )
}

export default Social
