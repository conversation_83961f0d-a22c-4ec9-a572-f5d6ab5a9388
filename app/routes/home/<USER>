import { FC, useCallback, useMemo, useState } from 'react'
import { Container } from '@/components'
import { Button } from '@/components/ui/button'
import { PromotionEntity } from '@/lib/api/entity'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { Link } from '@remix-run/react'
import { ChevronRightIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Badge } from '@/components/ui/badge'
import { PromotionDialog } from '@/components/dialog'
import useClientTask from '@/hooks/use-client-task'
import { strapiGetPromotions } from '@/lib/api/strapi'
import { queryStringBuilder } from '@/utils/fetchHelper'
import { warperCollectionType } from '@/utils/strapiHelper'
import { Loader } from '@/components/loader'
import { getLocaleParam } from '@/utils/localeHelper'

type PromotionProps = {
  locale: string
}

type SelectTagType = {
  id: number
  value: string
}

type TagsProps = {
  options: SelectTagType[]
  select: SelectTagType
  onClick: (tag: SelectTagType) => void
}

type ImageCarouselProps = {
  data: PromotionEntity[]
  select: SelectTagType
}

const Tags: FC<TagsProps> = ({ options, select, onClick }) => {
  if (options.length === 0) return null

  return (
    <div className="w-full flex flex-wrap gap-2 mt-4 animate-in fade-in duration-500">
      {options.map((opt) => (
        <Badge
          key={`badge-promotion-${opt.id}-${opt.value}`}
          onClick={() => onClick(opt)}
          className="text-white font-normal text-xs md:text-sm cursor-pointer px-4"
          variant={select.id === opt.id ? 'default' : 'secondary'}
        >
          {opt.value}
        </Badge>
      ))}
    </div>
  )
}

const ImageCarousel: FC<ImageCarouselProps> = ({ data, select }) => {
  const carouselItems = useMemo(() => {
    return data
      .filter((item) => {
        const tags = (
          item.services ? warperCollectionType(item.services) : []
        )?.[0]?.tag
        return select.id === 0 || tags === select.value
      })
      .map(({ createdAt, image, id }) => (
        <CarouselItem
          key={`home-promotion-${id}-${image?.data?.attributes?.name}-${createdAt}`}
          className="sm:basis-1/1 md:basis-1/2 lg:basis-1/3 animate-in fade-in duration-500"
        >
          <div className="animate-in zoom-in transform transition duration-300 hover:scale-105 p-2">
            <PromotionDialog image={image} />
          </div>
        </CarouselItem>
      ))
  }, [data, select])

  if (carouselItems.length === 0) return null

  return (
    <div className="w-full flex justify-center flex-1 px-10 mt-8 animate-in zoom-in duration-500">
      <Carousel
        className="w-full max-w-full"
        opts={{ align: 'start' }}
        isShowDots
      >
        <CarouselContent>{carouselItems}</CarouselContent>
        <CarouselPrevious className="text-secondary" />
        <CarouselNext className="text-secondary" />
      </Carousel>
    </div>
  )
}

export const Promotion: FC<PromotionProps> = ({ locale }) => {
  const { t } = useTranslation('home')
  const [dataPromotion, setDataPromotion] = useState<PromotionEntity[]>([])
  const [selectTag, setSelectTag] = useState<SelectTagType>({
    value: t('promotion.all'),
    id: 0,
  })

  const handleTagClicked = useCallback((value: SelectTagType) => {
    setSelectTag(value)
  }, [])

  const tags: SelectTagType[] = useMemo(() => {
    const uniqueTagsMap = new Map<string, SelectTagType>()

    dataPromotion.forEach((item, index) => {
      const services = item.services ? warperCollectionType(item.services) : []

      const tagValue = services?.length ? services?.[0]?.tag : null

      if (tagValue && !uniqueTagsMap.has(tagValue)) {
        uniqueTagsMap.set(tagValue, { value: tagValue, id: index + 1 })
      }
    })

    return [...uniqueTagsMap.values(), { value: t('promotion.all'), id: 0 }]
  }, [dataPromotion, t])

  const fetchPromotion = useCallback(async () => {
    const response = await strapiGetPromotions(
      queryStringBuilder(['image', 'services']),
      locale
    )
    setDataPromotion(warperCollectionType(response) ?? [])
  }, [locale])

  const { isTaskPending } = useClientTask(fetchPromotion)

  return (
    <div className="bg-secondary/25">
      <Container>
        <div className="w-full flex flex-col py-12">
          <header className="w-full flex justify-between items-center border-b-2 border-secondary pb-3">
            <h2 className="text-secondary text-xl md:text-2xl lg:text-3xl animate-in slide-in-from-left duration-500">
              {t('promotion.title')}
            </h2>
            <Link
              to={{
                pathname: '/promotion',
                search: getLocaleParam(locale),
              }}
              className="animate-in slide-in-from-right duration-500"
              aria-label="Learn more promotion"
            >
              <Button
                className=" text-white font-bold see-more-btn"
                variant="secondary"
                aria-label="Learn more promotion"
              >
                {t('promotion.seeMore')}
                <div className="chevron-btn">
                  <ChevronRightIcon className="w-4 h-4 text-white" />
                </div>
              </Button>
            </Link>
          </header>
          {isTaskPending ? (
            <div className="flex justify-center items-center w-full">
              <Loader />
            </div>
          ) : (
            <>
              <Tags
                options={tags}
                select={selectTag}
                onClick={handleTagClicked}
              />
              <ImageCarousel data={dataPromotion} select={selectTag} />
            </>
          )}
        </div>
      </Container>
    </div>
  )
}
