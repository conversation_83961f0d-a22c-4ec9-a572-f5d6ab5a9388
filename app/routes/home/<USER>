import BannerCarousel from '@/components/carousel/bannerCarousel'
import CoverBanner from '@/components/carousel/coverBanner'
import { ImageType, PageEntity } from '@/lib/api/entity'
import { FC, useMemo } from 'react'

type BannerProps = {
  data: PageEntity
  locale: string
}

export const Banner: FC<BannerProps> = ({ data, locale }) => {
  const images: ImageType[] = useMemo(
    () =>
      data.images?.data?.flatMap((item) =>
        item?.attributes ? [item.attributes] : []
      ) ?? [],
    [data.images?.data]
  )
  return (
    <div className="relative w-full h-full max-h-fit">
      <BannerCarousel
        className="animate-in fade-in duration-500"
        images={images}
        carouselKey="home-banner"
      />
      <CoverBanner
        locale={locale}
        className="animate-in fade-in duration-500"
      />
    </div>
  )
}
