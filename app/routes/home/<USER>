import { FC, memo, useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Link } from '@remix-run/react'
import { ChevronRightIcon } from '@heroicons/react/24/solid'

import { Container } from '@/components'
import { Button } from '@/components/ui/button'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { Card } from '@/components/ui/card'

import { ReviewEntity } from '@/lib/api/entity'
import { strapiGetReviews } from '@/lib/api/strapi/review'
import { queryStringBuilder } from '@/utils/fetchHelper'
import { warperCollectionType } from '@/utils/strapiHelper'

import useClientTask from '@/hooks/use-client-task'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { Loader } from '@/components/loader'
import { getLocaleParam } from '@/utils/localeHelper'

type ReviewProps = {
  locale: string
}

const ReviewHeader: FC<{ locale: string }> = memo(({ locale }) => {
  const { t } = useTranslation('home')

  return (
    <div className="w-full grid grid-cols-3 gap-4 items-center">
      <div className="col-span-2 text-primary text-start sm:text-center animate-in fade-in slide-in-from-left duration-500 sm:pl-48 lg:pl-80 xl:pl-96">
        <h2>{t('review.title')}</h2>
        <h3 className="font-normal">{t('review.detail')}</h3>
      </div>
      <Link
        to={{
          pathname: '/review',
          search: getLocaleParam(locale),
        }}
        className="animate-in slide-in-from-right duration-500 justify-self-end"
        aria-label="Learn more review"
      >
        <Button
          variant="primary"
          className="bg-primary text-white see-more-btn font-bold"
          aria-label="Learn more review"
        >
          {t('ourService.seeMore')}
          <div className="chevron-btn">
            <ChevronRightIcon className="w-4 h-4 text-white" />
          </div>
        </Button>
      </Link>
    </div>
  )
})

const ReviewCarousel: FC<{ data: ReviewEntity[] }> = memo(({ data }) => (
  <Carousel
    className="w-full"
    dotsClassName="!ring-primary-900 [&.active]:bg-primary-900"
    opts={{ align: 'start' }}
    isShowDots
  >
    <CarouselContent>
      {data.map((review) => (
        <CarouselItem
          key={review.id}
          className="md:basis-1/2 lg:basis-1/3 aspect-square"
        >
          <div className="p-1 animate-in zoom-in duration-500">
            <Card className="bg-opacity-80 bg-transparent border-none shadow-none">
              <StrapiImage
                className="rounded-2xl shadow-md shadow-primary-400"
                classNameFallBack="bg-no-image w-full h-full"
                value={review.image}
              />
            </Card>
          </div>
        </CarouselItem>
      ))}
    </CarouselContent>
    <CarouselPrevious className="-left-12 text-primary" />
    <CarouselNext className="-right-12 text-primary" />
  </Carousel>
))

const Review: FC<ReviewProps> = ({ locale }) => {
  const [dataReview, setDataReview] = useState<ReviewEntity[]>([])

  const fetchReview = useCallback(async () => {
    const response = await strapiGetReviews(
      queryStringBuilder(['image'], { $and: [{ isShowOnHome: true }] }),
      locale
    )
    setDataReview(warperCollectionType(response) ?? [])
  }, [locale])

  const { isTaskPending } = useClientTask(fetchReview)

  return (
    <div className="bg-secondary/25">
      <Container>
        <div className="w-full flex flex-col gap-y-8 py-12">
          <ReviewHeader locale={locale} />
          <div className="w-full px-10 flex justify-center">
            {isTaskPending ? (
              <div className="py-5">
                <Loader />
              </div>
            ) : (
              <ReviewCarousel data={dataReview} />
            )}
          </div>
        </div>
      </Container>
    </div>
  )
}

export default memo(Review)
