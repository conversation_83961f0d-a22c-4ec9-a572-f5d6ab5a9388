import { Container } from '@/components'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { Link } from '@remix-run/react'
import { ChevronRightIcon } from '@heroicons/react/24/solid'
import { Play } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import { FC, useMemo } from 'react'
import { MainMenuEntity, SubMainMenuField } from '@/lib/api/entity'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { getLocaleParam } from '@/utils/localeHelper'

type OurServiceProps = {
  data: MainMenuEntity | null
  locale: string
}

type CardLinkProps = {
  data: SubMainMenuField | null
  url: string | null
  locale: string
}

type CardReviewProps = {
  data: SubMainMenuField | null
}

export const OurService: FC<OurServiceProps> = ({ data, locale }) => {
  const { t } = useTranslation('home')

  const filterMenu = useMemo(
    () => data?.mainMenuItems?.find((_) => _.url === 'our-service'),
    [data]
  )
  if (!filterMenu) return null

  return (
    <div className="bg-gradient-to-r from-primary-700 via-primary-700 to-primary">
      <Container>
        <div className="w-full flex flex-col py-12">
          <div className="w-full flex justify-between items-center mb-6">
            <h2 className="text-white animate-in slide-in-from-left duration-500">
              {t('ourService.title')}
            </h2>
            <Link
              to={{
                pathname: '/our-service',
                search: getLocaleParam(locale),
              }}
              className="animate-in slide-in-from-right duration-500"
              aria-label="Learn more our service"
            >
              <Button
                className="see-more-btn text-primary font-bold "
                aria-label="Learn more our service"
                variant="tertiary"
              >
                {t('ourService.seeMore')}
                <div className="chevron-btn">
                  <ChevronRightIcon className="size-4 text-primary" />
                </div>
              </Button>
            </Link>
          </div>
          {/* web */}
          <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filterMenu.subMenuLink?.map((item) => (
              <div
                className="flex flex-col"
                key={`home-our-service-web-${item.id}-${item.name}`}
              >
                <div className="lg:basis-1/2 xl:basis-1/3">
                  <CardReview data={item} />
                </div>
                <CardLink url={filterMenu.url} data={item} locale={locale} />
              </div>
            ))}
          </div>
          {/* mobile */}
          <div className="w-full flex justify-center flex-1 mt-8 px-2 md:px-8 lg:px-10">
            <Carousel
              className="block md:hidden w-full max-w-full"
              opts={{ align: 'start' }}
              isShowDots
            >
              <CarouselContent>
                {filterMenu.subMenuLink?.map((item) => (
                  <CarouselItem
                    key={`home-our-service-mobile-${item.id}}`}
                    className="lg:basis-1/2 xl:basis-1/3"
                  >
                    <CardReview data={item} />
                    <CardLink
                      url={filterMenu.url}
                      data={item}
                      locale={locale}
                    />
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="text-secondary" />
              <CarouselNext className="text-secondary" />
            </Carousel>
          </div>
        </div>
      </Container>
    </div>
  )
}

const CardReview: FC<CardReviewProps> = ({ data }) => {
  return (
    <Card className="bg-primary-300 rounded-[50px] border-none">
      <CardContent className="!p-0 flex aspect-square items-center justify-center w-full h-80">
        <StrapiImage
          value={data?.image}
          className="rounded-t-[50px] object-cover w-full h-80"
        />
      </CardContent>
      <CardFooter className="rounded-b-[50px] block bg-secondary text-center font-bold text-white pt-3">
        <div
          className="ck ck-content"
          dangerouslySetInnerHTML={{ __html: data?.structuredData ?? '' }}
        />
      </CardFooter>
    </Card>
  )
}

const CardLink: FC<CardLinkProps> = ({ data, url, locale }) => {
  return (
    <div className="flex flex-col md:flex-col items-start py-4 px-6 md:px-16 text-white gap-0 md:gap-2">
      {data?.menu ? (
        data.menu.map((item) => (
          <Link
            aria-label={`Learn more about ${item.name}`}
            to={{
              pathname: `${url}/${item.url}`,
              search: getLocaleParam(locale),
            }}
            key={`our-service-card-link-${url}/${item.url}`}
          >
            <Button
              aria-label={`Learn more about ${item.name}`}
              className="text-white flex gap-1 font-bold text-base lg:text-md"
              size="link"
              variant="link"
            >
              <Play className="text-white" fill="white" size={14} />
              {item.name}
            </Button>
          </Link>
        ))
      ) : (
        <Link
          aria-label={`Learn more about ${data?.name}`}
          to={{
            pathname: `${url}/${data?.url}`,
            search: getLocaleParam(locale),
          }}
          key={`our-service-card-link-${url}/${data?.url}`}
        >
          <Button
            aria-label={`Learn more about ${data?.name}`}
            className="text-white font-thin flex gap-1"
            size={'link'}
            variant={'link'}
          >
            <Play className="text-white" fill="white" size={14} />
            {data?.name}
          </Button>
        </Link>
      )}
    </div>
  )
}
