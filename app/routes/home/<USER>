import { Button } from '@/components/ui/button'
import { ClockIcon } from '@heroicons/react/24/outline'
import { ContactEntity } from '@/lib/api/entity'
import { Container } from '@/components'
import { EnvelopeIcon, MapPinIcon, PhoneIcon } from '@heroicons/react/24/solid'
import { FC, useMemo } from 'react'
import { LongdoMap } from '@/components/longdo-map'
import { StrapiBackgroundImage } from '@/components/strapi/strapi-background-image'
import useClient from '@/hooks/use-client'
import { Loader } from '@/components/loader'

type ContactProps = {
  data: ContactEntity
  locale: string
}

export const Contact: FC<ContactProps> = ({ data, locale }) => {
  const { isClient } = useClient()
  const {
    title,
    subTitle,
    openTitle,
    openTime,
    address,
    telephone,
    email,
    map,
    backgroundImage,
  } = data

  const addressLines = useMemo(() => {
    return (
      address?.flatMap((addr) => addr.children).map((item) => item.text) ||
      undefined
    )
  }, [address])

  return (
    <StrapiBackgroundImage value={backgroundImage}>
      <Container className="grid grid-cols-1 md:grid-cols-5 gap-6 md:gap-8 p-4 md:p-8 text-primary">
        <div className="md:place-self-center rounded-2xl shadow-2xl p-6 md:p-8 md:col-span-2 col-span-1 grid auto-cols-auto bg-white opacity-90 max-w-full md:max-w-sm gap-4 animate-in zoom-in duration-500">
          <div className="col-span-2">
            {title && (
              <p className="text-3xl md:text-4xl font-extrabold text-center mb-2">
                {title}
              </p>
            )}
            {subTitle && (
              <p className="text-xl md:text-2xl font-bold text-center">
                {subTitle}
              </p>
            )}
          </div>
          {openTitle && openTime && (
            <>
              <ClockIcon className="size-9 md:size-12" />
              <div>
                <p className="text-sm md:text-base">{openTitle}</p>
                <p className="text-sm md:text-base">{openTime}</p>
              </div>
            </>
          )}

          {addressLines && (
            <>
              <Button variant="primary" size="icon" aria-label="Address icon">
                <MapPinIcon className="size-4 md:size-7" />
              </Button>
              <div>
                {addressLines.map((line) => (
                  <p key={`address-${line}`} className="text-sm md:text-base">
                    {line}
                  </p>
                ))}
              </div>
            </>
          )}

          {telephone && (
            <>
              <Button variant="primary" size="icon" aria-label="Telephone icon">
                <PhoneIcon className="size-4 md:size-7" />
              </Button>
              <p className="text-sm md:text-base pt-1">{telephone}</p>
            </>
          )}
          {email && (
            <>
              <Button variant="primary" size="icon" aria-label="Email icon">
                <EnvelopeIcon className="size-4 md:size-7" />
              </Button>
              <p className="text-sm md:text-base pt-1">{email}</p>
            </>
          )}
        </div>

        {map && (
          <div className="col-span-1 md:col-span-3 h-64 md:h-auto animate-in zoom-in duration-500">
            {isClient ? (
              <LongdoMap
                id="longdo-map"
                data={map}
                locale={locale}
                className="w-full h-full long-do-map opacity-80 animate-in zoom-in duration-500"
              />
            ) : (
              <div className="flex justify-center items-center w-full">
                <Loader />
              </div>
            )}
          </div>
        )}
      </Container>
    </StrapiBackgroundImage>
  )
}
