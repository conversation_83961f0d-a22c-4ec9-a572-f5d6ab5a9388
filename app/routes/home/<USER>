import { FC } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { AboutUsEntity } from '@/lib/api/entity'
import { ChevronRightIcon } from '@heroicons/react/24/solid'
import { Link } from '@remix-run/react'
import { useTranslation } from 'react-i18next'
import { StrapiContentMultiMedia } from '@/components/strapi/strapi-content-multi-media'
import { getLocaleParam } from '@/utils/localeHelper'

type AboutUsProps = {
  data: AboutUsEntity
  locale: string
}

export const AboutUs: FC<AboutUsProps> = ({ data, locale }) => {
  const { t } = useTranslation('home')
  const { landing } = data

  return (
    <StrapiContentMultiMedia
      clientRender
      data={landing}
      className="bg-background-primary"
      contentChild={
        <Link
          to={{
            pathname: '/about-us',
            search: getLocaleParam(locale),
          }}
          aria-label="Learn more about us"
        >
          <Button
            className="bg-white text-primary see-more-btn font-bold"
            variant="tertiary"
            aria-label="Learn more about us"
          >
            {t('aboutUs.more')}
            <div className="chevron-btn">
              <ChevronRightIcon className="w-4 h-4 text-primary" />
            </div>
          </Button>
        </Link>
      }
    />
  )
}
