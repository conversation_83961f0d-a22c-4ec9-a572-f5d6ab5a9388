// import { routes } from "@remix-run/dev/server-build"
import type { LoaderFunctionArgs } from '@remix-run/node'

import { generateSitemap } from '@/lib/server/sitemap/sitemap.server.js'
import { fetchDynamicSlugs } from '@/utils/fetchHelper.js'
import { STRAPI_ARTICLE, STRAPI_SERVICES } from '@/constants/http.js'
import { i18nConfig } from '@/lib/server/translations/i18n.config.js'

export async function loader({ request }: LoaderFunctionArgs) {
  const [build, articlesSlugs, servicesSlugs] = await Promise.all([
    import.meta.env.DEV
      ? import('../../build/server/index.js')
      : import(
          /* @vite-ignore */
          import.meta.resolve('../index.js')
        ),
    fetchDynamicSlugs(STRAPI_ARTICLE, i18nConfig.fallbackLng),
    fetchDynamicSlugs(STRAPI_SERVICES, i18nConfig.fallbackLng),
  ])

  return generateSitemap(request, build.routes, {
    siteUrl: import.meta.env.VITE_HOST_URL ?? '',
    articlesSlugs,
    servicesSlugs,
  })
}
