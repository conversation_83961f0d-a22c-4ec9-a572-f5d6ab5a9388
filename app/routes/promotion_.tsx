import { Container } from '@/components'
import { useLoaderData } from '@remix-run/react'
import { LoaderFunction, MetaFunction } from '@remix-run/node'
import { Title } from '@/components/title'
import { PageEntity, PromotionEntity } from '@/lib/api/entity'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { strapiGetPromotions } from '@/lib/api/strapi/promotion'
import { warperCollectionType } from '@/utils/strapiHelper'
import { fetchPageBySlug, queryStringBuilder } from '@/utils/fetchHelper'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { PromotionDialog } from '@/components/dialog'
import { defineRoute } from '@/utils/routeHelper'
import { useCallback, useState } from 'react'
import { Loader } from '@/components/loader'
import useClientTask from '@/hooks/use-client-task'
import { useTranslation } from 'react-i18next'
import { HelmetSEO } from '@/components/helmet/helmet-seo'

type LoaderData = {
  dataPage: PageEntity
  locale: string
}

export const meta: MetaFunction = ({ data }) => {
  const { dataPage } = data as LoaderData
  return buildTags(dataPage.seo)
}

export const loader: LoaderFunction = async ({ request }) => {
  const { slug, lng } = defineRoute(request)
  const [responsePage] = await Promise.all([
    fetchPageBySlug(
      slug,
      lng,
      queryStringBuilder([
        'backgroundImage',
        'seo',
        'seo.metaSocial',
        'seo.metaImage',
      ])
    ),
  ])
  return {
    dataPage: responsePage,
    locale: lng,
  }
}

const PromotionPage = () => {
  const { t } = useTranslation('promotion')
  const { locale, dataPage } = useLoaderData<LoaderData>()
  const [dataPromotion, setDataPromotion] = useState<PromotionEntity[]>([])

  const fetchPromotion = useCallback(async () => {
    const response = await strapiGetPromotions(
      queryStringBuilder(['image']),
      locale
    )
    setDataPromotion(warperCollectionType(response) ?? [])
  }, [locale])

  const { isTaskPending } = useClientTask(fetchPromotion)

  return (
    <div className="min-h-screen flex flex-col justify-center items-center">
      <HelmetSEO data={dataPage.seo} />
      <StrapiImage
        className="animate-in fade-in duration-500 w-full mb-5"
        value={dataPage.backgroundImage}
      />
      <Title
        className="animate-in fade-in zoom-in duration-500 my-10"
        caption={t('caption')}
        title={t('title')}
      />
      {isTaskPending ? (
        <Loader />
      ) : (
        <Container className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5 md:gap-10 mb-4 w-full px-10">
          {dataPromotion.map((item: PromotionEntity) => (
            <div
              key={`promotion-${item.id}-${item.image?.data?.attributes?.name}`}
              className="animate-in zoom-in transform transition-all duration-300 hover:scale-105"
            >
              <PromotionDialog
                className="duration-300 hover:scale-105"
                image={item.image}
              />
            </div>
          ))}
        </Container>
      )}
    </div>
  )
}

export default PromotionPage
