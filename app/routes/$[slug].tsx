import { LoaderFunction } from '@remix-run/node'
import { fetchPageBySlug } from '@/utils/fetchHelper'
import { i18nConfig } from '@/lib/server/translations/i18n.config'
import { withCors } from '@/utils/routeHelper'

export const loader: LoaderFunction = async ({ params, request }) => {
  withCors()
  const slug = params.slug ?? ''
  const locale =
    new URL(request.url).searchParams.get('lng') ?? i18nConfig.fallbackLng
  await fetchPageBySlug(slug, locale)
  return null
}

export default function SlugPage() {
  return null
}
