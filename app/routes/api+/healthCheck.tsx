import { APPLICATION_JSON } from '@/constants/http'
import { StrapiApi } from '@/lib/api/strapiApi'
import { LoaderFunction } from '@remix-run/node'

export const loader: LoaderFunction = async () => {
  const strapiApi = new StrapiApi()

  let isAvailable = true

  try {
    await strapiApi.get('_health')
  } catch {
    isAvailable = false
  }

  const status = isAvailable ? 200 : 500
  const data = {
    strapi: isAvailable ? 'Available ✅' : 'Not available ❌',
    status,
    datetime: new Date().toUTCString(),
  }

  return new Response(JSON.stringify(data), {
    status,
    headers: { 'Content-Type': APPLICATION_JSON },
  })
}
