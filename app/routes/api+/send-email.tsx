import { ActionFunction } from '@remix-run/node'
import { APPLICATION_JSON, POST, STRAPI_EMAIL } from '@/constants/http'
import { StrapiApi } from '@/lib/api/strapiApi'
import { withCors } from '@/utils/routeHelper'

export const action: ActionFunction = async ({ request }) => {
  const headers = withCors()
  if (request.method !== POST) {
    return new Response(JSON.stringify({ error: 'Invalid request method' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json', ...headers },
    })
  }
  try {
    const payload = await request.json()

    const strapiApi = new StrapiApi()
    const data = await strapiApi.post(`v1/${STRAPI_EMAIL}`, payload)
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': APPLICATION_JSON },
    })
  } catch {
    return new Response(JSON.stringify({ error: 'Failed to send email' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    })
  }
}
