import type { LoaderFunction } from '@remix-run/server-runtime'
import {
  imageLoader,
  fsResolver,
  fetchResolver,
  Resolver,
  MemoryCache,
} from 'remix-image/server'
import { sharpTransformer } from 'remix-image-sharp'
import { DiskCache } from '@/lib/server/disk-cache.server'

export const myResolver: Resolver = async (asset, url, options, basePath) => {
  if (asset.startsWith('/') && (asset.length === 1 || asset[1] !== '/')) {
    return fsResolver(asset, url, options, basePath)
  } else {
    return fetchResolver(asset, url, options, basePath)
  }
}

const memoryCache = new MemoryCache({
  maxSize: 1024 * 1024 * 1024,
})

const diskCache = new DiskCache()

const config = {
  selfUrl: import.meta.env.VITE_HOST_URL,
  cache: typeof window === 'undefined' ? diskCache : memoryCache,
  resolver: myResolver,
  verbose: true,
  redirectOnFail: true,
  transformer: sharpTransformer,
}

export const loader: LoaderFunction = ({ request }) => {
  return imageLoader(config, request)
}
