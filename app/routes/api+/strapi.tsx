import { LoaderFunction, ActionFunction } from '@remix-run/node'
import { StrapiApi } from '@/lib/api/strapiApi'
import { APPLICATION_JSON } from '@/constants/http'
import { i18nConfig } from '@/lib/server/translations/i18n.config'
import { withCors } from '@/utils/routeHelper'

const strapiApi = new StrapiApi()

const jsonResponse = (data: unknown, status = 200) =>
  new Response(JSON.stringify(data), {
    status,
    headers: { 'Content-Type': APPLICATION_JSON },
  })

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const { searchParams } = new URL(request.url)
  const endpoint = searchParams.get('endpoint')
  const locale = searchParams.get('lng') ?? i18nConfig.fallbackLng

  if (!endpoint) return jsonResponse({ error: "Missing 'endpoint'" }, 400)

  try {
    const data = await strapiApi.get(
      `v1/${endpoint}`,
      ...(locale ? [{ _locale: locale }] : [])
    )
    return jsonResponse(data)
  } catch {
    return jsonResponse({ error: 'Failed to fetch data from Strapi' }, 500)
  }
}

export const action: ActionFunction = async ({ request }) => {
  withCors()
  try {
    const { endpoint, payload } = await request.json()

    if (!endpoint) return jsonResponse({ error: "Missing 'endpoint'" }, 400)
    const data = await strapiApi.post(`v1/${endpoint}`, payload)
    return jsonResponse(data)
  } catch {
    return jsonResponse({ error: 'Failed to post data to Strapi' }, 500)
  }
}
