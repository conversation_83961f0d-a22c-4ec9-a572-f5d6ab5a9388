import { useState, useMemo, useCallback, useEffect } from 'react'
import { Container } from '@/components'
import { LoaderFunction, MetaFunction } from '@remix-run/node'
import { useLoaderData } from '@remix-run/react'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { defineRoute, withCors } from '@/utils/routeHelper'
import { useTranslation } from 'react-i18next'
import { fetchPageBySlug, queryStringBuilder } from '@/utils/fetchHelper'
import { PageEntity, ReviewEntity, ServiceEntity } from '@/lib/api/entity'
import { strapiGetReviews } from '@/lib/api/strapi/review'
import {
  warperCollectionType,
  warperCollectionTypePagination,
} from '@/utils/strapiHelper'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { RightArrowIcon } from '@/lib/assets/right-arrow-icon'
import { Loader } from '@/components/loader'
import { SelectDropdown } from '@/components/select'
import { PromotionDialog } from '@/components/dialog'
import { Pagination } from '@/lib/api/types'
import { useInView } from 'react-intersection-observer'
import { Button } from '@/components/ui/button'
import { ChevronRightIcon } from 'lucide-react'
import { HelmetSEO } from '@/components/helmet/helmet-seo'
import { strapiGetServices } from '@/lib/api/strapi'

type LoaderData = {
  dataPage: PageEntity
  locale: string
  dataService: ServiceEntity[]
}

type SubServiceMenuProps = {
  data: string[]
  selectedType: string | null
  setSelectedType: React.Dispatch<React.SetStateAction<string | null>>
  placeHolder: string
}

export const meta: MetaFunction = ({ data }) => {
  const { dataPage } = data as LoaderData
  return buildTags(dataPage.seo)
}

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const { slug, lng } = defineRoute(request)

  const [responsePage, responseServices] = await Promise.all([
    fetchPageBySlug(
      slug,
      lng,
      queryStringBuilder([
        'backgroundImage',
        'images',
        'seo',
        'seo.metaSocial',
        'seo.metaImage',
      ])
    ),
    strapiGetServices(
      queryStringBuilder(['reviews'], undefined, ['slug']),
      lng
    ),
  ])

  return {
    dataPage: responsePage,
    locale: lng,
    dataService: warperCollectionType(responseServices),
  }
}

const ServiceMenu = ({
  data,
  selectedType,
  setSelectedType,
  placeHolder,
}: SubServiceMenuProps) => {
  const options = data.map((item) => ({
    text: item,
    value: item,
  }))
  if (!data?.length) return null

  return (
    <>
      {data.map((item: string) => (
        <button
          key={`review-service-${item}`}
          onClick={() =>
            setSelectedType((prevType) => (prevType === item ? '' : item))
          }
          className={cn(
            'w-full cursor-pointer border-b-[1px] border-primary last:border-none py-3 flex flex-row gap-6 justify-between items-center max-lg:hidden',
            selectedType === item ? 'text-green-700  font-bold' : 'bg-none'
          )}
        >
          {item}
          <RightArrowIcon className="w-4 h-4 text-primary" />
        </button>
      ))}
      <SelectDropdown
        className="lg:hidden max-w-40"
        items={options}
        onValueChange={setSelectedType}
        value={selectedType ?? ''}
        placeHolder={placeHolder}
      />
    </>
  )
}

const ReviewPage = () => {
  const { t } = useTranslation('review')
  const { t: home } = useTranslation('home')
  const { locale, dataPage, dataService } = useLoaderData<LoaderData>()
  const [selectedType, setSelectedType] = useState<string | null>('')
  const [dataReviews, setDataReviews] = useState<ReviewEntity[]>([])
  const [pagination, setPagination] = useState<Pagination>()
  const [isFetching, setIsFetching] = useState<boolean>(false)
  const hasMore = useMemo(
    () =>
      !!pagination?.page &&
      !!pagination?.pageCount &&
      pagination?.page < pagination?.pageCount,
    [pagination?.page, pagination?.pageCount]
  )

  const { ref, entry } = useInView({
    delay: 100,
    skip: !hasMore,
    trackVisibility: true,
    threshold: 1,
  })

  const fetchReviews = useCallback(
    async (currPage: number | undefined = undefined) => {
      setIsFetching(true)
      const response = await strapiGetReviews(
        queryStringBuilder(
          ['image', 'services'],
          selectedType?.length && selectedType !== t('all')
            ? { services: { name: { $eq: selectedType } } }
            : {},
          ['services.slug:asc'],
          {
            page: currPage ? currPage + 1 : 1,
            pageSize: 15,
          }
        ),
        locale
      )
      const { data, pagination: resPagination } =
        warperCollectionTypePagination(response)
      setDataReviews((prev) => [...prev, ...(data ?? [])])
      setPagination(resPagination)
      setTimeout(() => {
        setIsFetching(false)
      }, 1000)
    },
    [locale, selectedType, t]
  )

  const serviceList: string[] = useMemo(
    () => [
      t('all'),
      ...[
        ...new Set(
          dataService
            .filter(
              (service) =>
                !!service.reviews?.data?.length && !!service.name?.length
            )
            .map((service) => service.name)
            .filter((name): name is string => name !== undefined)
        ),
      ].sort((a, b) => Intl.Collator().compare(a, b)),
    ],
    [dataService, t]
  )
  const renderReviews = useMemo(
    () => (
      <div
        className={cn('lg:w-4/5 lg:ml-14 pl-0 mb-4', {
          'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4':
            dataReviews?.length && (pagination?.page || !isFetching),
          'flex justify-center items-center':
            !dataReviews?.length && isFetching,
        })}
      >
        {!!dataReviews.length &&
          !!pagination?.page &&
          dataReviews.map((review) => (
            <div
              className="p-1 animate-in zoom-in duration-500"
              key={`review-${review.id}-${review.services?.data?.[0]?.attributes?.name}-${review.createdAt}`}
            >
              <Card className="bg-opacity-80 bg-transparent border-none shadow-none hover:scale-105 duration-300">
                <PromotionDialog image={review.image} />
              </Card>
            </div>
          ))}
        {!!pagination?.page && !dataReviews.length && !isFetching && (
          <h2 className="animate-in zoom-in duration-500">
            No Review available
          </h2>
        )}
        {isFetching && (
          <div className="flex justify-center items-center w-full">
            <Loader />
          </div>
        )}
        {hasMore && !isFetching && (
          <div className="w-full col-span-full flex p-10 justify-center">
            <Button
              className="max-md:hidden"
              disabled={isFetching}
              onClick={() => fetchReviews(pagination?.page)}
            >
              {home('promotion.seeMore')}
              <div className="chevron-btn">
                <ChevronRightIcon className="w-4 h-4 text-white" />
              </div>
            </Button>

            <div className="md:hidden" ref={ref} />
          </div>
        )}
      </div>
    ),
    [
      dataReviews,
      fetchReviews,
      hasMore,
      home,
      isFetching,
      pagination?.page,
      ref,
    ]
  )

  useEffect(() => {
    setSelectedType(t('all'))
  }, [t, locale])

  useEffect(() => {
    if (selectedType === '') return
    setPagination((prev) => (prev ? { ...prev, page: 0 } : undefined))
    setDataReviews([])
    fetchReviews()
  }, [fetchReviews, selectedType])

  useEffect(() => {
    if (entry?.isIntersecting && !isFetching && hasMore) {
      fetchReviews(pagination?.page)
    }
  }, [
    entry?.isIntersecting,
    fetchReviews,
    hasMore,
    isFetching,
    pagination?.page,
  ])

  return (
    <div className="bg-white text-primary py-14">
      <HelmetSEO data={dataPage.seo} />
      <Container>
        <div className="text-center">
          <h3>Review</h3>
          <h1 className="inline-block border-b-[2px] border-primary last:border-none pb-4 mb-14">
            {t('title')}
          </h1>
          <div className="flex flex-col lg:flex-row">
            <div className="w-full lg:w-48 flex max-lg:justify-between lg:flex-col text-nowrap text-left mb-4">
              <h3 className="inline-block border-b-[2px] border-primary last:border-none mb-5">
                {t('more')}
              </h3>
              <ServiceMenu
                data={serviceList}
                selectedType={selectedType}
                setSelectedType={setSelectedType}
                placeHolder={t('select')}
              />
            </div>
            {renderReviews}
          </div>
        </div>
      </Container>
    </div>
  )
}

export default ReviewPage
