import { Container } from '@/components'
import { ServicesCard } from '../components/card-service/service-card'
import { fetchPageBySlug, queryStringBuilder } from '@/utils/fetchHelper'
import { LoaderFunction, MetaFunction } from '@remix-run/node'
import { ImageType, PageEntity, ServiceEntity } from '@/lib/api/entity'
import { useLoaderData } from '@remix-run/react'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { defineRoute, withCors } from '@/utils/routeHelper'
import { AppContext } from '@/providers/appContext'
import { useContext, useMemo } from 'react'
import { StrapiImage } from '@/components/strapi/strapi-image'
import { strapiGetServices } from '@/lib/api/strapi'
import { warperCollectionType } from '@/utils/strapiHelper'
import { StrapiField } from '@/lib/api/types'
import { HelmetSEO } from '@/components/helmet/helmet-seo'

type ServiceCard = {
  id: number
  header: TrustedHTML | string | null
  items: ServiceCardItem[]
}

export type ServiceCardItem = {
  image?: StrapiField<ImageType> | null
  description?: TrustedHTML | null
  name?: string
  link?: string
}

type LoaderData = {
  dataPage: PageEntity
  dataService: ServiceEntity[]
  locale: string
}

export const meta: MetaFunction = ({ data }) => {
  const { dataPage } = data as LoaderData
  return buildTags(dataPage.seo)
}

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const { slug, lng } = defineRoute(request)

  const [responsePage, responseService] = await Promise.all([
    fetchPageBySlug(
      slug,
      lng,
      queryStringBuilder([
        'backgroundImage',
        'seo',
        'seo.metaSocial',
        'seo.metaImage',
      ])
    ),
    strapiGetServices(queryStringBuilder(['header', 'thumbnail']), lng),
  ])

  return {
    dataPage: responsePage,
    dataService: warperCollectionType(responseService),
    locale: lng,
  }
}

const OurServicePage = () => {
  const { mainMenu } = useContext(AppContext)
  const { dataPage, dataService, locale } = useLoaderData<LoaderData>()

  const filteredServices = useMemo(() => {
    if (!mainMenu?.mainMenuItems || !dataPage.slug) return []

    const matchingMenuItem = mainMenu.mainMenuItems.find(
      (item) => item.url === dataPage.slug
    )
    if (!matchingMenuItem?.subMenuLink) return []

    const result: ServiceCard[] = []

    for (const menuItem of matchingMenuItem.subMenuLink) {
      if (menuItem.menu) {
        const filteredMenu = menuItem.menu.filter((_) => {
          return dataService.find((service) => service.slug === _.url)
        })
        if (filteredMenu.length > 0) {
          result.push({
            id: menuItem.id,
            header: menuItem.structuredData || menuItem.name,
            items: filteredMenu.map((_) => {
              const foundService = dataService.find(
                (service) => service.slug === _.url
              )
              return {
                image: foundService?.thumbnail,
                description: foundService?.header?.description,
                name: foundService?.name,
                link: _.url,
              } as ServiceCardItem
            }),
          })
        }
      } else {
        const foundService = dataService.find(
          (service) => service.slug === menuItem.url
        )
        if (foundService) {
          result.push({
            id: menuItem.id,
            header: menuItem.structuredData || menuItem.name,
            items: [
              {
                image: foundService?.thumbnail,
                description: foundService.header?.description,
                link: menuItem.url,
                name: foundService?.name,
              } as ServiceCardItem,
            ],
          })
        }
      }
    }

    return result
  }, [dataPage.slug, dataService, mainMenu?.mainMenuItems])

  return (
    <div className="text-brown-200 bg-brown-100">
      <HelmetSEO data={dataPage.seo} />
      <StrapiImage
        value={dataPage.backgroundImage}
        className="animate-in fade-in duration-500 w-full h-fit"
      />
      <Container>
        {filteredServices.map((service) => (
          <div
            key={`out-service-${service.id}`}
            className="flex flex-col mt-20 mb-10  gap-5"
          >
            {service.header && (
              <div
                className="ck ck-content"
                dangerouslySetInnerHTML={{ __html: service.header }}
              />
            )}
            <hr className="border-none h-0.5 bg-brown-200" />
            <div className=" flex flex-col gap-5 items-center">
              {service.items.map((item) => (
                <ServicesCard
                  key={`services-card-${item.name}-${item.link}`}
                  data={item}
                  locale={locale}
                />
              ))}
            </div>
          </div>
        ))}
      </Container>
    </div>
  )
}

export default OurServicePage
