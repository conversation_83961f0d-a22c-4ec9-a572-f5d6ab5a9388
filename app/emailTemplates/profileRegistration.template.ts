import { format } from 'date-fns'

export const profileRegistrationTemplate = (params): string => {
  return `
<table style="width: 100%; max-width: 600px; margin: auto; font-family: Arial, sans-serif; border-collapse: collapse;">
  <tr>
    <td style="padding: 10px; text-align: center;">
      <h2 style="margin: 0; color: #333;">🎉 New Customer Registration</h2>
    </td>
  </tr>
  <tr>
    <td style="padding: 20px;">
      <h3 style="margin-bottom: 10px;">👤 Personal Information</h3>
      <p><strong>Full Name:</strong> ${params?.firstName} ${params?.lastName}</p>
      <p><strong>Nickname:</strong> ${params?.nickName} </p>
      <p><strong>Gender:</strong> ${params?.gender} </p>
      <p><strong>Age:</strong> ${params?.age} </p>
      <p><strong>Birth Date:</strong> ${format(
        new Date(params?.birthDate),
        'dd/MM/yyyy'
      )}</p>
      <p><strong>Mobile No:</strong> ${params?.mobileNo}</p>
      <p><strong>Email:</strong> ${params?.email}</p>
      <p><strong>Address:</strong> ${params?.address}</p>

      ${
        params?.message
          ? ` <h3 style="margin-top: 30px; margin-bottom: 10px;">💬 Message</h3>
      <p style="white-space: pre-line;">${params?.message}</p>`
          : ''
      }
     

      <h3 style="margin-top: 30px; margin-bottom: 10px;">💎 Services</h3>
      <p><strong>Used Services:</strong> ${params?.usedServices}</p>
      <p><strong>Wanted Services:</strong> ${params?.wantedServices}</p>

      <h3 style="margin-top: 30px; margin-bottom: 10px;">📣 Discovery</h3>
      <p><strong>Sources:</strong> ${params?.sources}</p>
      <p><strong>Reasons:</strong> ${params?.reasons}</p>
    </td>
  </tr>
</table>
`
}
