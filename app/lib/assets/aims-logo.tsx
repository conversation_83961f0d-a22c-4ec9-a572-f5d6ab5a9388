import { SVGProps } from 'react'
import { cn } from '../utils'

type Props = SVGProps<SVGSVGElement>

export const AimsLogo = ({ className, ...props }: Props) => {
  return (
    <svg
      role="img"
      aria-label="Aims Logo"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      version="1.1"
      viewBox="0 0 1024 450"
      fill="currentColor"
      xmlSpace="preserve"
      className={cn('aims-logo', className)}
      {...props}
    >
      <g
        transform="matrix(1 0 0 1 512 206)"
        id="0ede7ef5-c29c-4e99-9f3d-0b6950d7b05f"
      >
        <rect
          className="hiddenRect"
          vectorEffect="non-scaling-stroke"
          x="-512"
          y="-206"
          rx="0"
          ry="0"
          width="1024"
          height="450"
        />
      </g>
      <g
        transform="matrix(1 0 0 1 0 0)"
        id="ea55512e-99ca-404c-9746-39b33243bccb"
      ></g>
      <g transform="matrix(0.87 0 0 0.87 512 206)">
        <g className="visibleGroup" vectorEffect="non-scaling-stroke">
          <g transform="matrix(1 0 0 1 -316.04 -121.04)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-643.96, -418.96)"
              d="M 642.75499 435.68497 C 647.34531 436.01029 651.3606100000001 434.60791 654.80096 431.48586 C 658.39957 428.36732 660.36167 424.55374 660.68309 420.0403 C 661.00452 415.53116 659.60193 411.47507 656.48345 407.87257 C 653.52765 404.43617 649.7505 402.55908 645.16018 402.23376 C 640.56578 401.91649 636.5545099999999 403.31887 633.11414 406.4334 C 629.51118 409.39309000000003 627.54909 413.17071 627.23188 417.75713 C 626.9065 422.35107 628.30902 426.36691 631.4312500000001 429.80304 C 634.3873900000001 433.40581000000003 638.1605800000001 435.36824 642.75498 435.68497 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 -318.97 -22.3)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-641.03, -517.7)"
              d="M 656.54929 529.23601 C 656.4883500000001 502.64982 656.60999 476.51473999999996 656.54929 449.92855 C 645.9917300000001 449.92830999999995 636.0457200000001 449.91931999999997 625.48816 449.92855 C 625.48816 449.92855 629.28792 450.79091 630.46031 451.56780999999995 C 631.9017 452.52290999999997 633.95684 453.93297999999993 634.43803 456.53995999999995 C 634.93476 459.23045999999994 634.75734 462.05936999999994 634.75316 464.79377999999997 C 634.7429599999999 471.45640999999995 634.77088 475.67909999999995 634.75316 481.12685999999997 C 634.6400199999999 516.18683 634.85932 585.46665 634.75316 585.46665 C 634.85932 585.46665 651.9927299999999 560.21741 655.18947 546.83229 C 656.56871 541.09805 656.56264 535.13366 656.54929 529.2360199999999 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 -272.71 113.02)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-687.29, -653.02)"
              d="M 651.88634 714.94408 C 655.30431 717.49842 658.8032000000001 719.8986 662.6282 721.86855 C 671.63587 725.9629600000001 681.10878 727.99559 691.03711 727.97956 C 699.53047 727.96585 707.54355 725.10128 713.33764 718.75381 C 719.68007 711.8056300000001 722.16891 702.44734 723.1111599999999 693.2917500000001 C 723.5690699999999 688.8423400000001 723.6918 684.20977 722.0600199999999 680.0450500000001 C 716.57522 666.0466700000001 696.1895199999999 662.10189 683.1816999999999 660.27273 C 650.6071899999998 655.69212 634.2997899999999 641.94978 634.2627799999999 619.0457200000001 C 634.2527299999999 612.8265600000001 635.3019499999999 607.1627300000001 637.4137299999999 602.06078 C 639.5222399999999 596.96211 642.4936899999999 592.58947 646.3280799999999 588.93958 C 650.1624799999998 585.29297 654.7582999999998 582.4053299999999 660.1155399999999 580.27992 C 665.4760599999998 578.15451 671.4607499999998 577.01765 678.0827299999999 576.8758899999999 L 681.2611199999999 576.8707499999999 C 686.6873199999999 576.8619799999999 692.0855199999999 577.7772899999999 697.4491799999998 579.6232299999999 C 700.6852799999998 580.73696 703.8233199999999 582.1274099999999 706.8351099999999 583.7517799999999 C 708.4059399999999 584.59899 709.9425699999998 585.5092699999999 711.4433799999998 586.4749999999999 C 712.9387299999999 587.4372099999999 714.4362699999998 588.1714699999999 716.1203999999998 588.7760199999999 C 718.4074299999997 589.5969899999999 720.7308999999998 590.3029999999999 723.1852499999998 590.3029999999999 C 725.8590299999997 590.2986799999999 727.9355999999998 589.7710499999998 729.4051199999998 588.7070299999999 C 729.9385699999998 588.3064099999999 730.4392699999997 587.9123999999999 730.9071899999998 587.5118799999999 C 731.3751199999998 587.1146399999999 731.8756999999998 586.6518199999999 732.4122199999998 586.1201299999999 L 734.2111199999998 586.1172199999999 L 734.2727299999998 624.2447899999999 L 733.4764899999998 624.2460799999999 C 732.8074699999997 621.8649999999999 732.1450099999997 619.4839099999999 731.4759899999998 617.0995599999999 C 730.8069699999998 614.7184799999999 730.0101599999998 612.3376099999999 729.0790099999998 609.9536699999999 C 726.7528499999999 603.9468599999999 723.3271699999998 598.6331399999999 718.6167799999998 594.2302099999998 C 713.9332199999999 589.8523599999999 708.5013299999998 586.2845699999998 702.7250199999999 583.5211999999998 C 701.2773099999998 582.8286199999998 699.8038699999998 582.1879599999997 698.3075999999999 581.6075299999998 C 692.5369899999998 579.3690399999998 686.8664999999999 578.2510199999998 681.2993999999999 578.2600099999997 L 678.7140899999998 578.2641899999998 C 662.8053899999999 578.2898899999998 656.0034199999998 595.7616099999998 654.3242099999999 608.8876899999998 C 653.1474799999999 618.0859999999998 654.5099499999999 626.6113399999998 662.3382399999999 632.3157099999997 C 669.8321299999999 637.7764099999997 679.5951499999999 639.8745699999997 688.5901799999999 641.2001599999998 C 706.3770599999999 643.8189899999998 719.7869599999999 648.8958599999997 728.8231499999999 656.4242199999998 C 737.7245899999999 663.6972099999998 742.18688 673.4840599999998 742.20678 685.7945799999998 C 742.22925 699.6975799999998 737.83282 710.3965499999998 729.02078 717.8882099999997 C 720.2087499999999 725.3864299999997 707.5765899999999 729.1455599999997 691.12433 729.1721399999998 C 686.0815 729.1802899999998 681.13623 728.7229899999998 676.29178 727.8035099999997 C 671.44734 726.8873099999997 666.90032 725.5053399999997 662.65401 723.6575799999997 C 661.0571699999999 722.9274299999997 659.49046 722.1290799999997 657.9766599999999 721.2387699999997 C 656.4619899999999 720.3479499999997 655.0582699999999 719.2785199999997 653.51919 718.4284499999997 C 651.93227 717.5519499999997 650.17057 717.0102499999997 648.43214 716.5209299999997 C 647.11827 716.1511099999997 645.76643 715.9049099999996 644.40334 715.8207199999997 C 641.7600299999999 715.6574599999997 639.02793 716.1247299999997 636.7757799999999 717.5785499999997 C 635.79859 718.2093499999997 634.9379799999999 719.0098199999998 634.2259099999999 719.9287599999997 L 632.4401099999999 719.9316499999996 L 632.3781699999998 681.6042099999996 L 632.9745299999998 681.6032499999997 C 634.7739199999999 688.4018099999996 636.6993399999999 695.4029899999997 640.0365899999998 701.6322999999996 C 640.3645599999999 702.2444899999997 640.7078899999998 702.8484799999997 641.0683899999998 703.4421099999996 C 642.4845799999998 705.7740899999997 644.2092299999998 707.9034599999997 646.0014799999998 709.9540499999996 C 647.7031099999998 711.9009599999996 649.8231799999998 713.4022399999996 651.8863199999998 714.9440799999996 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 -245.67 -24.28)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-714.33, -515.72)"
              d="M 749.65227 467.48976 C 747.02342 466.21172 744.2674800000001 465.24278 741.44491 464.56164 C 737.68169 463.41803 734.0678 463.32194 730.39463 463.32763 C 730.01489 463.32834 729.63373 463.33047 729.25233 463.33189 L 728.06754 463.33639 C 717.53617 463.35343 705.456 469.54836 696.54241 479.50401999999997 C 691.34151 485.31293 687.01867 493.16551999999996 683.2807700000001 499.99353999999994 L 670.1635000000001 522.0079499999999 C 668.2447900000001 525.0491999999999 667.2292200000002 528.57327 667.2350200000001 532.1688099999999 C 667.2350200000001 532.1688099999999 667.28673 532.1688099999999 667.2855500000001 532.1688099999999 L 667.2855500000001 568.1067099999999 C 670.37733 567.5050899999999 673.6338400000001 567.1619099999999 677.06276 567.0890099999999 L 680.26625 567.0837999999999 C 682.95048 567.0790699999999 685.62785 567.3020099999999 688.29776 567.7474299999999 L 688.29776 542.6998199999999 C 688.29776 535.36295 688.14215 528.00619 688.4026100000001 520.6726299999999 C 688.7272100000001 511.5365699999999 690.16938 502.4371799999999 691.9789800000001 493.4163799999999 C 692.32878 491.6725699999999 692.7015400000001 489.9282899999999 693.2509800000001 488.2344199999999 C 695.5576000000001 481.1238099999999 701.9793800000001 475.6426899999999 708.0449600000002 471.66278999999986 C 713.9470000000002 467.79033999999984 720.9221200000002 464.94362999999987 728.0700100000001 464.93226999999985 L 729.2598900000002 464.92847999999987 C 731.9386800000002 464.91380999999984 734.5102500000002 464.9033899999999 737.0807600000002 465.24277999999987 C 737.4480800000001 465.3161499999999 737.8151600000002 465.39495999999986 738.1820000000001 465.4796899999999 C 745.9986100000001 467.2859799999999 747.0132300000001 471.7763799999999 743.0785400000001 478.1050299999999 C 742.7543000000001 478.6266599999999 742.4294600000001 479.15064999999987 742.1455700000001 479.6942899999999 C 739.9718400000002 483.86020999999994 740.3379800000001 488.5844499999999 743.2614800000001 491.7265299999999 C 746.3417800000001 495.0373499999999 750.2106800000001 495.7428799999999 754.4271400000001 494.4814099999999 C 758.4869200000002 493.2672799999999 761.0963600000001 489.5997899999999 761.3899500000001 485.00902999999994 C 761.8655500000001 477.57298999999995 757.7466000000001 471.42468999999994 749.6522500000001 467.48975999999993 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 -376.9 80.35)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-583.1, -620.35)"
              d="M 621.31257 604.57577 L 620.40287 603.01833 C 619.53401 601.65981 618.66514 600.3013 617.79628 598.94278 C 615.53309 595.40415 613.26989 591.86552 611.0067 588.3268899999999 C 607.86192 583.4098499999999 604.71714 578.4928199999999 601.57236 573.5757799999999 C 598.0587400000001 568.0820399999999 594.54512 562.5883099999999 591.03151 557.0945699999999 C 587.6618 551.8258399999999 584.29209 546.5571099999999 580.92238 541.2883799999998 C 578.2093199999999 537.0463599999998 575.49626 532.8043399999998 572.7832 528.5623299999999 C 570.67473 525.2656299999999 568.78661 521.7920299999998 566.26749 518.7788899999998 C 564.0424999999999 516.1175699999998 560.40316 514.6653199999998 556.9776099999999 514.6653199999998 L 535.3883299999999 514.6825599999999 C 535.3684199999999 514.6767299999999 535.3519099999999 514.6709099999998 535.3317599999999 514.6653199999998 L 529.7407599999999 514.6742999999998 C 534.4431799999999 515.4356599999998 538.1482599999999 517.4599699999998 540.86498 520.7525599999998 C 543.40689 523.8353899999997 544.7638 529.1675799999998 544.9393299999999 536.7474399999998 L 544.9393299999999 699.2127399999997 C 544.9393299999999 701.5774199999997 544.9136 703.9438099999998 544.9393299999999 706.3084899999997 C 544.98983 710.9608999999997 544.2408499999999 716.4620699999997 541.1900699999999 720.1863199999997 C 538.7489099999999 723.1649999999997 535.4915199999999 725.1038399999996 531.4273699999999 726.0264099999997 L 560.6139899999998 726.0264099999997 C 556.4396199999999 725.1392899999997 553.0967699999999 723.1980099999997 550.6000099999999 720.1710199999998 C 546.7388199999999 715.4902099999998 546.9281899999999 709.3711599999998 546.9281899999999 703.6609599999998 L 546.9281899999999 531.6381099999999 C 546.9281899999999 531.1867799999999 547.1865099999999 529.7575299999999 546.9281899999999 529.3593699999999 C 546.9364399999998 529.3717499999999 606.1263999999999 620.6090099999999 606.1263999999999 620.6090099999999 C 608.0919499999999 623.6384299999999 612.5751399999999 623.4854799999999 614.3299599999999 620.32981 L 621.5104399999999 607.69331 L 622.5259899999999 605.96034 L 636.1005899999999 583.29238 C 636.2450399999999 582.12558 636.3632799999999 580.8284 636.4567499999999 579.41226 L 621.3125699999999 604.57576 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 -447.68 -73.2)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-512.32, -466.8)"
              d="M 624.39118 501.47413 C 622.40138 498.75911 621.77397 497.67716 619.86249 493.35792 L 562.59617 352.57422999999994 L 562.16555 351.59376999999995 C 561.7082600000001 350.55215999999996 560.22166 350.57901999999996 559.80226 351.63653999999997 L 517.42979 457.15781 L 491.03432000000004 522.17687 L 491.00518000000005 522.16592 C 490.98652000000004 522.21551 490.66460000000006 523.06663 490.0991000000001 524.4975400000001 L 488.66005000000007 528.0683600000001 C 484.78344000000004 537.5170100000001 476.75705000000005 555.7826800000001 469.61602000000005 563.7591500000001 C 460.11329000000006 574.3722000000001 447.29978000000006 580.9771000000001 436.17489000000006 580.9950400000001 L 434.86331000000007 580.9990000000001 C 432.3268100000001 581.0132000000001 429.87766000000005 581.0247600000001 427.4414700000001 580.7906200000001 C 426.7869700000001 580.6722900000001 426.1327300000001 580.5426100000001 425.4794100000001 580.3917000000001 C 416.8631400000001 578.4006100000001 415.7446900000001 573.4508500000002 420.0819000000001 566.4745500000001 C 420.43935000000005 565.8997200000001 420.79741000000007 565.3222500000002 421.11030000000005 564.7228800000001 C 423.50639000000007 560.1307600000001 423.10279 554.9233000000002 419.88029000000006 551.4597000000001 C 416.48494000000005 547.8102900000001 412.22021000000007 547.0327000000001 407.57234000000005 548.4230900000001 C 403.09737000000007 549.7614200000002 400.22098000000005 553.8043600000001 399.89732000000004 558.8644200000001 C 399.37299 567.0610400000002 403.91338 573.8382100000001 412.83577 578.1757200000001 C 415.07398 579.2638400000001 417.39649000000003 580.14678 419.77068 580.84148 C 424.52382 582.6386100000001 429.02592000000004 582.77115 433.61254 582.7636600000001 C 434.031 582.763 434.45128 582.7605800000001 434.87158000000005 582.7589300000001 L 436.17771000000005 582.7540900000001 C 442.40391000000005 582.7440700000001 449.12078 580.7729100000001 455.52729000000005 577.2560300000001 C 458.39895000000007 575.7540200000001 461.11262000000005 573.9509400000002 463.6034900000001 571.8529600000002 C 466.1884800000001 569.7894400000001 468.6524600000001 567.4738400000001 470.9276900000001 564.9326600000002 C 476.7271600000001 558.4549800000002 480.6939200000001 550.1366900000002 484.3561300000001 542.3313100000001 C 491.1572700000001 527.8358600000001 497.8283000000001 510.83502000000016 503.82926000000015 495.9950900000001 C 508.76610000000016 483.7866500000001 513.7029500000001 471.5782100000001 518.6397900000002 459.36977000000013 C 519.2080300000001 457.9645400000001 519.7814400000002 456.5611900000001 520.3445800000002 455.1539500000001 C 521.2261700000001 452.9509600000001 523.5684100000002 451.70807000000013 525.9410300000002 451.70807000000013 L 577.5808800000002 451.70807000000013 C 579.9555100000002 451.7087300000001 582.0901700000002 453.15648000000016 582.9692800000003 455.36211000000014 L 598.0486100000003 493.39316000000014 C 598.0488300000003 493.53329000000014 598.0870300000003 493.6378100000001 598.1680700000003 493.7066600000001 C 598.2466900000003 493.7788700000001 598.2873100000003 493.8833400000001 598.2875300000003 494.0201600000001 L 599.7222600000002 497.78174000000007 C 602.2686700000003 504.1913600000001 602.4367000000002 508.7920700000001 600.2203900000002 511.58287000000007 C 598.6352700000002 513.3986600000001 595.4642200000002 514.3087300000001 590.7037400000002 514.3164400000001 L 588.8005200000001 514.31952 L 589.0387300000001 514.52823 L 589.0390600000001 514.73738 L 624.7593 514.6766200000001 L 624.7464200000001 506.70808000000005 C 624.7448 505.70268000000004 624.9853400000001 502.28520000000003 624.3911700000001 501.47415000000007 Z M 522.8148299999999 449.89672 L 551.4123399999999 378.79165 C 551.5517299999999 378.44325 552.0449599999998 378.44413000000003 552.1828299999999 378.79319 L 553.8245899999998 382.94324 L 580.5498399999998 449.89672 L 522.8148399999998 449.89672 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 42.28 -22.88)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1002.28, -517.12)"
              d="M 1004.30839 450.66528 L 1066.29307 583.58148 L 1051.9464 583.58148 L 1031.04091 539.8486 L 973.7816899999999 539.8486 L 953.0756899999999 583.58148 L 938.2570499999999 583.58148 L 1001.0552499999999 450.66528000000005 L 1004.3083799999999 450.66528000000005 Z M 1002.6307400000001 478.90516 L 979.8549200000001 527.01804 L 1025.2966000000001 527.01804 L 1002.6307400000002 478.90516 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 42.27 -22.88)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1002.27, -517.12)"
              d="M 1067.85687 584.57795 L 1051.31873 584.57795 L 1051.04723 584.0115999999999 L 1030.4132399999999 540.84507 L 974.4122599999998 540.84507 L 953.7062599999998 584.57795 L 936.6844899999999 584.57795 L 1000.4236999999998 449.66881 L 1004.9428599999999 449.66881 L 1005.2114399999999 450.24392 L 1067.8568599999999 584.57795 Z M 1052.5740600000001 582.585 L 1064.72927 582.585 L 1003.6739200000001 451.66175000000004 L 1001.68681 451.66175000000004 L 939.82961 582.585 L 952.44511 582.585 L 973.15111 538.85212 L 1031.66857 538.85212 L 1031.94007 539.4184700000001 L 1052.5740600000001 582.585 Z M 1026.8672100000001 528.0145100000001 L 978.2804200000002 528.0145100000001 L 1002.6326900000001 476.5716200000001 L 1026.8672100000001 528.0145100000001 Z M 981.4294300000001 526.0215600000001 L 1023.7259900000001 526.0215600000001 L 1002.6288000000002 481.23868000000016 L 981.4294300000001 526.0215600000001 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 183.45 -22.88)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1143.45, -517.12)"
              d="M 1136.81199 450.66528 L 1150.0940699999999 450.66528 L 1150.0940699999999 583.58148 L 1136.81199 583.58148 L 1136.81199 450.66528000000005 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 183.45 -22.88)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1143.45, -517.12)"
              d="M 1151.09055 584.57795 L 1135.8155199999999 584.57795 L 1135.8155199999999 449.66881 L 1151.09055 449.66881 L 1151.09055 584.57795 Z M 1137.80847 582.585 L 1149.09761 582.585 L 1149.09761 451.66175000000004 L 1137.80847 451.66175000000004 L 1137.80847 582.585 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 341.3 -22.88)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1301.3, -517.12)"
              d="M 1226.3067 583.58147 L 1245.3360200000002 450.66526999999996 L 1247.49537 450.66526999999996 L 1301.5287 559.7274299999999 L 1355.0433600000001 450.6652699999999 L 1357.17254 450.6652699999999 L 1376.30014 583.5814699999999 L 1363.2739900000001 583.5814699999999 L 1350.1417700000002 488.52538999999985 L 1303.14505 583.5814699999999 L 1299.7498400000002 583.5814699999999 L 1252.19358 487.80235999999985 L 1239.1372600000002 583.5814699999999 L 1226.3067 583.5814699999999 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 341.3 -22.88)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1301.3, -517.12)"
              d="M 1377.45037 584.57795 L 1362.40597 584.57795 L 1349.59877 491.87292 L 1303.76395 584.57795 L 1299.13191 584.57795 L 1298.85749 584.0242499999999 L 1252.74339 491.14989999999995 L 1240.0072300000002 584.57796 L 1225.1574500000002 584.57796 L 1244.4718900000003 449.66882 L 1248.1133000000002 449.66882 L 1248.3886900000002 450.22252 L 1301.5248100000001 557.47371 L 1354.42251 449.66881 L 1358.0357000000001 449.66881 L 1358.15929 450.52321 L 1377.45037 584.5779600000001 Z M 1364.14201 582.585 L 1375.14992 582.585 L 1356.3093900000001 451.66175000000004 L 1355.6642100000001 451.66175000000004 L 1301.5325900000003 561.98118 L 1246.8774400000002 451.66175 L 1246.2001500000001 451.66175 L 1227.4559600000002 582.585 L 1238.2673000000002 582.585 L 1251.64378 484.45484000000005 L 1300.36778 582.585 L 1302.5261600000001 582.585 L 1350.68478 485.17787000000004 L 1364.14202 582.585 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 519.39 -22.88)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1479.39, -517.12)"
              d="M 1440.49282 558.82341 L 1451.7877999999998 552.04662 C 1459.73914 566.68426 1468.9243999999999 574.00405 1479.34649 574.00405 C 1483.8033699999999 574.00405 1487.9897199999998 572.96476 1491.90652 570.88618 C 1495.82137 568.8076 1498.80301 566.0225399999999 1500.85142 562.52807 C 1502.89886 559.03555 1503.92355 555.33089 1503.92355 551.41409 C 1503.92355 546.95721 1502.41716 542.58986 1499.40634 538.3120299999999 C 1495.24918 532.4091 1487.65984 525.3004699999999 1476.63636 516.9880999999999 C 1465.55158 508.6153999999999 1458.65509 502.5616299999999 1455.94399 498.82582999999994 C 1451.24578 492.56186999999994 1448.8957 485.78507999999994 1448.8957 478.4954599999999 C 1448.8957 472.71318999999994 1450.28142 467.44278999999995 1453.05286 462.68326999999994 C 1455.82235 457.9247199999999 1459.72358 454.17529999999994 1464.75363 451.43402999999995 C 1469.7827 448.69275999999996 1475.24968 447.32260999999994 1481.15358 447.32260999999994 C 1487.41851 447.32260999999994 1493.27571 448.87375999999995 1498.7280899999998 451.97605999999996 C 1504.1794999999997 455.07932999999997 1509.94716 460.78569 1516.0320699999997 469.09806999999995 L 1505.1886199999997 477.32090999999997 C 1500.1877699999998 470.69494999999995 1495.9274599999997 466.32856999999996 1492.4028299999998 464.21885 C 1488.8791699999997 462.11107999999996 1485.0392499999998 461.05622 1480.8830599999997 461.05622 C 1475.5202099999997 461.05622 1471.1382599999997 462.68327 1467.7352699999997 465.93641 C 1464.3312999999996 469.18857 1462.6302899999996 473.19587 1462.6302899999996 477.95344 C 1462.6302899999996 480.84457 1463.2316799999996 483.64618 1464.4373699999996 486.35631 C 1465.6420899999996 489.06742 1467.8403599999997 492.01985 1471.0341299999995 495.21168 C 1472.7798999999995 496.89907 1478.5028099999995 501.32675 1488.2018699999996 508.49474 C 1499.7070399999996 516.98811 1507.5970799999996 524.54826 1511.8748999999996 531.17422 C 1516.1517499999995 537.80115 1518.2906599999997 544.4562999999999 1518.2906599999997 551.14357 C 1518.2906599999997 560.7813299999999 1514.6307599999996 569.15501 1507.3119499999996 576.26266 C 1499.9931299999996 583.37128 1491.0929999999996 586.92511 1480.6115499999996 586.92511 C 1472.5385699999997 586.92511 1465.2197499999995 584.77063 1458.6550999999997 580.4645800000001 C 1452.0884999999996 576.1575600000001 1446.0347399999998 568.94384 1440.4928299999997 558.82342 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 519.22 -22.88)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1479.22, -517.12)"
              d="M 1480.61155 587.92158 C 1472.37995 587.92158 1464.80909 585.69314 1458.1082000000001 581.29757 C 1451.4355300000002 576.92049 1445.21439 569.5199299999999 1439.61896 559.30219 L 1439.15868 558.46142 L 1452.16732 550.65702 L 1452.66361 551.57078 C 1460.3901600000002 565.79484 1469.36815 573.00759 1479.3465 573.00759 C 1483.61751 573.00759 1487.68612 571.9974900000001 1491.4394300000001 570.00552 C 1495.17134 568.02425 1498.0488500000001 565.33942 1499.9921600000002 562.0240100000001 C 1501.9393700000003 558.7017900000001 1502.9270800000002 555.1323900000001 1502.9270800000002 551.41411 C 1502.9270800000002 547.18786 1501.4683800000003 542.97329 1498.59184 538.88522 C 1494.5115600000001 533.09322 1486.92319 525.99336 1476.03595 517.78414 C 1464.76919 509.27324999999996 1457.93304 503.26327999999995 1455.13727 499.41071 C 1450.33785 493.01149 1447.89922 485.97098 1447.89922 478.49549 C 1447.89922 472.56239 1449.34333 467.07303 1452.19164 462.18215000000004 C 1455.03996 457.28737 1459.10661 453.37641 1464.2767900000001 450.55923 C 1469.42945 447.75081 1475.10759 446.32617 1481.1535700000002 446.32617 C 1487.5635000000002 446.32617 1493.6415900000002 447.93571 1499.2204700000002 451.11001999999996 C 1504.7701600000003 454.26876 1510.6974200000002 460.12303999999995 1516.8358500000002 468.50935999999996 L 1517.41486 469.30049999999994 L 1504.99496 478.7183299999999 L 1504.39357 477.9213499999999 C 1499.49295 471.4287099999999 1495.28713 467.1070899999999 1491.89095 465.0742499999999 C 1488.53953 463.0696299999999 1484.83585 462.0527199999999 1480.88304 462.0527199999999 C 1475.81115 462.0527199999999 1471.6199299999998 463.6019199999999 1468.42421 466.6565399999999 C 1465.1954099999998 469.7403499999999 1463.62674 473.4362499999999 1463.62674 477.9534599999999 C 1463.62674 480.6937599999999 1464.2057499999999 483.3844299999999 1465.34819 485.95150999999987 C 1466.49842 488.53999999999985 1468.6480299999998 491.41848999999985 1471.73865 494.5071599999999 C 1473.4318700000001 496.1429699999999 1479.17424 500.58427999999986 1488.79448 507.6928999999999 C 1500.34052 516.2164399999999 1508.38723 523.9352099999999 1512.7117700000001 530.6341499999999 C 1517.0752300000001 537.3943999999999 1519.2871300000002 544.2947799999998 1519.2871300000002 551.1435799999998 C 1519.2871300000002 561.0158599999999 1515.4919700000003 569.7077499999998 1508.0057800000002 576.9779099999998 C 1500.52932 584.2393099999998 1491.31292 587.9215899999998 1480.61154 587.9215899999998 Z M 1441.83086 559.1824999999999 C 1447.14408 568.67721 1452.98571 575.5542299999998 1459.20198 579.6315899999998 C 1465.57201 583.8101599999999 1472.77503 585.9286299999999 1480.61154 585.9286299999999 C 1490.77575 585.9286299999999 1499.52603 582.4361099999999 1506.6181100000001 575.5474099999999 C 1513.70241 568.6674699999999 1517.29418 560.4572699999999 1517.29418 551.1435599999999 C 1517.29418 544.6839999999999 1515.1893300000002 538.1475699999999 1511.03801 531.7142899999999 C 1506.84387 525.2196999999999 1498.96162 517.6770699999998 1487.6102 509.2965799999999 C 1477.78268 502.0341999999999 1472.13471 497.6610099999999 1470.3412500000002 495.9278899999999 C 1467.0657400000002 492.65431999999987 1464.7769600000001 489.57439999999986 1463.5265100000001 486.7611199999999 C 1462.2711900000002 483.9381099999999 1461.6338 480.9739899999999 1461.6338 477.9534299999999 C 1461.6338 472.9331199999999 1463.45548 468.6475099999999 1467.04725 465.2162899999999 C 1470.6254000000001 461.7948099999999 1475.2808 460.0597399999999 1480.8830400000002 460.0597399999999 C 1485.2017400000002 460.0597399999999 1489.2489300000002 461.17103999999995 1492.9146700000001 463.3634699999999 C 1496.4373600000001 465.4722199999999 1500.5205600000002 469.5855799999999 1505.3793300000002 475.9254499999999 L 1514.6463300000003 468.8985699999999 C 1508.8845000000003 461.1622799999999 1503.3659500000003 455.7634299999999 1498.2347000000002 452.8421299999999 C 1492.9604000000002 449.8410399999999 1487.2131700000002 448.3190799999999 1481.1535700000002 448.3190799999999 C 1475.4452600000002 448.3190799999999 1470.0872800000002 449.66100999999986 1465.2304500000002 452.30885999999987 C 1460.3843200000003 454.94989999999984 1456.5774900000004 458.60881999999987 1453.9140600000003 463.1844199999999 C 1451.2457700000002 467.76779999999985 1449.8921600000003 472.9194899999999 1449.8921600000003 478.4954499999999 C 1449.8921600000003 485.5301199999999 1452.1965000000002 492.16872999999987 1456.7409600000003 498.2283299999999 C 1459.3917300000003 501.8804399999999 1466.2843300000004 507.9196099999999 1477.2367600000002 516.19306 C 1488.2884600000002 524.52684 1496.0218200000002 531.77559 1500.2208300000002 537.73789 C 1503.3387000000002 542.16849 1504.9200200000002 546.7694 1504.9200200000002 551.41409 C 1504.9200200000002 555.49145 1503.8408300000003 559.39949 1501.7106800000001 563.03214 C 1499.57858 566.6706300000001 1496.43638 569.6094400000001 1492.37362 571.7658700000001 C 1488.32934 573.9125700000001 1483.94642 575.0005100000001 1479.3464900000001 575.0005100000001 C 1468.80276 575.0005100000001 1459.40828 567.74494 1451.4141200000001 553.4333 L 1441.83086 559.18248 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 22.78 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-982.78, -679.45)"
              d="M 1027.39366 650.75579 L 1020.19648 656.2772600000001 C 1016.2261599999999 651.1070900000001 1011.4578799999999 647.18834 1005.89554 644.5200500000001 C 1000.33223 641.8527300000001 994.22105 640.5185900000001 987.562 640.5185900000001 C 980.28113 640.5185900000001 973.53937 642.2663100000001 967.33478 645.7607700000001 C 961.13115 649.2571800000001 956.32201 653.9505300000001 952.91026 659.8447000000001 C 949.49754 665.7388700000001 947.79166 672.36872 947.79166 679.7303600000001 C 947.79166 690.8569900000001 951.60725 700.1424800000001 959.23844 707.5878000000001 C 966.8696299999999 715.0331200000002 976.49668 718.7562700000001 988.1196 718.7562700000001 C 1000.9014999999999 718.7562700000001 1011.59314 713.7515300000001 1020.19648 703.7410600000001 L 1027.39366 709.20122 C 1022.84336 714.99224 1017.1661899999999 719.46956 1010.36313 722.63414 C 1003.5581199999999 725.79775 995.9580699999999 727.38003 987.56201 727.38003 C 971.5951 727.38003 959.00003 722.06583 949.77682 711.43452 C 942.04151 702.45946 938.17434 691.62184 938.17434 678.92363 C 938.17434 665.5637 942.85893 654.32321 952.22713 645.20218 C 961.59631 636.08212 973.33211 631.52209 987.43745 631.52209 C 995.95807 631.52209 1003.6515400000001 633.20753 1010.51786 636.5784100000001 C 1017.3832100000001 639.9502600000001 1023.0088000000001 644.6757200000001 1027.39367 650.7557600000001 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 22.99 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-982.99, -679.45)"
              d="M 987.56201 728.37652 C 971.36739 728.37652 958.40157 722.89592 949.0246 712.0875 C 941.16278 702.96647 937.17787 691.8086999999999 937.17787 678.92365 C 937.17787 665.34769 942.00745 653.76175 951.53233 644.48793 C 961.05039 635.22288 973.13068 630.52564 987.43745 630.52564 C 996.07192 630.52564 1003.98532 632.2607099999999 1010.95673 635.68414 C 1017.9398199999999 639.1134099999999 1023.74155 643.98775 1028.20232 650.1728899999999 L 1028.7677 650.9572199999999 L 1020.0125600000001 657.6736799999999 L 1019.4063100000001 656.8844799999998 C 1015.5517900000001 651.8651399999999 1010.8613600000001 648.0076999999998 1005.46445 645.4182299999999 C 1000.06267 642.8287599999999 994.03908 641.5150499999999 987.562 641.5150499999999 C 980.48938 641.5150499999999 973.84883 643.2355199999998 967.82425 646.6287799999999 C 961.80357 650.02204 957.07617 654.6365699999999 953.77243 660.3438999999998 C 950.4648 666.0560999999999 948.7881199999999 672.5789099999998 948.7881199999999 679.7303499999998 C 948.7881199999999 690.5261199999998 952.53852 699.6588299999999 959.9342099999999 706.8744899999998 C 967.3376899999998 714.0969699999998 976.8207199999999 717.7597799999999 988.1195899999999 717.7597799999999 C 1000.5375399999999 717.7597799999999 1011.07544 712.8250999999999 1019.4403599999999 703.0919799999999 L 1020.05148 702.3806299999999 L 1028.8037 709.0202099999999 L 1028.1770099999999 709.8171899999999 C 1023.5508099999998 715.7045499999998 1017.6984799999999 720.3210199999999 1010.7834999999999 723.5371799999999 C 1003.8763099999999 726.7484699999999 996.0641199999999 728.3764899999999 987.5619899999999 728.3764899999999 Z M 987.43745 632.51859 C 973.66783 632.51859 962.05465 637.02607 952.9219400000001 645.91647 C 943.7970200000001 654.80006 939.1708100000001 665.90528 939.1708100000001 678.92365 C 939.1708100000001 691.3182499999999 942.9932200000001 702.0371399999999 950.53196 710.78352 C 959.51092 721.1345699999999 971.97072 726.38357 987.56201 726.38357 C 995.77123 726.38357 1003.30122 724.81782 1009.94274 721.7301199999999 C 1016.25925 718.7922799999999 1021.6474 714.63901 1025.97485 709.37542 L 1020.3346600000001 705.0966199999999 C 1011.6748900000001 714.8219599999999 1000.8401900000001 719.7527499999999 988.1196000000001 719.7527499999999 C 976.2826000000001 719.7527499999999 966.3315000000001 715.9001799999999 958.5426600000001 708.3010999999999 C 950.7479900000001 700.6961899999999 946.7951800000001 691.08373 946.7951800000001 679.7303599999999 C 946.7951800000001 672.2237299999999 948.5623600000001 665.3652 952.04807 659.3454899999999 C 955.5328000000001 653.3257799999999 960.5112700000001 648.4631099999999 966.8453000000001 644.89275 C 973.17348 641.3291899999999 980.14295 639.52211 987.562 639.52211 C 994.33976 639.52211 1000.65238 640.90102 1006.32663 643.62185 C 1011.72257 646.21034 1016.44316 649.99772 1020.3716400000001 654.88666 L 1026.01086 650.5601800000001 C 1021.8245100000001 645.01146 1016.4684700000001 640.61102 1010.0789800000001 637.4727200000001 C 1003.3819800000001 634.1855300000001 995.7644200000001 632.51858 987.4374500000001 632.51858 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 143.39 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1103.39, -679.45)"
              d="M 1081.27616 633.8177 L 1090.3962199999999 633.8177 L 1090.3962199999999 716.2738499999999 L 1125.5131199999998 716.2738499999999 L 1125.5131199999998 725.0844599999999 L 1081.27616 725.0844599999999 L 1081.27616 633.8177 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 143.39 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1103.39, -679.45)"
              d="M 1126.50959 726.08093 L 1080.2796899999998 726.08093 L 1080.2796899999998 632.8212199999999 L 1091.39269 632.8212199999999 L 1091.39269 715.2773699999999 L 1126.50959 715.2773699999999 L 1126.50959 726.0809199999999 Z M 1082.27263 724.08798 L 1124.5166399999998 724.08798 L 1124.5166399999998 717.27032 L 1089.3997399999998 717.27032 L 1089.3997399999998 634.81417 L 1082.27263 634.81417 L 1082.27263 724.08799 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 218.8 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1178.8, -679.45)"
              d="M 1174.24491 633.8177 L 1183.3649699999999 633.8177 L 1183.3649699999999 725.0844599999999 L 1174.24491 725.0844599999999 L 1174.24491 633.8177 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 218.8 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1178.8, -679.45)"
              d="M 1184.36144 726.08093 L 1173.2484399999998 726.08093 L 1173.2484399999998 632.8212199999999 L 1184.36144 632.8212199999999 L 1184.36144 726.08093 Z M 1175.24138 724.08798 L 1182.3684899999998 724.08798 L 1182.3684899999998 634.81416 L 1175.24138 634.81416 L 1175.24138 724.08798 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 315.78 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1275.78, -679.45)"
              d="M 1239.91478 725.08446 L 1239.91478 633.8177000000001 L 1241.90091 633.8177000000001 L 1302.64194 703.7780500000001 L 1302.64194 633.8177000000001 L 1311.63841 633.8177000000001 L 1311.63841 725.08446 L 1309.59097 725.08446 L 1249.3462299999999 655.9746200000001 L 1249.3462299999999 725.08446 L 1239.9147699999999 725.08446 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 315.78 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1275.78, -679.45)"
              d="M 1312.63489 726.08093 L 1309.13751 726.08093 L 1250.3427100000001 658.63414 L 1250.3427100000001 726.08093 L 1238.91831 726.08093 L 1238.91831 632.8212199999999 L 1242.35536 632.8212199999999 L 1242.65313 633.16473 L 1301.64547 701.11073 L 1301.64547 632.82122 L 1312.63489 632.82122 L 1312.63489 726.0809300000001 Z M 1310.04445 724.08798 L 1310.6419400000002 724.08798 L 1310.6419400000002 634.81416 L 1303.6384100000002 634.81416 L 1303.6384100000002 706.44535 L 1241.4464600000003 634.8141599999999 L 1240.9112500000003 634.8141599999999 L 1240.9112500000003 724.0879799999999 L 1248.3497600000003 724.0879799999999 L 1248.3497600000003 653.3150799999999 L 1310.0444500000003 724.0879799999999 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 412.69 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1372.69, -679.45)"
              d="M 1368.12596 633.8177 L 1377.24602 633.8177 L 1377.24602 725.0844599999999 L 1368.12596 725.0844599999999 L 1368.12596 633.8177 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 412.69 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1372.69, -679.45)"
              d="M 1378.24249 726.08093 L 1367.12949 726.08093 L 1367.12949 632.8212199999999 L 1378.24249 632.8212199999999 L 1378.24249 726.08093 Z M 1369.1224300000001 724.08798 L 1376.24954 724.08798 L 1376.24954 634.81416 L 1369.1224300000001 634.81416 L 1369.1224300000001 724.08798 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 514.12 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1474.12, -679.45)"
              d="M 1518.73343 650.75579 L 1511.53625 656.2772600000001 C 1507.5659300000002 651.1070900000001 1502.7976500000002 647.18834 1497.23531 644.5200500000001 C 1491.672 641.8527300000001 1485.56082 640.5185900000001 1478.90177 640.5185900000001 C 1471.6209 640.5185900000001 1464.87914 642.2663100000001 1458.67455 645.7607700000001 C 1452.47092 649.2571800000001 1447.66178 653.9505300000001 1444.25003 659.8447000000001 C 1440.83731 665.7388700000001 1439.13143 672.36872 1439.13143 679.7303600000001 C 1439.13143 690.8569900000001 1442.9470199999998 700.1424800000001 1450.57821 707.5878000000001 C 1458.2094 715.0331200000002 1467.8364499999998 718.7562700000001 1479.4593699999998 718.7562700000001 C 1492.2412699999998 718.7562700000001 1502.9329099999998 713.7515300000001 1511.5362499999999 703.7410600000001 L 1518.7334299999998 709.20122 C 1514.1831299999997 714.99224 1508.5059599999997 719.46956 1501.7028999999998 722.63414 C 1494.8978899999997 725.79775 1487.2978399999997 727.38003 1478.9017799999997 727.38003 C 1462.9348699999996 727.38003 1450.3397999999997 722.06583 1441.1165899999996 711.43452 C 1433.3812799999996 702.45946 1429.5141099999996 691.62184 1429.5141099999996 678.92363 C 1429.5141099999996 665.5637 1434.1986999999997 654.32321 1443.5668999999996 645.20218 C 1452.9360799999995 636.08212 1464.6718799999996 631.52209 1478.7772199999995 631.52209 C 1487.2978399999995 631.52209 1494.9913099999994 633.20753 1501.8576299999995 636.5784100000001 C 1508.7229799999996 639.9502600000001 1514.3485699999994 644.6757200000001 1518.7334399999995 650.7557600000001 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 514.33 139.45)">
            <path
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              transform=" translate(-1474.33, -679.45)"
              d="M 1478.90178 728.37652 C 1462.70716 728.37652 1449.7413399999998 722.89592 1440.36437 712.0875 C 1432.50255 702.96647 1428.51764 691.8086999999999 1428.51764 678.92365 C 1428.51764 665.34769 1433.34722 653.76175 1442.8721 644.48793 C 1452.3901600000002 635.22288 1464.47045 630.52564 1478.77722 630.52564 C 1487.41169 630.52564 1495.32509 632.2607099999999 1502.2965 635.68414 C 1509.2795899999999 639.1134099999999 1515.08132 643.98775 1519.54209 650.1728899999999 L 1520.10747 650.9572199999999 L 1511.35233 657.6736799999999 L 1510.74608 656.8844799999998 C 1506.8915599999998 651.8651399999999 1502.20113 648.0076999999998 1496.80422 645.4182299999999 C 1491.40244 642.8287599999999 1485.37885 641.5150499999999 1478.90177 641.5150499999999 C 1471.82915 641.5150499999999 1465.1886 643.2355199999998 1459.16402 646.6287799999999 C 1453.1433399999999 650.02204 1448.4159399999999 654.6365699999999 1445.1122 660.3438999999998 C 1441.80457 666.0560999999999 1440.12789 672.5789099999998 1440.12789 679.7303499999998 C 1440.12789 690.5261199999998 1443.8782899999999 699.6588299999999 1451.27398 706.8744899999998 C 1458.6774599999999 714.0969699999998 1468.16049 717.7597799999999 1479.4593599999998 717.7597799999999 C 1491.8773099999999 717.7597799999999 1502.41521 712.8250999999999 1510.7801299999999 703.0919799999999 L 1511.39125 702.3806299999999 L 1520.14347 709.0202099999999 L 1519.51678 709.8171899999999 C 1514.89058 715.7045499999998 1509.0382499999998 720.3210199999999 1502.1232699999998 723.5371799999999 C 1495.21608 726.7484699999999 1487.4038899999998 728.3764899999999 1478.9017599999997 728.3764899999999 Z M 1478.77722 632.51859 C 1465.0076 632.51859 1453.3944199999999 637.02607 1444.26171 645.91647 C 1435.13679 654.80006 1430.51058 665.90528 1430.51058 678.92365 C 1430.51058 691.3182499999999 1434.3329899999999 702.0371399999999 1441.8717299999998 710.78352 C 1450.8506899999998 721.1345699999999 1463.3104899999998 726.38357 1478.90178 726.38357 C 1487.1109999999999 726.38357 1494.6409899999999 724.81782 1501.28251 721.7301199999999 C 1507.59902 718.7922799999999 1512.98717 714.63901 1517.31462 709.37542 L 1511.67443 705.0966199999999 C 1503.01466 714.8219599999999 1492.17996 719.7527499999999 1479.45937 719.7527499999999 C 1467.62237 719.7527499999999 1457.67127 715.9001799999999 1449.88243 708.3010999999999 C 1442.0877600000001 700.6961899999999 1438.1349500000001 691.08373 1438.1349500000001 679.7303599999999 C 1438.1349500000001 672.2237299999999 1439.9021300000002 665.3652 1443.38784 659.3454899999999 C 1446.87257 653.3257799999999 1451.85104 648.4631099999999 1458.18507 644.89275 C 1464.51325 641.3291899999999 1471.48272 639.52211 1478.90177 639.52211 C 1485.6795299999999 639.52211 1491.99215 640.90102 1497.6663999999998 643.62185 C 1503.06234 646.21034 1507.7829299999999 649.99772 1511.71141 654.88666 L 1517.35063 650.5601800000001 C 1513.16428 645.01146 1507.8082399999998 640.61102 1501.4187499999998 637.4727200000001 C 1494.72175 634.1855300000001 1487.1041899999998 632.51858 1478.7772199999997 632.51858 Z"
              strokeLinecap="round"
            />
          </g>
          <g transform="matrix(1 0 0 1 -114.61 48.16)">
            <rect
              className="pathStyle"
              vectorEffect="non-scaling-stroke"
              x="-1.93342"
              y="-138.409905"
              rx="0"
              ry="0"
              width="3.86684"
              height="276.81981"
            />
          </g>
        </g>
      </g>
    </svg>
  )
}
