import { SVGProps } from 'react'

type Props = SVGProps<SVGSVGElement>

export const CheckMarkIcon = ({ className, ...props }: Props) => {
  return (
    <svg
      role="img"
      fill="none"
      stroke="currentColor"
      strokeWidth={3}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      xmlSpace="preserve"
      className={className}
      {...props}
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
    </svg>
  )
}
