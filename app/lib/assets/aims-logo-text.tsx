import { ReactNode, SVGProps } from 'react'
import { cn } from '../utils'

type Props = SVGProps<SVGSVGElement>

export const AimsLogoText = ({ className, ...props }: Props): ReactNode => {
  return (
    <svg
      role="img"
      aria-label="Aims Logo Text"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      xmlSpace="preserve"
      viewBox="0 0 728.31932 522.62282"
      className={cn('aims-logo-text', className)}
      {...props}
    >
      <g>
        <g>
          <path
            className="pathStyle-2"
            d="M150.07288,206.05588c1.5852,.11235,2.97182-.37195,4.1599-1.4501,1.24273-1.07694,1.92031-2.3939,2.03131-3.95255,.111-1.55717-.37336-2.95787-1.45028-4.20195-1.02074-1.18671-2.32512-1.83494-3.91032-1.94728-1.58661-.10956-2.97184,.37473-4.15992,1.45028-1.24423,1.02209-1.92181,2.32663-2.03135,3.91048-.11237,1.58645,.37197,2.97326,1.45019,4.15988,1.02086,1.24416,2.32387,1.92186,3.91048,2.03124Z"
          />
          <path
            className="pathStyle-2"
            d="M154.83654,238.36235c-.02104-9.18115,.02096-18.20651,0-27.38766-3.6459-.00008-7.0806-.00319-10.7265,0,0,0,1.31219,.2978,1.71706,.56609,.49776,.32983,1.20747,.81678,1.37365,1.71706,.17154,.92912,.11027,1.90605,.10883,2.85033-.00352,2.30084,.00612,3.75908,0,5.64039-.03907,12.10744,.03666,36.0322,0,36.0322,.03666,0,5.95343-8.71945,7.05738-13.34181,.4763-1.98023,.4742-4.03995,.46959-6.07661Z"
          />
          <path
            className="pathStyle-2"
            d="M153.22626,302.49389c1.18035,.8821,2.38864,1.71097,3.70954,2.39126,3.11067,1.41394,6.382,2.11588,9.8106,2.11035,2.93306-.00474,5.70026-.99397,7.70116-3.18598,2.19026-2.39945,3.04975-5.6312,3.37514-8.79295,.15813-1.53654,.20052-3.13633-.363-4.57455-1.8941-4.83413-8.93399-6.19641-13.42605-6.82808-11.24913-1.58185-16.88064-6.32756-16.89343-14.23714-.00347-2.14769,.35886-4.10361,1.08813-5.8655,.72814-1.76075,1.75429-3.27078,3.07844-4.53121,1.32415-1.2593,2.91125-2.25651,4.7613-2.99048,1.85118-.73398,3.9179-1.12658,6.2047-1.17553l1.09761-.00177c1.87386-.00303,3.73805,.31306,5.59031,.95053,1.11754,.38461,2.20121,.86478,3.24129,1.42573,.54246,.29257,1.07311,.60692,1.5914,.94042,.5164,.33229,1.03355,.58585,1.61514,.79462,.78979,.28351,1.59217,.52732,2.43974,.52732,.92335-.00149,1.64046-.1837,2.14794-.55115,.18422-.13835,.35713-.27441,.51872-.41273,.16159-.13718,.33446-.29701,.51974-.48062l.62122-.001,.02128,13.16679-.27497,.00044c-.23104-.82227-.45981-1.64454-.69084-2.46795-.23104-.82227-.5062-1.64447-.82776-2.46773-.80331-2.07436-1.98631-3.90938-3.61298-5.42987-1.6174-1.51183-3.49322-2.74391-5.48798-3.6982-.49994-.23917-1.00878-.46041-1.52549-.66086-1.99279-.77303-3.95102-1.15912-5.87353-1.15601l-.8928,.00144c-5.49383,.00888-7.8428,6.04248-8.42268,10.57537-.40637,3.1765,.06414,6.1206,2.76753,8.09052,2.5879,1.88577,5.95942,2.61034,9.06572,3.06812,6.14243,.90437,10.77335,2.6576,13.89386,5.2574,3.07398,2.51162,4.61497,5.89136,4.62184,10.14262,.00776,4.80119-1.51048,8.49593-4.55359,11.08306-3.04311,2.5894-7.40543,3.88756-13.08698,3.89674-1.74147,.00281-3.44924-.15511-5.1222-.47264-1.67296-.3164-3.2432-.79364-4.7096-1.43173-.55144-.25215-1.09249-.52785-1.61525-.8353-.52307-.30763-1.00782-.67694-1.53932-.9705-.54802-.30269-1.1564-.48975-1.75674-.65873-.45373-.12771-.92056-.21273-1.39129-.24181-.91283-.05638-1.85632,.10499-2.63407,.60704-.33746,.21784-.63466,.49427-.88056,.81161l-.6167,.001-.02139-13.23582,.20594-.00033c.62139,2.34778,1.28631,4.76554,2.43878,6.91674,.11326,.21141,.23182,.41999,.35632,.62499,.48906,.80532,1.08464,1.54066,1.70357,2.2488,.58763,.67234,1.31977,1.19078,2.03224,1.72323Z"
          />
          <path
            className="pathStyle-2"
            d="M186.98825,217.03921c-.90781-.44134-1.85954-.77593-2.83428-1.01124-1.29956-.39482-2.54756-.42805-3.81602-.42597-.13116,0-.26278,.00083-.39445,.00145l-.4092,.00145c-3.63684,.00582-7.80853,2.14522-10.88674,5.58309-1.79604,2.00607-3.28886,4.71786-4.5797,7.07596l-4.52985,7.60225c-.66258,1.05028-1.01326,2.26713-1.01129,3.5089,0,0,.01786,0,.01745,0v12.41066c1.06768-.2079,2.19226-.32628,3.37645-.35162l1.1062-.00166c.92697-.00166,1.8516,.07539,2.77358,.22929v-8.64983c0-2.53381-.05374-5.07426,.03624-7.60681,.1121-3.15521,.61014-6.29734,1.23502-9.41246,.12082-.6023,.24954-1.2046,.43926-1.78966,.79659-2.45551,3.01424-4.34838,5.10889-5.72266,2.03816-1.33731,4.44693-2.32051,6.91537-2.32445l.41086-.00125c.92515-.00519,1.81318-.00872,2.70089,.10862,.12679,.02534,.25359,.05255,.38028,.08183,2.69933,.62369,3.04976,2.1745,1.69095,4.36001-.112,.18007-.22415,.36096-.32218,.54871-.75069,1.43866-.62426,3.07006,.38531,4.15523,1.06373,1.14333,2.39979,1.38695,3.85595,.95122,1.40195-.41912,2.30306-1.68581,2.40447-3.2711,.16428-2.56787-1.25818-4.69107-4.05346-6.04998Z"
          />
          <path
            className="pathStyle-2"
            d="M142.66806,264.37983l-.31415-.53784c-.30005-.46914-.6001-.93829-.90015-1.40743-.78156-1.22201-1.56312-2.44403-2.34468-3.66604l-3.25801-5.09408c-1.21338-1.89718-2.42675-3.79436-3.64013-5.69154-1.16368-1.81948-2.32736-3.63896-3.49104-5.45843-.93691-1.46492-1.87383-2.92984-2.81074-4.39475-.72813-1.13847-1.38016-2.33802-2.2501-3.37857-.76837-.91905-2.02516-1.42056-3.20812-1.42056l-7.45554,.00595c-.00687-.00201-.01258-.00402-.01953-.00595l-1.93077,.0031c1.62391,.26292,2.90341,.96199,3.84158,2.09904,.87781,1.06461,1.3464,2.906,1.40702,5.5236v56.10499c0,.81661-.00889,1.6338,0,2.45041,.01744,1.60664-.24121,3.50639-1.29475,4.79251-.84302,1.02864-1.96791,1.69819-3.3714,2.01679h10.07917c-1.44156-.30635-2.59596-.97674-3.45818-2.02207-1.3334-1.61645-1.26801-3.72957-1.26801-5.70151v-59.40555c0-.15586,.08921-.64943,0-.78693,.00285,.00428,20.44323,31.51172,20.44323,31.51172,.67877,1.04616,2.22698,.99335,2.83298-.09642l2.47967-4.36383,.35071-.59846,4.68779-7.82804c.04989-.40294,.09072-.8509,.12299-1.33994l-5.22982,8.68984Z"
          />
          <path
            className="pathStyle-2"
            d="M143.73121,228.7752c-.68715-.93759-.90382-1.31123-1.56392-2.80281l-19.77608-48.61757-.14871-.33859c-.15792-.35971-.6713-.35043-.81613,.01477l-14.63271,36.44021-9.11529,22.45337-.01006-.00378c-.00644,.01713-.11761,.31105-.3129,.80519l-.49695,1.23313c-1.33873,3.26295-4.11053,9.57073-6.57658,12.32529-3.28163,3.66506-7.70658,5.94597-11.5484,5.95216l-.45293,.00137c-.87594,.0049-1.72172,.0089-2.56302-.07196-.22602-.04086-.45196-.08565-.67757-.13776-2.9755-.68759-3.36174-2.39692-1.86395-4.80608,.12344-.19851,.24709-.39793,.35514-.60491,.82746-1.58582,.68808-3.38414-.42477-4.58024-1.17254-1.26027-2.64529-1.5288-4.25037-1.04865-1.54537,.46217-2.53868,1.85834-2.65045,3.60576-.18107,2.83058,1.38689,5.17098,4.4681,6.66887,.77293,.37577,1.57498,.68067,2.39487,.92058,1.64143,.62061,3.19616,.66638,4.78008,.6638,.14451-.00023,.28965-.00106,.43479-.00163l.45105-.00167c2.15013-.00346,4.4697-.68417,6.68209-1.89867,.99168-.5187,1.92881-1.14136,2.789-1.86587,.89269-.71261,1.74359-1.51226,2.5293-2.38982,2.00276-2.23697,3.37262-5.10957,4.63731-7.80504,2.34867-5.00579,4.65241-10.87678,6.72476-16.00153l5.1146-12.64801c.19623-.48527,.39425-.9699,.58872-1.45587,.30444-.76077,1.1133-1.18998,1.93265-1.18998h17.83306c.82004,.00023,1.55722,.50019,1.8608,1.26187l5.20742,13.13346c.00008,.04839,.01327,.08449,.04125,.10826,.02715,.02494,.04118,.06101,.04125,.10826l.49546,1.29901c.87936,2.21347,.93739,3.80225,.17202,4.76602-.5474,.62706-1.64247,.94134-3.28643,.944l-.65725,.00106,.08226,.07207,.00011,.07223,12.33546-.02098-.00445-2.75182c-.00056-.3472,.08251-1.52738-.12268-1.80746Zm-35.07789-17.8115l9.87573-24.55509c.04813-.12031,.21847-.12001,.26608,.00053l.56696,1.43316,9.22917,23.12139h-19.93793Z"
          />
        </g>
        <g>
          <g>
            <path
              className="pathStyle-2"
              d="M274.93001,211.22911l21.40549,45.90065h-4.95441l-7.2194-15.1025h-19.77363l-7.15051,15.1025h-5.1174l21.68643-45.90065h1.12342Zm-.57935,9.75222l-7.86529,16.61507h15.69261l-7.82732-16.61507Z"
            />
            <path
              className="pathStyle-2"
              d="M296.87554,257.47387h-5.7112l-.09376-.19558-7.12564-14.90692h-19.33911l-7.15051,15.1025h-5.87822l22.01139-46.58888h1.56062l.09275,.19861,21.63367,46.39027Zm-5.27769-.68823h4.19762l-21.08456-45.21241h-.68622l-21.36147,45.21241h4.35658l7.15051-15.1025h20.20814l.09376,.19558,7.12564,14.90692Zm-8.87748-18.84511h-16.77873l8.4097-17.76504,8.36903,17.76504Zm-15.69127-.68823h14.60649l-7.2856-15.4651-7.32089,15.4651Z"
            />
          </g>
          <g>
            <path
              className="pathStyle-2"
              d="M320.68817,211.22911h4.58677v45.90065h-4.58677v-45.90065Z"
            />
            <path
              className="pathStyle-2"
              d="M325.61906,257.47387h-5.27501v-46.58888h5.27501v46.58888Zm-4.58677-.68823h3.89854v-45.21241h-3.89854v45.21241Z"
            />
          </g>
          <g>
            <path
              className="pathStyle-2"
              d="M351.59385,257.12976l6.57149-45.90065h.7457l18.65961,37.66301,18.4805-37.66301h.73528l6.60543,45.90065h-4.49839l-4.53502-32.82621-16.22962,32.82621h-1.17248l-16.42285-33.0759-4.50881,33.0759h-4.43084Z"
            />
            <path
              className="pathStyle-2"
              d="M403.78908,257.47387h-5.19536l-4.42278-32.01431-15.82838,32.01431h-1.59961l-.09477-.19121-15.92482-32.07279-4.39825,32.264h-5.12815l6.66996-46.58888h1.25751l.0951,.19121,18.34977,37.03762,18.26744-37.22883h1.24776l.04268,.29505,6.66189,46.29383Zm-4.59584-.68823h3.80142l-6.5063-45.21241h-.2228l-18.69355,38.09719-18.87435-38.09719h-.23389l-6.47303,45.21241h3.73354l4.61937-33.8878,16.82611,33.8878h.74536l16.63087-33.63812,4.64726,33.63812Z"
            />
          </g>
          <g>
            <path
              className="pathStyle-2"
              d="M425.55986,248.57993l3.90055-2.34026c2.74588,5.05489,5.91787,7.58267,9.51699,7.58267,1.53912,0,2.98481-.3589,4.33742-1.07671,1.35194-.71781,2.3816-1.67959,3.08899-2.88635,.70705-1.20609,1.06092-2.48544,1.06092-3.83805,0-1.53912-.52021-3.04732-1.55995-4.5246-1.43561-2.03849-4.05648-4.49335-7.86328-7.3639-3.82797-2.89139-6.20957-4.98197-7.14581-6.27207-1.62246-2.16317-2.43402-4.50343-2.43402-7.02079,0-1.99682,.47854-3.81688,1.43561-5.46051,.9564-1.64329,2.30364-2.9381,4.04069-3.88476,1.73672-.94666,3.62465-1.41982,5.66348-1.41982,2.1635,0,4.1862,.53567,6.0691,1.607,1.88256,1.07167,3.87434,3.04228,5.97567,5.91283l-3.74463,2.83964c-1.72697-2.28818-3.19821-3.79604-4.41538-4.5246-1.21684-.72789-2.5429-1.09217-3.97818-1.09217-1.85198,0-3.36522,.56188-4.5404,1.6853-1.17551,1.12309-1.76293,2.50695-1.76293,4.1499,0,.99841,.20768,1.9659,.62405,2.90181,.41603,.93624,1.17517,1.95582,2.2781,3.05807,.60288,.58271,2.5792,2.11175,5.92863,4.58711,3.97314,2.93306,6.69785,5.54385,8.17513,7.83202,1.47695,2.28851,2.21559,4.58677,2.21559,6.89612,0,3.32826-1.26389,6.21998-3.79134,8.67451-2.52745,2.45486-5.60098,3.68212-9.22059,3.68212-2.78789,0-5.31533-.74402-7.58234-2.23105-2.26768-1.48737-4.35826-3.97852-6.27207-7.47346Z"
            />
            <path
              className="pathStyle-2"
              d="M439.41427,258.62855c-2.84266,0-5.45715-.76956-7.7712-2.2875-2.30431-1.51156-4.45269-4.06724-6.38498-7.59578l-.15895-.29035,4.49234-2.69514,.17139,.31555c2.66825,4.91207,5.76867,7.40289,9.21454,7.40289,1.47493,0,2.87996-.34882,4.17612-1.03672,1.28876-.6842,2.28246-1.61137,2.95356-2.7563,.67244-1.14728,1.01353-2.37992,1.01353-3.66397,0-1.45947-.50374-2.91491-1.49711-4.32667-1.40907-2.00018-4.0296-4.45201-7.78935-7.28695-3.89081-2.93911-6.25157-5.01457-7.21705-6.34499-1.65741-2.20988-2.49955-4.64121-2.49955-7.22276,0-2.04891,.4987-3.94458,1.48232-5.63357,.98362-1.69034,2.38798-3.04093,4.17343-4.0138,1.77939-.96985,3.74026-1.46183,5.82815-1.46183,2.21357,0,4.31255,.55583,6.23914,1.65203,1.9165,1.09082,3.9634,3.11251,6.08321,6.00861l.19995,.27321-4.28903,3.25231-.20768-.27523c-1.69236-2.24214-3.14477-3.73454-4.31759-4.43656-1.15736-.69227-2.43638-1.04344-3.80142-1.04344-1.7515,0-3.19888,.53499-4.30247,1.58986-1.11502,1.06495-1.65674,2.34127-1.65674,3.90123,0,.94632,.19995,1.87551,.59448,2.76201,.39721,.8939,1.13955,1.88794,2.20685,2.95457,.58473,.5649,2.56777,2.09864,5.88998,4.5535,3.98725,2.94348,6.76607,5.60904,8.25948,7.92242,1.50686,2.33455,2.2707,4.7175,2.2707,7.08263,0,3.40925-1.3106,6.41086-3.89585,8.9215-2.58189,2.50762-5.76463,3.77924-9.46019,3.77924Zm-13.39234-9.92462c1.83484,3.27886,3.85216,5.65374,5.99886,7.06179,2.1998,1.44301,4.68725,2.17459,7.39348,2.17459,3.51006,0,6.53184-1.20609,8.98098-3.585,2.44646-2.37589,3.68682-5.21116,3.68682-8.42751,0-2.23071-.72688-4.48797-2.16048-6.70961-1.44838-2.24281-4.1704-4.84755-8.09045-7.74163-3.39379-2.50795-5.34423-4.01817-5.96358-4.61668-1.13115-1.13048-1.92154-2.19408-2.35337-3.16561-.43351-.97489-.65362-1.9985-.65362-3.04161,0-1.73369,.62909-3.21366,1.86946-4.39858,1.23566-1.18156,2.84333-1.78074,4.77798-1.78074,1.4914,0,2.88904,.38377,4.15494,1.1409,1.21651,.72822,2.62658,2.14872,4.30449,4.33809l3.20022-2.42663c-1.98976-2.67161-3.89551-4.53603-5.66751-5.54486-1.8214-1.03638-3.80612-1.56197-5.89872-1.56197-1.97128,0-3.82158,.46342-5.49882,1.37781-1.67354,.91204-2.98817,2.1756-3.90795,3.75572-.92145,1.5828-1.3889,3.36186-1.3889,5.28744,0,2.42932,.79577,4.72186,2.36513,6.81446,.91541,1.2612,3.29566,3.34674,7.07792,6.20385,3.81654,2.87795,6.48714,5.3812,7.93721,7.44019,1.07671,1.53004,1.62279,3.1189,1.62279,4.72287,0,1.40806-.37268,2.75764-1.1083,4.01212-.73629,1.2565-1.8214,2.27137-3.22442,3.01607-1.39663,.74133-2.91021,1.11704-4.49873,1.11704-3.64112,0-6.88537-2.5056-9.64603-7.44792l-3.30944,1.98539Z"
            />
          </g>
          <g>
            <path
              className="pathStyle-2"
              d="M282.90217,280.32741l-2.48544,1.90676c-1.37109-1.78544-3.01775-3.13872-4.93862-4.06018-1.92121-.92112-4.03161-1.38185-6.33122-1.38185-2.51434,0-4.84251,.60355-6.98517,1.81031-2.14233,1.20743-3.8031,2.82821-4.9813,4.86368-1.17853,2.03547-1.76763,4.32499-1.76763,6.86722,0,3.84242,1.31766,7.04902,3.95298,9.62016s5.95988,3.85687,9.97368,3.85687c4.41404,0,8.10624-1.72831,11.07728-5.18528l2.48544,1.88559c-1.57138,1.99984-3.5319,3.54602-5.88124,4.63886-2.35001,1.0925-4.97458,1.63892-7.87403,1.63892-5.51394,0-9.86346-1.83518-13.04856-5.50655-2.67128-3.09941-4.00675-6.84202-4.00675-11.22716,0-4.61366,1.61775-8.49539,4.85292-11.6452,3.23551-3.14948,7.28829-4.72422,12.15936-4.72422,2.94247,0,5.5993,.58204,7.97048,1.74613,2.37085,1.16442,4.31356,2.79629,5.82781,4.89594Z"
            />
            <path
              className="pathStyle-2"
              d="M269.14689,307.13259c-5.59257,0-10.07013-1.89264-13.30832-5.62517-2.71496-3.14981-4.09109-7.00298-4.09109-11.45265,0-4.68826,1.66783-8.68929,4.9571-11.89187,3.28692-3.19955,7.45867-4.82167,12.3993-4.82167,2.98179,0,5.71456,.59918,8.12204,1.78141,2.41151,1.18425,4.41505,2.86753,5.95551,5.00348l.19525,.27086-3.02346,2.31943-.20936-.27254c-1.3311-1.73336-2.95087-3.06546-4.81461-3.9597-1.86542-.89423-3.94558-1.3479-6.18235-1.3479-2.44242,0-4.73564,.59414-6.81614,1.76595-2.07915,1.17181-3.71169,2.76537-4.85259,4.73631-1.14224,1.97262-1.72126,4.22518-1.72126,6.69482,0,3.72816,1.29514,6.88201,3.84914,9.37383,2.55668,2.49418,5.83151,3.75908,9.7334,3.75908,4.28836,0,7.92746-1.70412,10.81616-5.06531l.21104-.24565,3.02245,2.29288-.21642,.27523c-1.59759,2.03311-3.61861,3.62734-6.00659,4.73799-2.3853,1.10897-5.08312,1.67119-8.0192,1.67119Zm-.04301-33.10312c-4.75513,0-8.76558,1.55659-11.91942,4.62676-3.15116,3.06782-4.74875,6.90284-4.74875,11.39854,0,4.28029,1.32001,7.9819,3.9234,11.00234,3.10075,3.57458,7.40356,5.38725,12.78778,5.38725,2.83493,0,5.4353-.54071,7.72886-1.607,2.18131-1.01454,4.04203-2.44881,5.53645-4.26651l-1.94776-1.47762c-2.99053,3.3585-6.73213,5.06128-11.125,5.06128-4.08773,0-7.5242-1.33043-10.21396-3.95466-2.69178-2.62624-4.05682-5.94576-4.05682-9.86648,0-2.5923,.61027-4.9608,1.81401-7.03961,1.2034-2.07882,2.92264-3.75807,5.11-4.99104,2.18534-1.23062,4.59215-1.85467,7.15421-1.85467,2.3406,0,4.52057,.47619,6.48009,1.41579,1.86341,.8939,3.49359,2.20181,4.85024,3.89014l1.94742-1.49409c-1.44569-1.91617-3.29532-3.43579-5.50184-4.51956-2.31271-1.13518-4.94332-1.71084-7.81892-1.71084Z"
            />
          </g>
          <g>
            <path
              className="pathStyle-2"
              d="M301.50969,274.4781h3.14948v28.47501h12.1271v3.04261h-15.27658v-31.51763Z"
            />
            <path
              className="pathStyle-2"
              d="M317.13039,306.33984h-15.96481v-32.20586h3.83771v28.47501h12.1271v3.73085Zm-15.27658-.68823h14.58835v-2.35438h-12.1271v-28.47501h-2.46124v30.82939Z"
            />
          </g>
          <g>
            <path
              className="pathStyle-2"
              d="M333.61507,274.4781h3.14948v31.51763h-3.14948v-31.51763Z"
            />
            <path
              className="pathStyle-2"
              d="M337.10867,306.33984h-3.83771v-32.20586h3.83771v32.20586Zm-3.14948-.68823h2.46124v-30.82939h-2.46124v30.82939Z"
            />
          </g>
          <g>
            <path
              className="pathStyle-2"
              d="M356.29319,305.99572v-31.51763h.68588l20.97602,24.15977v-24.15977h3.1068v31.51763h-.70705l-20.80463-23.86606v23.86606h-3.25701Z"
            />
            <path
              className="pathStyle-2"
              d="M381.40601,306.33984h-1.20777l-20.30391-23.29175v23.29175h-3.94525v-32.20586h1.18694l.10283,.11863,20.37213,23.46415v-23.58277h3.79503v32.20586Zm-.89457-.68823h.20634v-30.82939h-2.41856v24.73677l-21.47707-24.73677h-.18483v30.82939h2.56878v-24.44038l21.30535,24.44038Z"
            />
          </g>
          <g>
            <path
              className="pathStyle-2"
              d="M400.56903,274.4781h3.14948v31.51763h-3.14948v-31.51763Z"
            />
            <path
              className="pathStyle-2"
              d="M404.06262,306.33984h-3.83771v-32.20586h3.83771v32.20586Zm-3.14948-.68823h2.46124v-30.82939h-2.46124v30.82939Z"
            />
          </g>
          <g>
            <path
              className="pathStyle-2"
              d="M452.5791,280.32741l-2.48544,1.90676c-1.37109-1.78544-3.01775-3.13872-4.93862-4.06018-1.92121-.92112-4.03161-1.38185-6.33122-1.38185-2.51434,0-4.84251,.60355-6.98517,1.81031-2.14233,1.20743-3.8031,2.82821-4.9813,4.86368-1.17853,2.03547-1.76763,4.32499-1.76763,6.86722,0,3.84242,1.31766,7.04902,3.95298,9.62016,2.63532,2.57113,5.95988,3.85687,9.97368,3.85687,4.41404,0,8.10624-1.72831,11.07728-5.18528l2.48544,1.88559c-1.57138,1.99984-3.5319,3.54602-5.88124,4.63886-2.35001,1.0925-4.97458,1.63892-7.87403,1.63892-5.51394,0-9.86346-1.83518-13.04856-5.50655-2.67128-3.09941-4.00675-6.84202-4.00675-11.22716,0-4.61366,1.61775-8.49539,4.85292-11.6452,3.23551-3.14948,7.28829-4.72422,12.15936-4.72422,2.94247,0,5.5993,.58204,7.97048,1.74613,2.37085,1.16442,4.31356,2.79629,5.82781,4.89594Z"
            />
            <path
              className="pathStyle-2"
              d="M438.82382,307.13259c-5.59257,0-10.07013-1.89264-13.30832-5.62517-2.71496-3.14981-4.09109-7.00298-4.09109-11.45265,0-4.68826,1.66783-8.68929,4.9571-11.89187,3.28692-3.19955,7.45867-4.82167,12.3993-4.82167,2.98179,0,5.71456,.59918,8.12204,1.78141,2.41151,1.18425,4.41505,2.86753,5.95551,5.00348l.19525,.27086-3.02346,2.31943-.20936-.27254c-1.3311-1.73336-2.95087-3.06546-4.81461-3.9597-1.86542-.89423-3.94558-1.3479-6.18235-1.3479-2.44242,0-4.73564,.59414-6.81614,1.76595-2.07915,1.17181-3.71169,2.76537-4.85259,4.73631-1.14224,1.97262-1.72126,4.22518-1.72126,6.69482,0,3.72816,1.29514,6.88201,3.84914,9.37383,2.55668,2.49418,5.83151,3.75908,9.7334,3.75908,4.28836,0,7.92746-1.70412,10.81616-5.06531l.21104-.24565,3.02245,2.29288-.21642,.27523c-1.59759,2.03311-3.61861,3.62734-6.00659,4.73799-2.3853,1.10897-5.08312,1.67119-8.0192,1.67119Zm-.04301-33.10312c-4.75513,0-8.76558,1.55659-11.91942,4.62676-3.15116,3.06782-4.74875,6.90284-4.74875,11.39854,0,4.28029,1.32001,7.9819,3.9234,11.00234,3.10075,3.57458,7.40356,5.38725,12.78778,5.38725,2.83493,0,5.4353-.54071,7.72886-1.607,2.18131-1.01454,4.04203-2.44881,5.53645-4.26651l-1.94776-1.47762c-2.99053,3.3585-6.73213,5.06128-11.125,5.06128-4.08773,0-7.5242-1.33043-10.21396-3.95466-2.69178-2.62624-4.05682-5.94576-4.05682-9.86648,0-2.5923,.61027-4.9608,1.81401-7.03961,1.2034-2.07882,2.92264-3.75807,5.11-4.99104,2.18534-1.23062,4.59215-1.85467,7.15421-1.85467,2.3406,0,4.52057,.47619,6.48009,1.41579,1.86341,.8939,3.49359,2.20181,4.85024,3.89014l1.94742-1.49409c-1.44569-1.91617-3.29532-3.43579-5.50184-4.51956-2.31271-1.13518-4.94332-1.71084-7.81892-1.71084Z"
            />
          </g>
        </g>
        <rect
          className="pathStyle-2"
          x="219.38311"
          y="210.91371"
          width="1.33536"
          height="95.59563"
        />
      </g>
      <text
        className="pathStyle"
        transform="translate(66.08999 360.21221) scale(1.0222 1)"
      >
        <tspan x="0" y="0" className="hidden sm:flex">
          Aims for your perfection
        </tspan>
      </text>
    </svg>
  )
}
