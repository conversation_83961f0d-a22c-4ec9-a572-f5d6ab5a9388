export interface XiorClientConfig {
  baseURL: string
  token: string
  headers?: Record<string, string>
}

export interface Pagination {
  page: number
  pageSize: number
  pageCount: number
  total: number
}

export interface StrapiPagination {
  meta?: {
    pagination?: Pagination
  }
}

export interface PaginationQuery {
  withCount?: boolean
  start?: number
  limit?: number
  //-------------
  page?: number
  pageSize?: number
}

export interface StrapiAttributes<T> {
  id: number
  attributes?: T
}

export interface StrapiCollectionType<T>
  extends StrapiFields<T>,
    StrapiPagination {}

export interface StrapiSingleType<T> extends StrapiField<T> {
  meta?: unknown
}

export interface StrapiField<T> {
  data?: StrapiAttributes<T>
}

export interface StrapiFields<T> {
  data?: StrapiAttributes<T>[]
}
