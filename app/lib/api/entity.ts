import { LayoutAlignment } from '@/enums/layoutAlignment'
import { Seo } from '../server/seo/types'
import { StrapiCollectionType, StrapiField, StrapiFields } from './types'
import { MediaStyle } from '@/enums/mediaStyle'

export interface PageEntity extends BaseEntity {
  id: number
  slug?: string
  name?: string
  backgroundImage: StrapiField<ImageType> | null
  images: StrapiFields<ImageType> | null
  seo?: Seo | null
}

export interface MainMenuEntity extends BaseEntity {
  mainMenuItems?: MainMenuField[]
}

export interface MainMenuField {
  id: number
  __component: Field[] | null
  name: string | null
  url: string | null
  showFooter?: boolean
  subMenuLink?: SubMainMenuField[]
}

export interface SubMainMenuField {
  id: number
  __component: Field[] | null
  name: string | null
  image: StrapiField<ImageType> | null
  structuredData: TrustedHTML | null
  url: string | null
  menu: MenuField[]
}

export interface MenuField {
  name: string | null
  url: string | null
}

export interface ServiceEntity extends BaseEntity {
  slug?: string
  name?: string
  reviews?: StrapiFields<ReviewEntity> | null
  articles?: StrapiFields<ArticleEntity> | null
  promotions?: StrapiFields<PromotionEntity> | null
  seo?: Seo | null
  tag: string | null
  thumbnail?: StrapiField<ImageType> | null
  bannerImage?: StrapiField<ImageType> | null
  header?: ServiceHeader | null
  content?: Array<
    | ServiceContentImage
    | ServiceContentDetail
    | ServiceContentBoxes
    | ServiceContentBasic
    | ServiceVdo
    | ServiceReview
    | ServicePromotion
    | ServiceSocial
  > | null
  localizations?: StrapiCollectionType<ServiceEntity>
  questionAnswer: ServiceQuestionAnswer[] | null
  popupPreview?: PopupPreview | null
}

export interface ServiceQuestionAnswer {
  id: number
  question: string | null
  answer: TrustedHTML | null
}

export interface ServiceHeader {
  id: number
  title: string | null
  image?: StrapiFields<ImageType> | null
  description: TrustedHTML | null
  type: string | null
}

export interface ReviewEntity extends BaseEntity {
  id: number
  image?: StrapiField<ImageType> | null
  services?: StrapiCollectionType<ServiceEntity> | null
  isShowOnHome: boolean | null
  isShowOnService: boolean | null
}

export interface CustomerEntity extends BaseEntity {
  email: string
  firstName: string
  lastName: string
  nickName: string
  age: number
  mobileNo: string
  address: string
  birthDate: string
  gender: string
  message: string
  wantedServices: string
  sources: string
  reasons: string
}

export interface CustomerContactEntity extends BaseEntity {
  name: string
  email: string
  message: string
}

export interface PromotionEntity extends BaseEntity {
  id: number
  image?: StrapiField<ImageType> | null
  services?: StrapiCollectionType<ServiceEntity> | null
}

export interface AboutUsEntity extends BaseEntity {
  landing: ContentMultiMedia | null
  profile: ContentMedia | null
  experience: ContentMedia | null
  main: ContentMedia | null
  mainDescription: TrustedHTML | null
  reason: ContentMedia | null
}

export interface ContactEntity extends BaseEntity {
  title: string | null
  subTitle: string | null
  openTitle: string | null
  openTime: string | null
  telephone: string | null
  email: string | null
  address?: Field[] | null
  map?: MapComponent | null
  backgroundImage: StrapiField<ImageType> | null
  images: StrapiFields<ImageType> | null
}

export interface ContentMedia {
  content: TrustedHTML | null
  media: StrapiField<ImageType> | null
  alignment: LayoutAlignment | null
  mediaStyle: MediaStyle | null
}

export interface ContentMultiMedia {
  content: TrustedHTML | null
  media: StrapiFields<ImageType> | null
  alignment: LayoutAlignment | null
}

export interface ImageType {
  name: string | null
  alternativeText: string | null
  caption: string | null
  width: number
  height: number
  formats: ImageFormat
  hash?: string | null
  ext?: string | null
  mime?: string | null
  size: number
  url?: string | null
  previewUrl?: string | null
  provider?: string | null
  provider_metadata?: Record<string, unknown> | null
}

export interface ImageFormat {
  // strapi optimizer
  thumbnail: ImageBody | null
  medium?: ImageBody | null
  small?: ImageBody | null
  large?: ImageBody | null
  // image optimizer
  thumbnail_webp: ImageBody | null
  thumbnail_avif: ImageBody | null
  sm?: ImageBody | null
  sm_webp?: ImageBody | null
  sm_avif?: ImageBody | null
  xs?: ImageBody | null
  xs_webp?: ImageBody | null
  xs_avif?: ImageBody | null
  md?: ImageBody | null
  md_webp?: ImageBody | null
  md_avif?: ImageBody | null
  lg?: ImageBody | null
  lg_webp?: ImageBody | null
  lg_avif?: ImageBody | null
  xl?: ImageBody | null
  xl_webp?: ImageBody | null
  xl_avif?: ImageBody | null
  original?: ImageBody | null
  original_webp?: ImageBody | null
  original_avif?: ImageBody | null
}

export interface ImageBody {
  name: string | null
  hash: string | null
  ext: string | null
  mime: string | null
  path?: string | null
  width: number
  height: number
  size: number
  sizeInBytes?: number
  url: string | null
}

export interface MapComponent {
  id: number
  title: string | null
  detail: string | null
  link: string | null
  linkDetail: string | null
  telephone: string | null
  address: string | null
  zoomLevel: number | null
  markerLat: number | null
  markerLon: number | null
  centerLon: number | null
  centerLat: number | null
  locationLon: number | null
  locationLat: number | null
}

export interface BaseEntity {
  locale?: string
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
}

export interface RichTextBlocksType {
  text?: string
  level?: number
  code?: string
  src?: string
  title?: string
  href?: string
}

export interface Field extends RichTextBlocksType {
  alt: string
  type: string
  children: RichTextBlocksType[]
}

export interface ServiceContentImage {
  id: number
  __component: string
  description1: TrustedHTML | null
  image1: StrapiField<ImageType> | null
  description2: TrustedHTML | null
  image2: StrapiField<ImageType> | null
  type: string | null
}

export interface ServiceContentDynamic {
  id: number
  __component: string
  content?: TrustedHTML | null
  backgroundImage?: StrapiField<ImageType> | null
  backgroundColor?: string | null
  hiddenGap?: boolean | null
  buttonImage?: StrapiField<ImageType> | null
}

export interface ServiceContentDetail {
  id: number
  __component: string
  description: TrustedHTML | null
  images: MetaImage[] | null
  type: string | null
}

export interface ServiceContentBasic {
  id: number
  __component: string
  title: string | null
  description: TrustedHTML | null
  images: MetaImage[] | null
  type: string | null
}

export interface ServiceContentBoxes {
  id: number
  __component: string
  title: string | null
  description: TrustedHTML | null
  images: MetaImage[] | null
  type: string | null
}

export interface ServiceVdo {
  id: number
  __component: string
  title: string | null
  vdoUrl: string | null
  type: string | null
}

export interface ServicePromotion {
  id: number
  __component: string
  type: string | null
  promotions?: StrapiFields<PromotionEntity> | null
  buttonImage: StrapiField<ImageType>
  backgroundColor: string | null
}

export interface ServiceReview {
  id: number
  __component: string
  type: string | null
  reviews?: StrapiFields<ReviewEntity> | null
}

export interface ServiceSocial {
  id: number
  __component: string
  links?: ImageLink[] | null
  type: string | null
  backgroundImage?: StrapiField<ImageType> | null
}

export interface MetaImage {
  id: number
  image?: StrapiField<ImageType> | null
  description: string | null
}

export interface ArticleEntity extends BaseEntity {
  id: number
  title: string
  subtitle?: string
  content: TrustedHTML | null
  seo?: Seo | null
  image?: StrapiField<ImageType> | null
  service?: StrapiField<ServiceEntity> | null
  slug: string
}

export interface ImageLink {
  id: number
  url: string | null
}

export interface SocialMediaEntity extends BaseEntity {
  socials?: {
    type: string
    url: string
  }[]
}

export interface HomeEntity extends BaseEntity {
  banner: TrustedHTML | null
  sticky: TrustedHTML | null
  contactButton?: StrapiField<ImageType> | null
}

export interface PopupPreview {
  id: number
  isShow: boolean
  image: StrapiField<ImageType>
  isShowContactButton: boolean
  buttonImage?: StrapiField<ImageType> | null
}
