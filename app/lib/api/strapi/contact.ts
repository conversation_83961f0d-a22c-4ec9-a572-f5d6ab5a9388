import { InternalApi } from '../internalApi'
import { ContactEntity } from '../entity'
import { StrapiSingleType } from '../types'
import { STRAPI_API, STRAPI_CONTACT } from '@/constants/http'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiGetContact = async (
  param?: string,
  lng?: string
): Promise<StrapiSingleType<ContactEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_CONTACT}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiSingleType<ContactEntity>>(STRAPI_API, tempParam)
}

export { strapiGetContact }
