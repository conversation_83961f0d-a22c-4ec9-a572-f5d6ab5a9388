import { InternalApi } from '../internalApi'
import { AboutUsEntity } from '../entity'
import { StrapiSingleType } from '../types'
import { STRAPI_API, STRAPI_ABOUT_US } from '@/constants/http'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiGetAboutUs = async (
  param?: string,
  lng?: string
): Promise<StrapiSingleType<AboutUsEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_ABOUT_US}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiSingleType<AboutUsEntity>>(STRAPI_API, tempParam)
}

export { strapiGetAboutUs }
