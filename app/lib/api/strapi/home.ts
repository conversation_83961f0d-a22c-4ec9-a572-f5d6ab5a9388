import { InternalApi } from '../internalApi'
import { HomeEntity } from '../entity'
import { StrapiSingleType } from '../types'
import { STRAPI_API, STRAPI_HOME } from '@/constants/http'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiGetHome = async (
  param?: string,
  lng?: string
): Promise<StrapiSingleType<HomeEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_HOME}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiSingleType<HomeEntity>>(STRAPI_API, tempParam)
}

export { strapiGetHome }
