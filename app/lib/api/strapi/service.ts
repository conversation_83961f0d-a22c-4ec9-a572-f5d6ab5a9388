import { InternalApi } from '../internalApi'
import { ServiceEntity } from '../entity'
import { StrapiCollectionType } from '../types'
import { STRAPI_API, STRAPI_SERVICES } from '@/constants/http'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiGetServices = async (
  param?: string,
  lng?: string
): Promise<StrapiCollectionType<ServiceEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_SERVICES}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiCollectionType<ServiceEntity>>(
    STRAPI_API,
    tempParam
  )
}

export { strapiGetServices }
