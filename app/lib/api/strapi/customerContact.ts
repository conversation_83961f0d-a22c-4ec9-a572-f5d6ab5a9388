import { InternalApi } from '../internalApi'
import { CustomerContactEntity } from '../entity'
import { StrapiSingleType } from '../types'
import { STRAPI_API, STRAPI_CUSTOMER_CONTRACTS } from '@/constants/http'

const strapiCreateCustomerContacts = async (
  payload
): Promise<StrapiSingleType<CustomerContactEntity>> => {
  const internalApi = new InternalApi()

  const body = {
    endpoint: STRAPI_CUSTOMER_CONTRACTS,
    payload,
  }

  return internalApi.post<StrapiSingleType<CustomerContactEntity>>(
    STRAPI_API,
    body
  )
}

export { strapiCreateCustomerContacts }
