import { InternalApi } from '../internalApi'
import { PageEntity } from '../entity'
import { StrapiCollectionType } from '../types'
import { STRAPI_API, STRAPI_PAGE } from '@/constants/http'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiGetPage = async (
  param?: string,
  lng?: string
): Promise<StrapiCollectionType<PageEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_PAGE}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiCollectionType<PageEntity>>(
    STRAPI_API,
    tempParam
  )
}

export { strapiGetPage }
