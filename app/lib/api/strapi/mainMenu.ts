import { InternalApi } from '../internalApi'
import { MainMenuEntity } from '../entity'
import { StrapiSingleType } from '../types'
import { STRAPI_API, STRAPI_MAIN_MENU } from '@/constants/http'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiMainMenu = async (
  param?: string,
  lng?: string
): Promise<StrapiSingleType<MainMenuEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_MAIN_MENU}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiSingleType<MainMenuEntity>>(
    STRAPI_API,
    tempParam
  )
}

export { strapiMainMenu }
