import { InternalApi } from '../internalApi'
import { CustomerEntity } from '../entity'
import { StrapiCollectionType, StrapiSingleType } from '../types'
import { STRAPI_API, STRAPI_CUSTOMERS } from '@/constants/http'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiCreateCustomers = async (
  payload
): Promise<StrapiSingleType<CustomerEntity>> => {
  const internalApi = new InternalApi()

  const body = {
    endpoint: STRAPI_CUSTOMERS,
    payload,
  }

  return internalApi.post<StrapiSingleType<CustomerEntity>>(STRAPI_API, body)
}

const strapiGetCustomers = async (
  param?: string,
  lng?: string
): Promise<StrapiCollectionType<CustomerEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_CUSTOMERS}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiCollectionType<CustomerEntity>>(
    STRAPI_API,
    tempParam
  )
}
export { strapiCreateCustomers, strapiGetCustomers }
