import { InternalApi } from '../internalApi'
import { PromotionEntity } from '../entity'
import { StrapiCollectionType } from '../types'
import { STRAPI_API, STRAPI_PROMOTIONS } from '@/constants/http'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiGetPromotions = async (
  param?: string,
  lng?: string
): Promise<StrapiCollectionType<PromotionEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_PROMOTIONS}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiCollectionType<PromotionEntity>>(
    STRAPI_API,
    tempParam
  )
}

export { strapiGetPromotions }
