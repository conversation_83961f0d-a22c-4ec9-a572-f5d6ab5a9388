import { ArticleEntity } from '@/lib/api/entity'
import { InternalApi } from '@/lib/api/internalApi'
import { STRAPI_API, STRAPI_ARTICLE } from '@/constants/http'
import { StrapiCollectionType } from '@/lib/api/types'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiGetArticles = async (
  param?: string,
  lng?: string
): Promise<StrapiCollectionType<ArticleEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_ARTICLE}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiCollectionType<ArticleEntity>>(
    STRAPI_API,
    tempParam
  )
}

export { strapiGetArticles }
