import { InternalApi } from '../internalApi'
import { ReviewEntity } from '../entity'
import { StrapiCollectionType } from '../types'
import { STRAPI_API, STRAPI_REVIEWS } from '@/constants/http'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiGetReviews = async (
  param?: string,
  lng?: string
): Promise<StrapiCollectionType<ReviewEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_REVIEWS}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiCollectionType<ReviewEntity>>(
    STRAPI_API,
    tempParam
  )
}

export { strapiGetReviews }
