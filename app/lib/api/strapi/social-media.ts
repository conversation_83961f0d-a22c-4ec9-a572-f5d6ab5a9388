import { InternalApi } from '../internalApi'
import { SocialMediaEntity } from '../entity'
import { StrapiSingleType } from '../types'
import { STRAPI_API, STRAPI_SOCIAL_MEDIA } from '@/constants/http'
import { mappingLanguage } from '@/constants/language'
import { DefaultLocale } from '@/lib/server/translations/i18n.config'

const strapiGetSocialMedia = async (
  param?: string,
  lng?: string
): Promise<StrapiSingleType<SocialMediaEntity>> => {
  const internalApi = new InternalApi()

  let endpoint = `${STRAPI_SOCIAL_MEDIA}?${param}`
  if (lng && lng !== DefaultLocale) {
    endpoint += `&locale=${mappingLanguage[lng]}`
  }

  const tempParam = { endpoint }

  return internalApi.get<StrapiSingleType<SocialMediaEntity>>(
    STRAPI_API,
    tempParam
  )
}

export { strapiGetSocialMedia }
