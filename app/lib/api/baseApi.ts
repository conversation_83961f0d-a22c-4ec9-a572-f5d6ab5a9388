import { <PERSON><PERSON>, XiorResponse } from 'xior'
import { XiorClientConfig } from './types'
import { APPLICATION_JSON, DELETE, POST, PUT } from '@/constants/http'

export class BaseApi {
  private readonly xiorClient: Xior

  constructor(config: XiorClientConfig) {
    const headers: Record<string, string> = {
      'Content-Type': APPLICATION_JSON,
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, POST, PUT, DELETE',
      ...(config.token ? { Authorization: `Bearer ${config.token}` } : {}),
      ...config.headers,
    }

    this.xiorClient = new Xior({
      baseURL: config.baseURL,
      headers,
    })
  }

  private async handleResponse<T>(response: XiorResponse<T>): Promise<T> {
    if (response.status < 200 || response.status >= 300) {
      console.error(
        `Request failed with status: ${response.status} - ${response.statusText}`
      )
      throw new Error(
        `Request failed: ${response.statusText} (Status Code: ${response.status})`
      )
    }
    return response.data
  }

  async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
    try {
      const response = await this.xiorClient.get<T>(endpoint, { params })
      return this.handleResponse(response)
    } catch (error) {
      console.error(`Error during GET request to ${endpoint}:`, error)
      throw new Error(`Error fetching data from ${endpoint}`)
    }
  }

  async post<T>(endpoint: string, body: unknown): Promise<T> {
    try {
      const response = await this.xiorClient.post<T>(endpoint, body, {
        method: POST,
        headers: {
          'Content-Type': APPLICATION_JSON,
        },
      })
      return this.handleResponse(response)
    } catch (error) {
      console.error(`Error during POST request to ${endpoint}:`, error)
      throw new Error(`Error posting data to ${endpoint}`)
    }
  }

  async put<T>(endpoint: string, body: unknown): Promise<T> {
    try {
      const response = await this.xiorClient.put<T>(endpoint, body, {
        method: PUT,
        headers: {
          'Content-Type': APPLICATION_JSON,
        },
      })
      return this.handleResponse(response)
    } catch (error) {
      console.error(`Error during PUT request to ${endpoint}:`, error)
      throw new Error(`Error updating data at ${endpoint}`)
    }
  }

  async delete<T>(endpoint: string): Promise<T> {
    try {
      const response = await this.xiorClient.delete<T>(endpoint, {
        method: DELETE,
      })
      return this.handleResponse(response)
    } catch (error) {
      console.error(`Error during DELETE request to ${endpoint}:`, error)
      throw new Error(`Error deleting data at ${endpoint}`)
    }
  }
}
