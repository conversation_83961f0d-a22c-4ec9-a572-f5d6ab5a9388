import { LANGUAGE } from '@/constants/language'

export const SupportLocales = [LANGUAGE.THA, LANGUAGE.ENG]

export const DefaultLocale = LANGUAGE.THA
export const DefaultFallbackLocale = LANGUAGE.THA

export const i18nConfig = {
  supportedLngs: SupportLocales,
  fallbackLng: DefaultFallbackLocale,
  defaultNS: 'common',
  lng: DefaultLocale,
  ns: ['common'],
  react: {
    useSuspense: false,
  },
  debug: import.meta.env.DEV,
  interpolation: {
    escapeValue: false,
  },
}
