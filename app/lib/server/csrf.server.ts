import { createCookie } from '@remix-run/node'
import { CSRF, CSRFError } from 'remix-utils/csrf/server'

export const cookie = createCookie('csrf', {
  path: '/',
  httpOnly: true,
  secure: import.meta.env.PROD,
  sameSite: 'lax',
  secrets: [import.meta.env.VITE_SESSION_SECRET],
})

export const csrf = new CSRF({
  cookie,
  formDataKey: 'csrf',
  secret: import.meta.env.VITE_SESSION_SECRET,
})

export async function validateCsrfToken(request: Request) {
  try {
    await csrf.validate(request)
  } catch (error) {
    if (error instanceof CSRFError) {
      throw new Response('Invalid CSRF token', { status: 403 })
    }
    throw error
  }
}
