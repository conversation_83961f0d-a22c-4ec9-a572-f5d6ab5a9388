import crypto from 'node:crypto'
import { access, writeFile, rmdir, readFile, stat } from 'node:fs/promises'
import { mkdirSync } from 'node:fs'
import { CacheConfig, CacheStatus, Cache } from 'remix-image/server'

type DiskCacheConfig = CacheConfig & {
  path: string
}

function createHash(input: string) {
  const hash = crypto.createHash('sha256')
  hash.update(input)
  return hash.digest('hex')
}

export class DiskCache extends Cache {
  config: DiskCacheConfig
  constructor(config: Partial<DiskCacheConfig> | undefined = {}) {
    super()
    this.config = {
      path: config.path ?? '.cache/remix-image',
      ttl: config.ttl ?? 24 * 60 * 60,
      tbd: config.tbd ?? 365 * 24 * 60 * 60,
    }

    mkdirSync(this.config.path, { recursive: true })
  }
  has(key: string): Promise<boolean> {
    return access(`${this.config.path}/${createHash(key)}`)
      .then(() => true)
      .catch(() => false)
  }
  async status(key: string): Promise<CacheStatus> {
    const filePath = `${this.config.path}/${createHash(key)}`
    try {
      const stats = await stat(filePath)
      const now = Date.now()
      const fileAgeInSeconds = (now - stats.mtimeMs) / 1000

      if (fileAgeInSeconds > this.config.ttl) {
        return CacheStatus.STALE
      }
      return CacheStatus.HIT
    } catch {
      return CacheStatus.MISS
    }
  }
  get(key: string): Promise<Uint8Array | null> {
    return readFile(`${this.config.path}/${createHash(key)}`).catch(() => null)
  }
  set(key: string, resultImg: Uint8Array): Promise<void> {
    return writeFile(`${this.config.path}/${createHash(key)}`, resultImg)
  }
  clear(): Promise<void> {
    return rmdir(this.config.path, { recursive: true })
  }
}
