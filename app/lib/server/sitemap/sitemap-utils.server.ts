import type { Server<PERSON><PERSON> } from '@remix-run/server-runtime'
import type { SEOHandle, SitemapEntry } from '@/lib/server/sitemap/types'
import {
  ServerRoute,
  ServerRouteManifest,
} from '@remix-run/server-runtime/dist/routes'
import { ServerRouteModule } from '@remix-run/server-runtime/dist/routeModules'
import { removeTrailingSlash } from '@/utils/pathHelper'
import { STRAPI_ARTICLE, STRAPI_SERVICES } from '@/constants/http'

export type Options = {
  siteUrl: string
  articlesSlugs: string[]
  servicesSlugs: string[]
}

function typedBoolean<T>(
  value: T
): value is Exclude<T, '' | 0 | false | null | undefined> {
  return Boolean(value)
}
function getRouteName(path) {
  return path.split('/').filter(Boolean)[0]
}

async function getRawSitemapEntries(
  routes: ServerRouteManifest,
  request: Request,
  dynamicSlugs: Record<string, string[]>
) {
  const rawSitemapEntries = (
    await Promise.all(
      Object.entries(routes).map(([id, { module: mod }]) =>
        processRoute(id, mod, routes, request, dynamicSlugs)
      )
    )
  )
    .flatMap((entry) => entry)
    .filter(typedBoolean)

  return rawSitemapEntries
}

async function processRoute(
  id: string,
  mod: ServerRouteModule,
  routes: ServerRouteManifest,
  request: Request,
  dynamicSlugs: Record<string, string[]>
): Promise<SitemapEntry[] | undefined> {
  if (id === 'root') return

  const handle = mod.handle as SEOHandle | undefined

  if (handle?.getSitemapEntries) {
    const entries = await handle.getSitemapEntries(request)
    return entries?.filter(Boolean) as SitemapEntry[] | undefined
  }

  if (!('default' in mod)) return

  const path = resolveRoutePath(id, routes)
  if (!path) return

  if (path.includes(':')) {
    const routeName = getRouteName(path)
    if (routeName.includes(':')) return []
    const routeType = routeName === 'article' ? STRAPI_ARTICLE : STRAPI_SERVICES
    const slugs = dynamicSlugs[routeType] || []
    return slugs.map((slug) => ({
      route: removeTrailingSlash(path.replace(':slug', slug)),
    }))
  }

  return [{ route: removeTrailingSlash(path) }]
}

function resolveRoutePath(
  id: string,
  routes: ServerRouteManifest
): string | undefined {
  const manifestEntry = routes[id]

  const path = getInitialPath(manifestEntry)
  if (!path) return

  return buildFullPath(path, manifestEntry, routes)
}

function getInitialPath(
  manifestEntry: Omit<ServerRoute, 'children'>
): string | undefined {
  if (manifestEntry.path) {
    return removeTrailingSlash(manifestEntry.path)
  }
  if (manifestEntry.index) {
    return ''
  }
  return undefined
}

function buildFullPath(
  path: string,
  manifestEntry: Omit<ServerRoute, 'children'>,
  routes: ServerRouteManifest
): string {
  let parentId = manifestEntry.parentId
  let parent = parentId ? routes[parentId] : null

  while (parent) {
    const parentPath = parent.path ? removeTrailingSlash(parent.path) : ''
    path = `${parentPath}/${path}`
    parentId = parent.parentId
    parent = parentId ? routes[parentId] : null
  }

  return path
}

export const SitemapRoute = async (
  request: Request,
  routes: ServerBuild['routes'],
  options: Options
) => {
  const { siteUrl, servicesSlugs, articlesSlugs } = options

  const dynamicSlugs = {
    articles: articlesSlugs,
    services: servicesSlugs,
  }

  function getEntry({
    route,
    lastmod,
    changefreq,
    priority = 0.7,
  }: SitemapEntry) {
    return `
      <url>
        <loc>${siteUrl}${route}</loc>
        ${lastmod ? `<lastmod>${lastmod}</lastmod>` : ''}
        ${changefreq ? `<changefreq>${changefreq}</changefreq>` : ''}
        ${
          typeof priority === 'number' ? `<priority>${priority}</priority>` : ''
        }
      </url>
    `.trim()
  }

  const sitemapEntries: Array<SitemapEntry> = []

  if (!sitemapEntries.some((entry) => entry.route === '/')) {
    sitemapEntries.push({ route: '/' })
  }

  for (const entry of await getRawSitemapEntries(
    routes,
    request,
    dynamicSlugs
  )) {
    const existingEntryForRoute = sitemapEntries.find(
      (e) => e.route === entry.route
    )
    if (!existingEntryForRoute) {
      sitemapEntries.push(entry)
    }
  }

  const xmlContent = `
    <?xml version="1.0" encoding="UTF-8"?>
    <urlset
      xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd"
    >
      ${sitemapEntries.map((entry) => getEntry(entry)).join('')}
    </urlset>
  `.trim()

  return xmlContent
}
