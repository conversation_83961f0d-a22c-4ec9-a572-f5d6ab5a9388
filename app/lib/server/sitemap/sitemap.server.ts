import type { ServerBuild } from '@remix-run/server-runtime'

import { SitemapRoute } from '@/lib/server/sitemap/sitemap-utils.server'
import type { SEOOptions } from '@/lib/server/sitemap/types'

export async function generateSitemap(
  request: Request,
  routes: ServerBuild['routes'],
  options: SEOOptions
) {
  const { siteUrl, headers, servicesSlugs, articlesSlugs } = options
  const sitemap = await SitemapRoute(request, routes, {
    siteUrl,
    servicesSlugs,
    articlesSlugs,
  })
  const bytes = new TextEncoder().encode(sitemap).byteLength

  return new Response(sitemap, {
    headers: {
      ...headers,
      'Content-Type': 'application/xml',
      'Content-Length': String(bytes),
    },
  })
}
