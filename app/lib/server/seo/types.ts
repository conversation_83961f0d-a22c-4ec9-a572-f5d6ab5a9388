import { ImageType } from '@/lib/api/entity'
import { StrapiField } from '@/lib/api/types'

export interface Seo {
  id: number
  metaTitle: string | null
  metaDescription: string | null
  metaImage?: StrapiField<ImageType> | string | null
  metaSocial: MetaSocial[] | null
  keywords: string | null
  metaRobots: string | null
  structuredData: object | null
  metaViewport: string | null
  canonicalURL: string | null
}

export interface MetaSocial {
  id: number
  socialNetwork: string
  title: string | null
  description: string | null
  image: string | null
}

export type BuildTagsParams = Seo
