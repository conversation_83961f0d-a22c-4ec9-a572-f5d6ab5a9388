import { seoConfig } from '@/lib/brand/config'
import { Seo } from './types'

export const buildTags = (config: Seo | null | undefined) => {
  const tagsToRender: Record<string, string | undefined>[] = []

  const addTag = (
    type: string,
    key: string,
    content: string | undefined | null = null
  ) => {
    if (content) {
      tagsToRender.push({ [type]: key, content })
    }
  }

  tagsToRender.push({
    ['title']: `${seoConfig.metaTitle}${
      config?.metaTitle ? ` - ${config?.metaTitle}` : ''
    }`,
  })

  addTag('name', 'title', config?.metaTitle ?? seoConfig?.metaTitle)
  addTag('property', 'og:title', config?.metaTitle ?? seoConfig?.metaTitle)
  addTag(
    'name',
    'description',
    config?.metaDescription ?? seoConfig?.metaDescription
  )
  addTag(
    'property',
    'og:description',
    config?.metaDescription ?? seoConfig?.metaDescription
  )

  addTag('name', 'keywords', config?.keywords ?? seoConfig?.keywords)
  addTag(
    'name',
    'robots',
    config?.metaRobots
      ? `index, follow, ${config.metaRobots}`
      : seoConfig?.metaRobots
  )

  tagsToRender.push({
    tagName: 'link',
    rel: 'canonical',
    href: config?.canonicalURL ?? import.meta.env.VITE_HOST_URL,
  })
  addTag(
    'property',
    'og:image',
    typeof config?.metaImage === 'string'
      ? config?.metaImage
      : config?.metaImage?.data?.attributes?.url
  )

  config?.metaSocial?.forEach(
    ({ socialNetwork, title, description, image }) => {
      const network = socialNetwork.toLowerCase()
      addTag('property', `og:${network}:title`, title)
      addTag('property', `og:${network}:description`, description)
      addTag('property', `og:${network}:image`, image)
    }
  )

  addTag('name', 'viewport', config?.metaViewport)
  return tagsToRender
}
