import { MetaFunction, MetaDescriptor } from '@remix-run/node'

export const mergeMeta = (
  overrideFn: MetaFunction,
  appendFn?: MetaFunction
): MetaFunction => {
  return (args) => {
    let mergedMeta = args.matches.reduceRight((acc, match) => {
      if (match.meta.length > 0 && acc.length === 0) {
        return acc.concat(match.meta || [])
      }
      return acc
    }, [] as MetaDescriptor[])

    const overrides = overrideFn(args)
    overrides.forEach((override) => {
      const index = mergedMeta.findIndex(
        (meta) =>
          ('name' in meta &&
            'name' in override &&
            meta.name === override.name) ||
          ('property' in meta &&
            'property' in override &&
            meta.property === override.property)
      )
      if (index !== -1) {
        mergedMeta[index] = override
      } else {
        mergedMeta.push(override)
      }
    })

    if (appendFn) {
      mergedMeta = mergedMeta.concat(appendFn(args))
    }

    return mergedMeta
  }
}
