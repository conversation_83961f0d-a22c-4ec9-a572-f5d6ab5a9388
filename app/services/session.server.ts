import { createCookieSessionStorage } from '@remix-run/node'
import { createThemeSessionResolver } from 'remix-themes'

export const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: '_session',
    sameSite: 'lax',
    path: '/',
    httpOnly: true,
    secrets: [import.meta.env.VITE_SESSION_SECRET],
    secure: import.meta.env.PROD,
  },
})

export const { getSession, commitSession, destroySession } = sessionStorage

export const themeSessionResolver = createThemeSessionResolver(sessionStorage)
