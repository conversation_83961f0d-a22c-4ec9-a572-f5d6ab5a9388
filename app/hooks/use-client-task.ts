/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState, useCallback } from 'react'

type AsyncTask = (() => Promise<void>) | undefined

export default function useClientTasks(asyncTask: AsyncTask) {
  const [isClientRendered, setIsClientRendered] = useState<boolean>(false)
  const [isTaskPending, setIsTaskPending] = useState<boolean>(true)

  const executeTask = useCallback(async () => {
    try {
      if (asyncTask) {
        await asyncTask()
      }
    } finally {
      setIsTaskPending(false)
    }
  }, [asyncTask])

  useEffect(() => {
    setIsClientRendered(true)
    executeTask()
  }, [])

  return { isClientRendered, isTaskPending }
}
