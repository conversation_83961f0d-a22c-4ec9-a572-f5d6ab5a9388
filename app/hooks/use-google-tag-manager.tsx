import TagManager, { TagManagerArgs } from '@sooro-io/react-gtm-module'
import { useCallback, useEffect } from 'react'

declare global {
  interface Window {
    dataLayer: Array<{ event?: string }>
  }
}

export default function useGoogleTagManager(locale: string) {
  const isBrowser = typeof window !== 'undefined'

  const removeInjectedText = useCallback(() => {
    if (isBrowser) {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (
            mutation.type === 'childList' ||
            mutation.type === 'characterData'
          ) {
            document.body.childNodes.forEach((node) => {
              if (
                node.nodeType === Node.TEXT_NODE &&
                (node.nodeValue?.trim() === '$demo' ||
                  node.nodeValue?.trim() === 'demo' ||
                  node.nodeValue?.trim() === '$')
              ) {
                node.nodeValue = ''
              }
            })
          }
        })
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true,
      })

      document.body.childNodes.forEach((node) => {
        if (
          node.nodeType === Node.TEXT_NODE &&
          (node.nodeValue?.trim() === 'demo' ||
            node.nodeValue?.trim() === '$demo' ||
            node.nodeValue?.trim() === '$')
        ) {
          node.nodeValue = ''
        }
      })
    }
  }, [isBrowser])

  const removeDemoFromDataLayer = useCallback(() => {
    if (isBrowser && window.dataLayer?.length) {
      window.dataLayer = window.dataLayer.filter(
        (item) => item.event !== 'demo'
      )
    }
  }, [isBrowser])

  useEffect(() => {
    if (isBrowser) {
      removeDemoFromDataLayer()

      if (import.meta.env.VITE_GTM) {
        const tagManagerArgs: TagManagerArgs = {
          gtmId: import.meta.env.VITE_GTM,
          dataLayer: { userLocale: locale },
          nonce:
            document
              .querySelector('meta[name="csp-nonce"]')
              ?.getAttribute('content') || undefined,
        }

        requestAnimationFrame(() => {
          TagManager.initialize(tagManagerArgs)

          setTimeout(() => {
            TagManager.dataLayer({
              dataLayer: {
                event: 'page_view',
                pagePath: window.location.pathname,
              },
            })

            removeDemoFromDataLayer()
          }, 500)
        })
      }
    }
  }, [isBrowser, locale, removeDemoFromDataLayer])

  useEffect(() => {
    removeInjectedText()
  }, [removeInjectedText])
}
