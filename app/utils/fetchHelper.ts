import { strapiGetPage } from '@/lib/api/strapi/page'
import { ERROR_CODE_404 } from '@/constants/error'
import { ArticleEntity, PageEntity, ServiceEntity } from '@/lib/api/entity'
import { checkEnvVars } from '@/utils/errorHandling'
import { strapiGetServices } from '@/lib/api/strapi/service'
import { strapiGetArticles } from '@/lib/api/strapi/article'
import qs from 'qs'
import { PaginationQuery } from '@/lib/api/types'
import { STRAPI_ARTICLE } from '@/constants/http'

type FilterParams = {
  [key: string]:
    | string
    | number
    | boolean
    | object
    | Array<object>
    | FilterParams
    | { [key: string]: object }
}

export const queryStringBuilder = (
  populate: string[],
  filters?: FilterParams,
  sort?: string[],
  pagination?: PaginationQuery
) =>
  qs.stringify(
    {
      ...(pagination && { pagination }),
      populate,
      ...(filters && { filters }),
      ...(sort && { sort }),
    },
    {
      encodeValuesOnly: true,
    }
  )

export async function fetchPageBySlug(
  slug: string | undefined,
  lng: string,
  param = ''
): Promise<PageEntity> {
  checkEnvVars()

  const response = await strapiGetPage(
    `filters[slug][$eq]=${slug}&${param}`,
    lng
  )

  if (!response?.data?.length || !response.data[0].attributes) {
    throw new Response('Page Not Found', { status: ERROR_CODE_404 })
  }

  return response.data[0].attributes
}

export async function fetchServiceBySlug(
  slug: string,
  lng: string,
  param = ''
): Promise<ServiceEntity> {
  checkEnvVars()

  const response = await strapiGetServices(
    `filters[slug][$eq]=${slug}&${param}`,
    lng
  )
  if (!response?.data?.length || !response.data[0]?.attributes) {
    throw new Response('Service not found', { status: ERROR_CODE_404 })
  }

  return response.data[0].attributes
}

export async function fetchArticleBySlug(
  slug: string | undefined,
  lng: string,
  param = ''
): Promise<ArticleEntity> {
  checkEnvVars()

  const response = await strapiGetArticles(
    `filters[slug][$eq]=${slug}&${param}`,
    lng
  )

  if (!response?.data?.length || !response.data[0].attributes) {
    throw new Response('Articles Not Found', { status: ERROR_CODE_404 })
  }

  return response.data[0].attributes
}

export async function fetchDynamicSlugs(
  path: string | undefined,
  lng: string
): Promise<string[]> {
  checkEnvVars()

  const fetchData =
    path === STRAPI_ARTICLE ? strapiGetArticles : strapiGetServices

  const response = await fetchData(`fields[0]=slug`, lng)

  if (!response?.data) {
    throw new Response('Failed to fetch slugs for dynamic routes', {
      status: ERROR_CODE_404,
    })
  }

  return response.data
    .map((item) => item.attributes?.slug)
    .filter((slug): slug is string => Boolean(slug))
}
