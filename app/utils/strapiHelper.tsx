import { StrapiSingleType, StrapiCollectionType } from '@/lib/api/types'

export function warperSingleType<T>(response: StrapiSingleType<T>): T | null {
  return response.data?.attributes ?? null
}

export function warperCollectionType<T>(
  response: StrapiCollectionType<T>
): Array<T> | null {
  return (
    response.data?.map((item) =>
      item.attributes ? { id: String(item.id), ...item.attributes } : ({} as T)
    ) ?? null
  )
}

export function warperCollectionTypePagination<T>(
  response: StrapiCollectionType<T>
): {
  data: Array<T> | null
  pagination:
    | {
        page: number
        pageSize: number
        pageCount: number
        total: number
      }
    | undefined
} {
  const data =
    response.data?.map((item) =>
      item.attributes ? { id: String(item.id), ...item.attributes } : ({} as T)
    ) ?? null
  return {
    data,
    pagination: response.meta?.pagination,
  }
}
