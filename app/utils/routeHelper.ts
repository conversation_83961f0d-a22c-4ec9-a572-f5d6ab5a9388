import { i18nConfig } from '@/lib/server/translations/i18n.config'

type RouteData = {
  url: URL
  path: string
  slug: string
  lng: string
}

export const defineRoute = (request: Request): RouteData => {
  const url = new URL(request.url)
  const path = url.pathname
  const slug = path.split('/').filter(Boolean).pop()
  const lng = url.searchParams.get('lng') ?? i18nConfig.fallbackLng

  return { url, path, slug, lng } as RouteData
}

export const withCors = (value?: Headers) => {
  const headers = value ?? new Headers()

  headers.set('Access-Control-Allow-Origin', '*')
  headers.set(
    'Access-Control-Allow-Headers',
    'Origin, X-Requested-With, Content-Type, Accept, referer-path'
  )

  return headers
}
