class APIResponseError extends Error {
  constructor(response) {
    super(`API Error Response: ${response.status} ${response.statusText}`)
  }
}

export const checkStatus = (response) => {
  if (response.ok) {
    return response
  } else {
    throw new APIResponseError(response)
  }
}

class MissingEnvironmentVariable extends Error {
  constructor(name) {
    super(
      `Missing Environment Variable: The ${name} environment variable must be defined`
    )
  }
}

export const checkEnvVars = () => {
  const requiredEnvVars = {
    VITE_STRAPI_URL: import.meta.env.VITE_STRAPI_URL,
    VITE_STRAPI_API_TOKEN: import.meta.env.VITE_STRAPI_API_TOKEN,
  }

  for (const [key, value] of Object.entries(requiredEnvVars)) {
    if (!value) {
      throw new MissingEnvironmentVariable(key)
    }
  }
}
