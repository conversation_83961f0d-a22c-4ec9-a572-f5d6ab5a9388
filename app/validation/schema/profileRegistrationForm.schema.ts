import * as v from 'valibot'
import {
  requiredString,
  requiredEmail,
  recordField,
  requiredNumber,
  requiredMobileNumber,
} from '@/validation/validators'

export type ProfileRegistrationFormType = v.InferOutput<
  ReturnType<typeof ProfileRegistrationFormSchema>
>

export const ProfileRegistrationFormSchema = (t: (key: string) => string) =>
  v.object({
    email: requiredEmail(t),
    message: v.optional(v.string()),
    firstName: requiredString(t, 'firstName'),
    lastName: requiredString(t, 'lastName'),
    nickName: requiredString(t, 'nickName'),
    gender: requiredString(t, 'gender'),
    age: requiredNumber(t, 'age'),
    birthDate: v.pipe(v.date(), v.toMaxValue(new Date())),
    address: requiredString(t, 'address'),
    mobileNo: requiredMobileNumber(t),
    usedServices: recordField(t, 'usedService'),
    wantedServices: recordField(t, 'wantedService'),
    sources: recordField(t, 'source'),
    reasons: recordField(t, 'reason'),
  })
