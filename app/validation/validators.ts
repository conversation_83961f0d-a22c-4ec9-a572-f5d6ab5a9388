import * as v from 'valibot'

export const requiredString = (t: (key: string) => string, field: string) =>
  v.pipe(v.string(), v.nonEmpty(t(`form.${field}.validate.required`)))

export const requiredEmail = (t: (key: string) => string) =>
  v.pipe(
    v.string(),
    v.nonEmpty(t('form.email.validate.required')),
    v.email(t('form.email.validate.invalid'))
  )

export const requiredNumber = (t: (key: string) => string, field: string) =>
  v.pipe(
    v.string(),
    v.nonEmpty(t(`form.${field}.validate.required`)),
    v.transform((value) => Number(value)),
    v.number(t(`form.${field}.validate.invalid`))
  )

export const requiredMobileNumber = (t: (key: string) => string) =>
  v.pipe(
    v.string(),
    v.nonEmpty(t('form.mobileNo.validate.required')),
    v.transform((value) => value.replace(/\D/g, '')),
    v.minLength(10, t('form.mobileNo.validate.length')),
    v.maxLength(10, t('form.mobileNo.validate.length'))
  )

export const recordField = (t: (key: string) => string, field: string) =>
  v.pipe(
    v.record(v.string(), v.boolean(), t(`form.${field}.validate.required`)),
    v.rawCheck(({ dataset, addIssue }) => {
      if (
        !Object.values(dataset.value as Record<string, boolean>).find(
          (item) => !!item
        )
      ) {
        addIssue({
          message: t(`form.${field}.validate.required`),
        })
      }
    })
  )
