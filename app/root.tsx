import {
  type LinksFunction,
  type LoaderFunction,
  type MetaFunction,
} from '@remix-run/node'
import {
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
} from '@remix-run/react'
import {
  PreventFlashOnWrongTheme,
  ThemeProvider,
  useTheme,
  type Theme,
} from 'remix-themes'
import 'remixicon/fonts/remixicon.css'
import 'remix-image/remix-image.css'
import 'react-google-reviews/dist/index.css'
import { csrf } from '@/lib/server/csrf.server'
import { buildTags } from '@/lib/server/seo/seo-utils'
import { themeSessionResolver } from '@/services/session.server'
import stylesheet from '@/tailwind.css?url'
import { cn } from '@/lib/utils'
import { AuthenticityTokenProvider } from 'remix-utils/csrf/react'
import { useChangeLanguage } from 'remix-i18next/react'
import { useTranslation } from 'react-i18next'
import { i18next } from '@/lib/server/translations/i18next.server'
import { Suspense, useEffect, useMemo } from 'react'
import { THEME_ACTION } from '@/constants/theme'
import { ErrorBoundary } from '@/components/error/errorBoundary'
import { HeartBeatLoader } from './components/heart-beat-loader'
import StickyLineButton from '@/components/button/stickyLineButton'
import { strapiMainMenu } from '@/lib/api/strapi/mainMenu'
import { HomeEntity, MainMenuEntity } from '@/lib/api/entity'
import { warperSingleType } from '@/utils/strapiHelper'
import { seoConfig } from '@/lib/brand/config'
import { useIsBot } from '@/providers/is-bot'
import { AppContext } from './providers/appContext'
import { queryStringBuilder } from './utils/fetchHelper'
import crypto from 'crypto'
import Navbar from './components/navigationMenu'
import Footer from './components/footer'
import TagManager from '@sooro-io/react-gtm-module'
import { strapiGetHome } from './lib/api/strapi/home'
import useGoogleTagManager from './hooks/use-google-tag-manager'
import { HelmetProvider } from 'react-helmet-async'
import { withCors } from './utils/routeHelper'

type RootMetaData = {
  locale: string
  token: string
  theme: Theme | null
  dataMainMenu: MainMenuEntity | null
  dataHome: HomeEntity | null
}

export const handle = {
  i18n: [
    'common',
    'navbar',
    'error',
    'article',
    'contact-us',
    'home',
    'promotion',
    'review',
    'footer',
    'profile-registration',
  ],
}

export const links: LinksFunction = () => [
  { rel: 'preload', href: stylesheet, as: 'style' },
  { rel: 'stylesheet', href: stylesheet },
  // Century Gothic
  {
    rel: 'preload',
    href: '/fonts/century-gothic/CenturyGothic.woff2',
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'preload',
    href: '/fonts/century-gothic/CenturyGothic-Bold.woff2',
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous',
  },
  // Kanit
  {
    rel: 'preload',
    href: '/fonts/kanit/Kanit-Italic.woff2',
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous',
  },
  // Sukhumvit Set
  {
    rel: 'preload',
    href: '/fonts/sukhumvit-set/SukhumvitSet-Bold.woff2',
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'preload',
    href: '/fonts/sukhumvit-set/SukhumvitSet-Light.woff2',
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'preload',
    href: '/fonts/sukhumvit-set/SukhumvitSet-Medium.woff2',
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'preload',
    href: '/fonts/sukhumvit-set/SukhumvitSet-SemiBold.woff2',
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'preload',
    href: '/fonts/sukhumvit-set/SukhumvitSet-Text.woff2',
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'preload',
    href: '/fonts/sukhumvit-set/SukhumvitSet-Thin.woff2',
    as: 'font',
    type: 'font/woff2',
    crossOrigin: 'anonymous',
  },
]

export const meta: MetaFunction = () => buildTags(seoConfig)

export const loader: LoaderFunction = async ({ request }) => {
  withCors()
  const locale = await i18next.getLocale(request)

  const [themeSession, csrfResult] = await Promise.allSettled([
    themeSessionResolver(request),
    csrf.commitToken(),
  ])

  const [mainMenuResponse, homeResponse] = await Promise.all([
    strapiMainMenu(
      queryStringBuilder([
        'mainMenuItems',
        'mainMenuItems.subMenuLink',
        'mainMenuItems.subMenuLink.image',
      ]),
      locale
    ),
    strapiGetHome(queryStringBuilder(['contactButton']), locale),
  ])

  const getTheme =
    themeSession.status === 'fulfilled' ? themeSession.value.getTheme : null
  const token = csrfResult.status === 'fulfilled' ? csrfResult.value[0] : null
  const responseMainMenu = warperSingleType(mainMenuResponse)
  const responseHome = warperSingleType(homeResponse)

  const etag = crypto
    .createHash('sha256')
    .update(JSON.stringify(responseMainMenu))
    .digest('hex')

  const ifNoneMatch = request.headers.get('If-None-Match')
  if (ifNoneMatch === etag) {
    return new Response(null, {
      status: 304,
      headers: {
        'Cache-Control': 'public, max-age=3600, s-maxage=3600',
        Vary: 'Accept-Language',
      },
    })
  }

  const headers: HeadersInit = {
    'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    ETag: etag,
    Vary: 'Accept-Language',
  }

  if (csrfResult.status === 'fulfilled' && csrfResult.value[1]) {
    headers['Set-Cookie'] = [
      ...(headers['Set-Cookie'] ? [headers['Set-Cookie']] : []),
      csrfResult.value[1],
    ].join(', ')
  }

  return createJsonResponse(
    {
      locale,
      token,
      theme: getTheme?.(),
      dataMainMenu: responseMainMenu,
      dataHome: responseHome,
    },
    headers
  )
}

interface AppProps {
  locale: string
  token: string
  dataMainMenu: MainMenuEntity | null
  dataHome: HomeEntity | null
}

const createJsonResponse = (
  data: Record<string, string | null | undefined | object>,
  headers = {}
) => {
  return new Response(JSON.stringify(data), {
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  })
}

const Layout = ({ children }: { children: React.ReactNode }) => (
  <div className="min-h-screen w-full overflow-x-clip flex flex-col">
    <Navbar />
    <main className="flex-1">{children}</main>
    <Footer />
    <StickyLineButton />
  </div>
)

const App = ({ locale, token, dataMainMenu, dataHome }: AppProps) => {
  const { i18n } = useTranslation()
  const [theme] = useTheme()
  const isBot = useIsBot()
  const appValue = useMemo(() => {
    if (dataMainMenu?.mainMenuItems) {
      dataMainMenu.mainMenuItems.forEach((item) => {
        if (item.url === 'home') item.url = ''
      })
    }
    return {
      mainMenu: dataMainMenu,
      home: dataHome,
    }
  }, [dataMainMenu, dataHome])

  const metaGTV = import.meta.env.VITE_GSV ? (
    <meta name="google-site-verification" content={import.meta.env.VITE_GSV} />
  ) : null

  useChangeLanguage(locale)
  useGoogleTagManager(locale)

  useEffect(() => {
    if (import.meta.env.VITE_GTM) {
      TagManager.initialize({ gtmId: import.meta.env.VITE_GTM })
    }
  }, [])

  return (
    <html lang={locale} dir={i18n.dir()} className={cn('h-full light')}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {metaGTV}
        <Meta />
        <Links />
        <PreventFlashOnWrongTheme ssrTheme={Boolean(theme)} />
      </head>
      <body className="min-h-screen w-full overflow-x-clip">
        <AuthenticityTokenProvider token={token}>
          <Suspense fallback={<HeartBeatLoader />}>
            <AppContext.Provider value={appValue}>
              <Layout>
                <Outlet />
              </Layout>
            </AppContext.Provider>
          </Suspense>
        </AuthenticityTokenProvider>
        <ScrollRestoration />
        {!isBot && <Scripts />}
      </body>
    </html>
  )
}

const AppWithProviders = () => {
  const { theme, locale, token, dataMainMenu, dataHome } =
    useLoaderData<RootMetaData>()

  return (
    <ThemeProvider specifiedTheme={theme} themeAction={THEME_ACTION}>
      <HelmetProvider>
        <App
          dataMainMenu={dataMainMenu}
          dataHome={dataHome}
          locale={locale}
          token={token}
        />
      </HelmetProvider>
    </ThemeProvider>
  )
}

export { ErrorBoundary }

export default AppWithProviders
