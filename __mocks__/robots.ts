import { RobotsConfig, RobotsPolicy } from '@/lib/server/robots/types'

export const validateRobotsPolicy = (policy: RobotsPolicy): boolean => {
  const allowedTypes = [
    'allow',
    'disallow',
    'sitemap',
    'crawlDelay',
    'userAgent',
  ]
  return allowedTypes.includes(policy.type) && typeof policy.value === 'string'
}

export const createRobotsConfig = (config: RobotsConfig): RobotsConfig => {
  return {
    appendOnDefaultPolicies: config.appendOnDefaultPolicies ?? true,
    headers: config.headers ?? {},
  }
}
