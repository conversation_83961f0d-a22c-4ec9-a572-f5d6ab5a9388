import { Options } from '@/lib/server/sitemap/sitemap-utils.server'
import { SEOHandle } from '@/lib/server/sitemap/types'
import { ServerBuild } from '@remix-run/node'

export const mockRoutes: ServerBuild['routes'] = {
  root: {
    path: '/',
    module: {
      default: undefined,
    },
    parentId: undefined,
    id: '',
  },
  review: {
    path: 'review',
    module: {
      default: 'default',
      handle: {
        getSitemapEntries: async () => [
          { route: '/contact', lastmod: '2023-12-19', changefreq: 'weekly' },
          { route: '/services', priority: 0.7 },
        ],
      } as SEOHandle,
    },
    parentId: undefined,
    id: '',
  },
  test: {
    index: true,
    path: undefined,
    module: {
      default: 'default',
    },
    parentId: undefined,
    id: '',
  },
  test_: {
    index: false,
    path: undefined,
    module: {
      default: undefined,
    },
    parentId: undefined,
    id: '',
  },
  article: {
    path: 'article/:slug',
    module: {
      default: undefined,
    },
    parentId: 'root',
    id: '',
  },
  service: {
    path: 'our-service/:slug',
    module: {
      default: undefined,
    },
    parentId: 'root',
    id: '',
  },
}

export const mockRequest: Request = new Request('https://example.com')

export const mockOptions: Options = {
  siteUrl: 'https://example.com',
  articlesSlugs: ['article-1', 'article-2'],
  servicesSlugs: ['service-1', 'service-2'],
}
