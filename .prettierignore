
# Generally, ignore all in node_modules
.vscode/
package-lock.json
bun.lockb
node_modules/
.DS_Store
/.cache


# Ignore build output directories
/build
/server-build
/public/build
/coverage

# Ignore specific static files which don't need formatting
public/**/*.html
public/**/*.min.css
public/**/*.min.js

# Ignore configuration files
*.config.js

# Ignore markdown files within the docs folder
/docs/**/*.md

# Ignore files with minified names typically not needing formatting
*.min.js
*.min.css

# Ignore all log files
*.log
