# Dependency directories
node_modules/
/vendor/

# Production builds
/.scannerwork
/.cache
/build/
/dist/
/out/

# Logs and databases
*.log
*.sql
*.sqlite

# Operating system generated files
.DS_Store    # macOS specific
Thumbs.db    # Windows specific
ehthumbs.db  # Windows specific
.dbshell     # MongoDB specific

# IDE/Editor directories
.idea/
*.sublime-project
*.sublime-workspace

# Environment files
.env
.env.local
.env.production
.env.development

# Security files
.secret

# Build outputs
/public/build/

# Testing
/coverage/
/nyc_output/
/cypress/
/reports/

# Bun and other package managers
bun.lockb
bun.lock
package-lock.json
yarn.lock

# Debug logs from npm, yarn, etc.
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# User-specific files
*.swp
*.swo

# TypeScript compiled files
/tsconfig.tsbuildinfo
/dist-tsc/

# Storybook files
.storybook-out/

# Compiled CSS files
*.css.map
*.css

# Sentry Config File
.env.sentry-build-plugin
