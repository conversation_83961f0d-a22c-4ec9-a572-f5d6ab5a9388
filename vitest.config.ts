/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import tsconfigPaths from 'vite-tsconfig-paths'

export default defineConfig(() => ({
  plugins: [react(), tsconfigPaths()],
  resolve: {
    alias: {
      '~': path.resolve(__dirname, 'app'),
      '@': path.resolve(__dirname, 'app'),
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './vitest.setup.ts',
    coverage: {
      //provider: 'v8',
      reporter: ['text', 'html', 'lcov', 'json-summary', 'json'],
      include: ['app/**/*.{ts,tsx}'],
      enabled: true,
      exclude: [
        '.cache',
        '.scannerwork',
        '.vscode',
        '.github',
        '.husky',
        'build',
        'coverage',
        'public',
        'node_modules',
        '**/*.d.ts',
      ],
    },
  },
}))
